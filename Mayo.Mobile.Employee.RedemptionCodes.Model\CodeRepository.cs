﻿//-----------------------------------------------------------------------
// <copyright file="CodeRepository.cs" company="Mayo Foundation">
//     Copyright © Mayo Foundation. All rights reserved.
// </copyright>
//-----------------------------------------------------------------------

namespace Mayo.Mobile.Employee.RedemptionCodes.Model
{
    using Mayo.Mobile.Application.Azure.DataAccess.Interfaces;
    using Microsoft.Azure.Cosmos.Table;
    using System;
    using System.Collections.Generic;
    using System.Linq;
    using System.Text;
    using System.Threading.Tasks;

    /// <summary>
    /// Initializes a new instance of the CodeRepository class.
    /// </summary>
    public class CodeRepository : ICodeRepository
    {
        /// <summary>
        /// The <see cref="StorageRepository"/>
        /// </summary>
        private ITableStorageRepository TableStorageRepo = null;

        /// <summary>
        /// Initializes a new instance of the CodeRepository class.
        /// </summary>
        /// <param name="connectionString">The connections string</param>
        public CodeRepository(ITableStorageRepository tableStorageRepository)
        {
            TableStorageRepo = tableStorageRepository;
        }

        /// <summary>
        /// Get the list of users
        /// </summary>
        /// <returns>The <see cref="Task{TResult}"/></returns>
        public async Task<List<(string UserId, string code, DateTimeOffset date)>> GetUsersAsync()
        {
            var list = await TableStorageRepo.GetEntitiesAsync("RedemptionCodeStorage", "USER");
            return list.Select(x => (x.RowKey, x.Properties["Code"].StringValue, x.Timestamp)).ToList();
        }

        /// <summary>
        /// Get the code for a user if they have one
        /// </summary>
        /// <param name="userId">The user identifier</param>
        /// <returns>The <see cref="Task{TResult}"/></returns>
        public async Task<string> GetCodeAsync(string userId)
        {
            var e = await TableStorageRepo.GetEntityAsync("RedemptionCodeStorage", "USER", userId);
            return (e == null)
                ? string.Empty
                : e.Properties["Code"].StringValue;
        }

        /// <summary>
        /// Get the number of codes
        /// </summary>
        /// <returns>The <see cref="Task{TResult}"/></returns>
        public async Task<Tuple<int, int>> GetCodeCountsAsync()
        {
            var available = await TableStorageRepo.GetEntitiesAsync("RedemptionCodeStorage", "AVAILABLECODES");
            var redeemed = await TableStorageRepo.GetEntitiesAsync("RedemptionCodeStorage", "CODE");

            return Tuple.Create(redeemed.Count, available.Count);
        }

        /// <summary>
        /// Get the url for the code
        /// </summary>
        /// <param name="code">The redemption code</param>
        /// <returns>The <see cref="Task{TResult}"/></returns>
        public async Task<(string Url, string UserId)> GetUrlAsync(string code)
        {
            var e = await TableStorageRepo.GetEntityAsync("RedemptionCodeStorage", "CODE", code);
            return (e == null)
                ? (string.Empty, string.Empty)
                : (e.Properties["Url"].StringValue, e.Properties["UserId"] != null ? e.Properties["UserId"].StringValue : string.Empty);
        }


        /// <summary>
        /// Check to determine if the code can be saved
        /// </summary>
        /// <param name="code">The redemption code</param>
        /// <param name="url">The redemption code url</param>
        /// <returns>The <see cref="Task{TResult}"/></returns>
        public async Task<bool> CanSaveCodesAsync(string code, string url = null)
        {
            var a = await TableStorageRepo.GetEntityAsync("RedemptionCodeStorage", "AVAILABLECODES", code);

            //// code has been saved
            if (a != null)
            {
                return false;
            }
            else
            {
                var e = await TableStorageRepo.GetEntityAsync("RedemptionCodeStorage", "CODE", code);
                if (e == null)
                {
                    //// code has not been saved
                    return true;
                }
                else
                {
                    //// code has been saved and claimed
                    return false;
                }
            }
        }

        /// <summary>
        /// Save the code 
        /// </summary>
        /// <param name="code">The code value</param>
        /// <param name="url">The url value</param>
        /// <returns>The <see cref="Task{TResult}"/></returns>
        public async Task<bool> SaveAsync(string code, string url)
        {
            if (await CanSaveCodesAsync(code, url) == true)
            {
                var a = new DynamicTableEntity(
                    "AVAILABLECODES",
                    code,
                    "*",
                    new Dictionary<string, EntityProperty>
                    {
                        {
                            "Url", new EntityProperty(url)
                        }
                    });

                await TableStorageRepo.SaveAsync("RedemptionCodeStorage", a);

                var c = new DynamicTableEntity(
                    "CODE",
                    code,
                    "*",
                    new Dictionary<string, EntityProperty>
                    {
                        {
                            "Url", new EntityProperty(url)
                        }
                    });

                await TableStorageRepo.SaveAsync("RedemptionCodeStorage", c);

                return true;
            }
            else
            {
                return false;
            }
        }

        /// <summary>
        /// Get the code for a user if they do not have one
        /// </summary>
        /// <param name="userId">The user identifier</param>
        /// <returns>The <see cref="Task{TResult}"/></returns>
        public async Task<string> RedeemCodeAsync(string userId)
        {
            //// find available code
            DynamicTableEntity code = null;
            try
            {
                var entities = await TableStorageRepo.GetEntitiesAsync("RedemptionCodeStorage", "AVAILABLECODES");
                code = entities.First();
            }
            catch
            {
            }

            if (code == null)
            {
                return "UNAVAILABLE";
            }
            else
            {
                //// remove the code from the available codes
                await TableStorageRepo.DeleteAsync("RedemptionCodeStorage", code);

                //// save the code to the user
                var e = new DynamicTableEntity(
                    "USER",
                    userId,
                    "*",
                    new Dictionary<string, EntityProperty>
                    {
                        {
                            "Code", new EntityProperty(code.RowKey) 
                        }
                    });

                await TableStorageRepo.SaveAsync("RedemptionCodeStorage", e);

                //// update the code with the user
                var c = new DynamicTableEntity(
                    "CODE",
                    code.RowKey,
                    "*",
                    new Dictionary<string, EntityProperty>
                    {
                        {
                            "UserId", new EntityProperty(userId)
                        }
                    });

                await TableStorageRepo.SaveAsync("RedemptionCodeStorage", c);

                return code.RowKey;
            }
        }
    }
}
