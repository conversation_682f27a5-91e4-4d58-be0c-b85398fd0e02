﻿//-----------------------------------------------------------------------
// <copyright file="PagingResponse.cs" company="Mayo Foundation">
//     Copyright © Mayo Foundation. All rights reserved.
// </copyright>
//-----------------------------------------------------------------------

namespace Mayo.Mobile.Employee.Web.Api.Model
{
	using System;
	using System.Collections.Generic;
	using System.Runtime.Serialization;
	using System.Text;

	/// <summary>
	/// Initializes a new instance of the PagingResponse class.
	/// </summary>
	[DataContract(Namespace = "http://www.mayo.edu/MayoMobile")]
	public class PagingResponse<T>
	{
		/// <summary>
		/// Initializes a new instance of the Response class.
		/// </summary>
		public PagingResponse(List<T> list)
		{
			this.List = list;
		}

		/// <summary>
		/// Gets or sets the <see cref="List{T}"/>
		/// </summary>
		[DataMember]
		public List<T> List { get; set; }

		/// <summary>
		/// Handle Serialization
		/// </summary>
		/// <param name="context">The <see cref="StreamingContext"/></param>
		[OnSerializing]
		private void OnSerialized(StreamingContext context)
		{
			this.List = this.List ?? new List<T>();
		}
	}
}
