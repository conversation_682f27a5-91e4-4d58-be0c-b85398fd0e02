﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <OutputType>Exe</OutputType>
    <TargetFramework>netcoreapp3.1</TargetFramework>
  </PropertyGroup>

  <PropertyGroup>
    <LangVersion>latest</LangVersion>
  </PropertyGroup>

  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|AnyCPU'">
    <PlatformTarget>AnyCPU</PlatformTarget>
  </PropertyGroup>

  <ItemGroup>
    <None Remove="appsettings.json" />
  </ItemGroup>

  <ItemGroup>
    <Content Include="appsettings.json">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
  </ItemGroup>
  
  <ItemGroup>
    <PackageReference Include="DocumentFormat.OpenXml" Version="2.12.3" />
    <PackageReference Include="Microsoft.Extensions.Configuration" Version="3.1.17" />
    <PackageReference Include="Microsoft.Extensions.Configuration.FileExtensions" Version="3.1.17" />
    <PackageReference Include="Microsoft.Extensions.Configuration.Json" Version="3.1.17" />
    <PackageReference Include="Novell.Directory.Ldap.NETStandard2_0" Version="3.1.0" />
    <PackageReference Include="System.DirectoryServices" Version="4.7.0" />
    <PackageReference Include="System.DirectoryServices.AccountManagement" Version="4.7.0" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\Mayo.Mobile.Employee.Location.Model\Mayo.Mobile.Employee.Location.Model.csproj" />
    <ProjectReference Include="..\Mayo.Mobile.Employee.RedemptionCodes.Model\Mayo.Mobile.Employee.RedemptionCodes.Model.csproj" />
  </ItemGroup>

</Project>
