﻿//-----------------------------------------------------------------------
// <copyright file="PagerRequest.cs" company="Mayo Foundation">
//     Copyright © Mayo Foundation. All rights reserved.
// </copyright>
//-----------------------------------------------------------------------

namespace Mayo.Mobile.Employee.Web.Api.Model
{
    using System;
    using System.Collections.Generic;
    using System.Runtime.Serialization;
    using System.Text;

    /// <summary>
    /// Initializes a new instance of the PagerRequest class
    /// </summary>
    [DataContract(Namespace = "http://www.mayo.edu/MayoMobile")]
    public class PagerRequest : Request
    {
        /// <summary>
        /// Gets or sets the pager number
        /// </summary>
        [DataMember]
        public string PagerNumber { get; set; }

        /// <summary>
        /// Gets or sets the entity
        /// </summary>
        [DataMember]
        public string Entity { get; set; }

        /// <summary>
        /// Gets or sets the message
        /// </summary>
        [DataMember]
        public string Message { get; set; }
    }
}
