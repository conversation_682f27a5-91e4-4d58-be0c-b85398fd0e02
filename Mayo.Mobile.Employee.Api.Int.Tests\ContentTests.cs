using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Configuration;
using System.Linq;
using System.Net.Http;
using System.ServiceModel;
using System.Web.Http;
using Mayo.Mobile.Application.Azure.DataAccess.Interfaces;
using Mayo.Mobile.Application.Azure.DataAccess.Models;
using Mayo.Mobile.Application.Azure.DataAccess.Repositories;
using Mayo.Mobile.Employee.Azure.Search.Model;
using Mayo.Mobile.Employee.Content.Model;
using Mayo.Mobile.Employee.Content.News.Model;
using Mayo.Mobile.Employee.Content.News.Model.Models;
using Mayo.Mobile.Employee.Person.Model;
using Mayo.Mobile.Employee.Search.Model;
using Mayo.Mobile.Employee.Web.Api.Controllers;
using Mayo.Mobile.Employee.Web.Api.Model;
using Mayo.Mobile.Platform.Staff.Client;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Options;
using Microsoft.VisualStudio.TestTools.UnitTesting;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;

namespace Mayo.Mobile.Employee.Api.Int.Tests
{
    [TestClass]
    public class ContentTests
    {
        private readonly string accessToken = "Bearer eyJhbGciOiJodHRwOi8vd3d3LnczLm9yZy8yMDAxLzA0L3htbGRzaWctbW9yZSNobWFjLXNoYTI1NiIsInR5cCI6IkpXVCJ9.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.xOq-edQV7OumzUJNh0xiiMUJK1surLz-_BNUzM1Rh78";
        private readonly string userId = "**********";

        private IContentService ContentService;
        private NewsService NewsService;
        private MockContentService mockService;
        private ISearchService searchService;

        [TestInitialize]
        public void Init()
        {
            var config = new ConfigurationBuilder().AddJsonFile("appsettings.json").Build();
            var saRepoOptions = Options.Create<StorageAccountRepoOptions>(new StorageAccountRepoOptions() { ConnectionString = config.GetConnectionString("StorageAccount") });
            ITableStorageRepository tableStorageRepo = new TableStorageRepository(saRepoOptions);
            IBlobStorageRepository blobStorageRepo = new BlobStorageRepository(saRepoOptions);
            IQueueStorageRepository queueStorageRepo = new QueueStorageRepository(saRepoOptions);

            var newsClient = new HttpClient();
            var httpBinding = new BasicHttpBinding(BasicHttpSecurityMode.Transport)
            {
                MaxReceivedMessageSize = int.MaxValue,
                MaxBufferSize = int.MaxValue,
                TransferMode = TransferMode.Streamed,
                MaxBufferPoolSize = int.MaxValue,
                SendTimeout = TimeSpan.FromSeconds(240),
                ReceiveTimeout = TimeSpan.FromSeconds(240),
                ReaderQuotas = System.Xml.XmlDictionaryReaderQuotas.Max
            };

            var platformStaffServiceUrl = "https://roemmc801w.mayo.edu/mobile/staff/staffservice.svc/soap";
            //services.PostConfigure<PlatformStaffServiceOptions>(config =>
            //{
            //    config.ApplicationId = "Employee";
            //    config.EndpointAddress = platformStaffServiceUrl;
            //    config.HttpBinding = httpBinding;
            //});
            //IPersonService personSvc = new PersonService();

            IContentStorageRepository contentStorageRepository = new ContentStorageRepository(tableStorageRepo, blobStorageRepo);
            INewsCenterRepository newsCenterRepository = new NewsCenterRepository(newsClient, Options.Create<NewsContentServiceOptions>(new NewsContentServiceOptions()
            {
                ContentUrl = "https://newscenter.mayo.edu/",
                ConsumerKey = "5ce4812e6f311",
                SecretKey = "1MCDo5/yYD16MKG69kwLLkazuW3vWEFSrP0AnQi25Jc="
            }));
            //this.ContentService = new NewsService(personSvc, contentStorageRepository, newsCenterRepository);
            //NewsService = new NewsService(personSvc, contentStorageRepository, newsCenterRepository);
            this.mockService = new MockContentService(new ContentStorageRepository(tableStorageRepo, blobStorageRepo));

            this.searchService = new SearchService(new SearchRepository(
                    config["SearchServiceName"],
                    config["SearchServiceQueryKey"]));
        }

        [TestMethod]
        public void GetListV1_Test()
        {
            var controller = new ContentController(ContentService, mockService) { ControllerContext = new ControllerContext().SetContext(accessToken, userId) };

            var result = controller.GetContentListV1Async(accessToken, string.Empty, null).GetAwaiter().GetResult() as OkObjectResult;
            Assert.IsNotNull(result);
            Assert.IsNotNull(result.Value);
        }

        [TestMethod]
        public void GetList_Test()
        {
            var controller = new ContentController(ContentService, mockService) { ControllerContext = new ControllerContext().SetContext(accessToken, userId) };

            var result = controller.GetContentListAsync(accessToken, string.Empty, null).GetAwaiter().GetResult() as OkObjectResult;
            Assert.IsNotNull(result);
            Assert.IsNotNull(result.Value);
        }

        [TestMethod]
        public void GetListSection_Test()
        {
            var controller = new ContentController(ContentService, mockService) { ControllerContext = new ControllerContext().SetContext(accessToken, userId) };

            var result = controller.GetContentListAsync(accessToken, "29", null).GetAwaiter().GetResult() as OkObjectResult;
            Assert.IsNotNull(result);
            Assert.IsNotNull(result.Value);
        }

        [TestMethod]
        public void GetListContent_Test()
        {
            var controller = new ContentController(ContentService, mockService) { ControllerContext = new ControllerContext().SetContext(accessToken, userId) };

            var result = controller.GetContentListAsync(accessToken, "29", null).GetAwaiter().GetResult() as OkObjectResult;
            Assert.IsNotNull(result);
            Assert.IsNotNull(result.Value);

            var list = (List<Package>)result.Value;

            result = controller.GetContentAsync(accessToken, list.First().Items.First().Id).GetAwaiter().GetResult() as OkObjectResult;
            Assert.IsNotNull(result);
            Assert.IsNotNull(result.Value);
        }

        //93639
        [TestMethod]
        public void GetContent_Test()
        {
            var controller = new ContentController(ContentService, mockService) { ControllerContext = new ControllerContext().SetContext(accessToken, userId) };

            var result = controller.GetContentAsync(accessToken, "95267").GetAwaiter().GetResult() as OkObjectResult;
            Assert.IsNotNull(result);
        }

        [TestMethod]
        public void SearchContent_Test()
        {
            var controller = new ContentController(ContentService, mockService)
            {
                ControllerContext = new ControllerContext().SetContext(accessToken, userId)
            };

            var json = new JObject(
                new JProperty("Query", "parking"));
            //// var j = System.Text.Json.JsonDocument.Parse(json.ToString()).RootElement;

            var result = controller.SearchAsync(this.searchService, json).GetAwaiter().GetResult() as OkObjectResult;
            Assert.IsNotNull(result);
        }

        [TestMethod]
        public void GetFavorites_Test()
        {
            var controller = new ContentController(ContentService, mockService) { ControllerContext = new ControllerContext().SetContext(accessToken, userId) };

            var result = controller.FavoritesListAsync(accessToken).GetAwaiter().GetResult() as OkObjectResult;
            Assert.IsNotNull(result);
            Assert.IsNotNull(result.Value);
        }

        [TestMethod]
        public void AddFavorite_Test()
        {
            var controller = new ContentController(ContentService, mockService) { ControllerContext = new ControllerContext().SetContext(accessToken, userId) };

            var result = controller.GetContentListAsync(accessToken, string.Empty, null).GetAwaiter().GetResult() as OkObjectResult;
            Assert.IsNotNull(result);
            Assert.IsNotNull(result.Value);

            var list = (List<Package>)result.Value;
            var item = list.First().Items.First();
            var request = new FavoriteRequest { CategoryId = item.Category.Id, Id = item.Id  };

            var result2 = controller.AddFavoriteAsync(accessToken, request).GetAwaiter().GetResult() as OkObjectResult;
            Assert.IsNotNull(result2);
            Assert.IsNotNull(result2.Value);
        }

        [TestMethod]
        public void DeleteFavorite_Test()
        {
            var controller = new ContentController(ContentService, mockService) { ControllerContext = new ControllerContext().SetContext(accessToken, userId) };

            var result = controller.FavoritesListAsync(accessToken).GetAwaiter().GetResult() as OkObjectResult;
            Assert.IsNotNull(result);
            Assert.IsNotNull(result.Value);

            var list = (List<Package>)result.Value;
            var item = list.First().Items.First();
            var request = new FavoriteRequest { CategoryId = item.Category.Id, Id = item.Id };

            var result2 = controller.DeleteFavoriteAsync(accessToken, request).GetAwaiter().GetResult() as OkObjectResult;
            Assert.IsNotNull(result2);
            Assert.IsNotNull(result2.Value);
        }

        [TestMethod]
        public void GetAllComments_Test()
        {
            var controller = new ContentController(ContentService, mockService)
            {
                ControllerContext = new ControllerContext().SetContext(accessToken, userId, "<EMAIL>") 
            };

            var okResult = controller.GetCommentsAsync("95267").GetAwaiter().GetResult() as OkObjectResult;
            Assert.IsNotNull(okResult);
            var resultValue = okResult.Value as PagingResponse<Comment>;
            Assert.IsNotNull(resultValue);
            var json = JsonConvert.SerializeObject(resultValue);
            Assert.IsNotNull(json);
        }

        [TestMethod]
        public void GetAllComments_MultiPages_Test()
        {
            var controller = new ContentController(ContentService, mockService) { ControllerContext = new ControllerContext().SetContext(accessToken, userId, "<EMAIL>") };

            var okResult = controller.GetCommentsAsync("98895").GetAwaiter().GetResult() as OkObjectResult;
            Assert.IsNotNull(okResult);
            var resultValue = okResult.Value as PagingResponse<Comment>;
            Assert.IsNotNull(resultValue);
            var json = JsonConvert.SerializeObject(resultValue);
            Assert.IsNotNull(json);
        }

        [TestMethod]
        public void GetCommentsPaged_Test()
        {
            var controller = new ContentController(ContentService, mockService) { ControllerContext = new ControllerContext().SetContext(accessToken, userId) };

            var okResult = controller.GetCommentsAsync("95283", "1").GetAwaiter().GetResult() as OkObjectResult;
            Assert.IsNotNull(okResult);
            var resultValue = okResult.Value as PagingResponse<Comment>;
            Assert.IsNotNull(resultValue);
        }

        [TestMethod]
        public void PostComment_Test()
        {
            var controller = new ContentController(ContentService, mockService) 
            {
                ControllerContext = new ControllerContext().SetContext(accessToken, userId, "<EMAIL>")
            };

            var c = new Comment
            {
                ContentId = "97698",
                ThreadId = null,
                Text = "This is a Test Comment, Sorry for any inconvenience.",
                Id = null
            };

            var okResult = controller.AddCommentAsync(c).GetAwaiter().GetResult() as OkResult;
            Assert.IsNotNull(okResult);
        }

        [TestMethod]
        public void SortOrder_Test()
        {
            var t = ContentService.GetListAsync("49", null).Result;

            Assert.IsTrue(t.Count == 0);
        }

        //[TestMethod]
        //public void SaveContentLists_Test()
        //{
        //    var t = NewsService.SaveAsync();
        //    t.Wait();
        //    Assert.IsTrue(t.IsCompleted);
        //}

        //[TestMethod]
        //public void RefeshContentLists_Test()
        //{
        //    var list = new ConcurrentBag<string>();
        //    Action<string> callback = list.Add;

        //    var newsService = new NewsContentService(
        //        "Employee",
        //        @"https://newscenter.mayo.edu/",
        //        @"5ce4812e6f311",
        //        @"1MCDo5/yYD16MKG69kwLLkazuW3vWEFSrP0AnQi25Jc=",
        //        this.repository,
        //        callback);

        //    var t = newsService.RefeshBlobsAsync(new List<string> { "93014" });
        //    t.Wait();
        //    Assert.IsTrue(t.IsCompleted);

        //    Assert.IsTrue(list.Count == 0);
        //}

        //[TestMethod]
        //public void SaveContentWrapper_Test()
        //{
        //    var list = new ConcurrentBag<string>();
        //    Action<string> callback = list.Add;

        //    var newsService = new NewsContentService(
        //        "Employee",
        //        @"https://newscenter.mayo.edu/",
        //        @"5ce4812e6f311",
        //        @"1MCDo5/yYD16MKG69kwLLkazuW3vWEFSrP0AnQi25Jc=",
        //        this.repository,
        //        callback);

        //    var t = newsService.SaveWrapperAsync();
        //    t.Wait();
        //    Assert.IsTrue(t.IsCompleted);
        //}


        //// 86875
        //[TestMethod]
        //public void SaveContent_Test()
        //{
        //    var list = new ConcurrentBag<string>();
        //    Action<string> callback = list.Add;

        //    var newsService = new NewsContentService(
        //        "Employee",
        //        @"https://newscenter.mayo.edu/",
        //        @"5ce4812e6f311",
        //        @"1MCDo5/yYD16MKG69kwLLkazuW3vWEFSrP0AnQi25Jc=",
        //        this.repository,
        //        callback);

        //    //86875
        //    //86543
        //    //91314
        //    //92342
        //    var t = newsService.SaveAsync("SECTIONS", "34", "93639");
        //    t.Wait();
        //    Assert.IsTrue(t.IsCompleted);

        //    Assert.IsTrue(list.Count == 0);
        //}

        //[TestMethod]
        //public void GrabContent_Test()
        //{
        //    var list = new ConcurrentBag<string>();
        //    Action<string> callback = list.Add;

        //    var newsService = new NewsContentService(
        //        "Employee",
        //        @"https://newscenter.mayo.edu/",
        //        @"5ce4812e6f311",
        //        @"1MCDo5/yYD16MKG69kwLLkazuW3vWEFSrP0AnQi25Jc=",
        //        this.repository,
        //        callback);

        //    //86875
        //    //86543
        //    var t = newsService.GetContentAsync("93639");
        //    t.Wait();
        //    Assert.IsTrue(t.IsCompleted);
        //}

        //[TestMethod]
        //public void GrabContentMeta_Test()
        //{
        //    var list = new ConcurrentBag<string>();
        //    Action<string> callback = list.Add;

        //    var newsService = new NewsContentService(
        //        "Employee",
        //        @"https://newscenter.mayo.edu/",
        //        @"5ce4812e6f311",
        //        @"1MCDo5/yYD16MKG69kwLLkazuW3vWEFSrP0AnQi25Jc=",
        //        this.repository,
        //        callback);

        //    //86875
        //    //86543
        //    var t = newsService.GetMetaDataAsync("93639");
        //    t.Wait();
        //    Assert.IsTrue(t.IsCompleted);
        //}

        //[TestMethod]
        //public void GrabComments_Test()
        //{
        //    var list = new ConcurrentBag<string>();
        //    Action<string> callback = list.Add;

        //    var newsService = new NewsContentService(
        //        "Employee",
        //        @"https://newscenter.mayo.edu/",
        //        @"5ce4812e6f311",
        //        @"1MCDo5/yYD16MKG69kwLLkazuW3vWEFSrP0AnQi25Jc=",
        //        this.repository,
        //        callback);

        //    //86875
        //    //86543
        //    var t = newsService.CommentsAsync("95283");
        //    t.Wait();
        //    Assert.IsTrue(t.IsCompleted);
        //}

        //[TestMethod]
        //public void GrabUser_Test()
        //{
        //    var list = new ConcurrentBag<string>();
        //    Action<string> callback = list.Add;

        //    var newsService = new NewsContentService(
        //        "Employee",
        //        @"https://newscenter.mayo.edu/",
        //        @"5ce4812e6f311",
        //        @"1MCDo5/yYD16MKG69kwLLkazuW3vWEFSrP0AnQi25Jc=",
        //        this.repository,
        //        callback);

        //    var t = newsService.UserAsync("<EMAIL>");
        //    t.Wait();
        //    Assert.IsTrue(t.IsCompleted);
        //}

        //[TestMethod]
        //public void AddUser_Test()
        //{
        //    var list = new ConcurrentBag<string>();
        //    Action<string> callback = list.Add;

        //    var newsService = new NewsContentService(
        //        "Employee",
        //        @"https://newscenter.mayo.edu/",
        //        @"5ce4812e6f311",
        //        @"1MCDo5/yYD16MKG69kwLLkazuW3vWEFSrP0AnQi25Jc=",
        //        this.repository,
        //        callback);

        //    var t = newsService.AddUserAsync("<EMAIL>");
        //    t.Wait();
        //    Assert.IsTrue(t.IsCompleted);
        //}

        //[TestMethod]
        //public void SaveImages_Test()
        //{
        //    var newsService = new NewsContentService(
        //        "Employee",
        //        @"https://newscenter.mayo.edu/",
        //        @"5ce4812e6f311",
        //        @"1MCDo5/yYD16MKG69kwLLkazuW3vWEFSrP0AnQi25Jc=",
        //        this.repository);

        //    var t = newsService.SaveImageAsync("CareProviderWTablet-1024x576.jpg", "https://newscenter.mayo.edu/n1/64f97ed3edbbd8e3/uploads/2019/03/CareProviderWTablet-1024x576.jpg");
        //    t.Wait();
        //    Assert.IsTrue(t.IsCompleted);
        //}

        //[TestMethod]
        //public void GetApiContent_Test()
        //{
        //    var newsService = new NewsContentService(
        //        "Employee",
        //        @"https://newscenter.mayo.edu/",
        //        @"5ce4812e6f311",
        //        @"1MCDo5/yYD16MKG69kwLLkazuW3vWEFSrP0AnQi25Jc=",
        //        this.repository);

        //    var t = newsService.GetRequestClientAsync($"https://newscenter.mayo.edu/api-v2/editorial/?cat=31&posts_per_page=10&include_featured_image&include_terms");
        //    t.Wait();
        //    Assert.IsTrue(t.IsCompleted);
        //}

        //[TestMethod]
        //public void GetApiSiteOptions_Test()
        //{
        //    var newsService = new NewsContentService(
        //        "Employee",
        //        @"https://newscenter.mayo.edu/",
        //        @"5ce4812e6f311",
        //        @"1MCDo5/yYD16MKG69kwLLkazuW3vWEFSrP0AnQi25Jc=",
        //        this.repository);

        //    var t = newsService.GetRequestClientAsync($"https://newscenter.mayo.edu/api-v2/site-options/single?option=intranet_news_center_headlines&specific_key=49");
        //    t.Wait();
        //    Assert.IsTrue(t.IsCompleted);
        //}
    }
}
