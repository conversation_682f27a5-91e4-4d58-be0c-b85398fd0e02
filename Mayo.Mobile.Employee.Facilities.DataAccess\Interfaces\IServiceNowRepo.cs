﻿using Mayo.Mobile.Employee.Facilities.DataAccess.Models;
using System.Collections.Generic;
using System.IO;
using System.Threading.Tasks;

namespace Mayo.Mobile.Employee.Facilities.DataAccess.Interfaces
{
    public interface IServiceNowRepo
    {
        /// <summary>
        /// Use to create a Facility WorkOrder 
        /// </summary>
        /// <param name="info">The request body</param>
        /// <returns></returns>
        public Task<FacilityCreateWOResult> CreateFacilityWorkOrderAsync(FacilitiesCreateWORequestBody info);

        /// <summary>
        /// Use to Attach an Image to a Created WorkOrder
        /// </summary>
        /// <param name="imageStream">the image</param>
        /// <param name="sysId">the id for the workorder</param>
        /// <param name="imageName"> the images file name</param>
        /// <returns></returns>
        public Task<FacilityWOAttachmentResult> AttachImageToFacilityWorkOrder(Stream imageStream, string sysId, string imageName);
    }
}
