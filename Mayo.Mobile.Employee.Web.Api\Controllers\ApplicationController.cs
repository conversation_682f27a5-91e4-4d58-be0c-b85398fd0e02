﻿//-----------------------------------------------------------------------
// <copyright file="ApplicationController.cs" company="Mayo Foundation">
//     Copyright © Mayo Foundation. All rights reserved.
// </copyright>
//-----------------------------------------------------------------------

namespace Mayo.Mobile.Employee.Web.Api.Controllers
{
    using System;
    using System.Collections.Generic;
    using System.Linq;
    using System.Security.Principal;
    using System.Text;
    using System.Threading.Tasks;
    using Mayo.Mobile.Application.Model;
    using Mayo.Mobile.Application.Models;
    using Mayo.Mobile.Employee.Application.Model;
    using Mayo.Mobile.Employee.Web.Api.Model;
    using Mayo.Mobile.Logging.Interfaces;
    using Mayo.Mobile.Logging.Model;
    using Mayo.Mobile.Notifications.Azure.Storage.Model;
    using Microsoft.AspNetCore.Authorization;
    using Microsoft.AspNetCore.Mvc;

    /// <summary>
    /// Initializes a new instance of the ApplicationController class
    /// </summary>
    [AllowAnonymous]
    [ApiVersion("1.0")]
    [ApiController]
    [Route("api/[controller]")]
    [Route("api/v{version:apiVersion}/[controller]")]
    public class ApplicationController : ControllerBase
    {

        private IApplicationService ApplicationService;
        private IMayoMobileLogger MayoMobileLogger;

        /// <summary>
        /// Initializes a new instance of the ApplicationController class.
        /// </summary>
        /// <param name="service">The <see cref="IApplicationService"/></param>
        /// <param name="logger">The <see cref="ILogger"/></param>
        public ApplicationController(IApplicationService service, IMayoMobileLogger logger)
        {
            ApplicationService = service;
            MayoMobileLogger = logger;
        }

        /// <summary>
        /// Get the privacy policy
        /// </summary>
        /// <returns>The <see cref="Task{TResult}"/></returns>
        [HttpGet("{name}")]
        public async Task<ContentResult> Get(string name)
        {
            var r = new ContentResult
            {
                StatusCode = 200,
                ContentType = "text/html"
            };

            switch (true)
            {
                case bool _ when name.Equals("terms", StringComparison.OrdinalIgnoreCase):
                    r.Content = await ApplicationService.GetDataAsync("terms.html");
                    break;
                case bool _ when name.Equals("policy", StringComparison.OrdinalIgnoreCase):
                    r.Content = await ApplicationService.GetDataAsync("policy.html");
                    break;
                case bool _ when name.Equals("health", StringComparison.OrdinalIgnoreCase):
                    r.StatusCode = 200;
                    r.Content = "<html><body>Hi there</body></html>";
                    break;
                default:
                    r.StatusCode = 404;
                    r.Content = "<html></html>";
                    break;
            }

            return r;
        }

        /// <summary>
        /// Update the device
        /// </summary>
        /// <param name="device">The <see cref="Device"/></param>
        /// <returns>The <see cref="Task{TResult}"/></returns>
        [HttpPost("Device/Update")]
        [ProducesResponseType(typeof(List<Mayo.Mobile.Employee.Web.Api.Model.DeviceResponse>), 200)]
        public async Task<IActionResult> UpdateDevice([FromBody] Device device)
        {
            //// if device id is not passed, one is created in the ApplicationRepository.cs
            var status = await ApplicationService.SaveDeviceAsync(device);
            return Ok(new DeviceResponse(status.Device, status.Update));
        }

        /// <summary>
        /// Send Feedback
        /// </summary>
        /// <returns>The <see cref="Task{TResult}"/></returns>
        [Authorize]
        [HttpPost("Feedback/Send")]
        [ProducesResponseType(200)]
        public async Task<IActionResult> Feedback([FromHeader] string authorization, [FromBody] FeedbackRequest r)
        {
            //// todo: add function to save feedback partitioned by date for easier search
            await MayoMobileLogger.LogFeedbackAsync(User.Identity.GetUserId(), r.Email, r.DeviceId, r.Type, r.Message);
            return Ok();
        }
    }
}
