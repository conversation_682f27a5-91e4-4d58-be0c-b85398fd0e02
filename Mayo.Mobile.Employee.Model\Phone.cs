﻿//-----------------------------------------------------------------------
// <copyright file="Phone.cs" company="Mayo Foundation">
//     Copyright © Mayo Foundation. All rights reserved.
// </copyright>
//-----------------------------------------------------------------------

namespace Mayo.Mobile.Employee.Model
{
    using System;
    using System.Collections.Generic;
    using System.Runtime.Serialization;
    using System.Text;

    /// <summary>
    /// Initializes a new instance of the Phone class.
    /// </summary>
    [DataContract(Namespace = "http://www.mayo.edu/MayoMobile")]
    public class Phone
    {
        /// <summary>
        /// Initializes a new instance of the Phone class.
        /// </summary>
        /// <param name="number">The phone number</param>
        public Phone(string number)
        {
            //// (77)6-0366
            
            var internalNumber = string.Empty;
            var externalNumber = string.Empty;

            switch (number)
            {
                //// Minnesota
                case string n when number.StartsWith("(77)"):
                    internalNumber = number;
                    switch (number.Substring(4,1))
                    {
                        case "2":
                            externalNumber = $"507-42{number.Substring(4)}";
                            break;
                        case "3":
                            externalNumber = $"507-29{number.Substring(4)}";
                            break;
                        case "4":
                            externalNumber = $"507-28{number.Substring(4)}";
                            break;
                        case "5":
                            externalNumber = $"507-25{number.Substring(4)}";
                            break;
                        case "6":
                            externalNumber = $"507-26{number.Substring(4)}";
                            break;
                        case "8":
                            externalNumber = $"507-53{number.Substring(4)}";
                            break;
                        default:
                            break;
                    }
                    break;
                //// Florida
                case string n when number.StartsWith("(78)"):
                    internalNumber = number;
                    switch (number.Substring(4, 1))
                    {
                        default:
                            externalNumber = $"904-95{number.Substring(4)}";
                            break;
                    }
                    break;
                //// Arizona
                case string n when number.StartsWith("(79)"):
                    internalNumber = number;
                    switch (number.Substring(4, 1))
                    {
                        case "1":
                            externalNumber = $"480-30{number.Substring(4)}";
                            break;
                        case "2":
                            externalNumber = $"480-34{number.Substring(4)}";
                            break;
                        case "6":
                            externalNumber = $"480-42{number.Substring(4)}";
                            break;
                        default:
                            break;
                    }
                    break;
                default:
                    internalNumber = number;
                    externalNumber = number;
                    break;
            }
            
            this.PhoneNumbers = new Dictionary<string, string>
                {
                    { "Internal", internalNumber },
                    { "External", externalNumber }
                };
        }

        /// <summary>
        /// Initializes a new instance of the Phone class.
        /// </summary>
        /// <param name="internalNumber">The internal phone number</param>
        /// <param name="externalNumber">The external phone number</param>
        public Phone(string internalNumber, string externalNumber)
        {
            this.PhoneNumbers = new Dictionary<string, string>();
            if (string.IsNullOrEmpty(internalNumber) == false)
            {
                this.PhoneNumbers.Add("Internal", internalNumber);
            }

            if (string.IsNullOrEmpty(externalNumber) == false)
            {
                this.PhoneNumbers.Add("External", externalNumber);
            }
        }

        /// <summary>
        /// Gets or sets the phone numbers
        /// </summary>
        [DataMember]
        public Dictionary<string, string> PhoneNumbers { get; set; }
    }
}