﻿using System.Collections.Generic;

namespace Mayo.Mobile.Employee.Azure.DataAccess.Models
{
    public class FacilityWorkOrderQueueMessage
    {
        public string Id { get; set; }
        public string CampusId { get; set; }
        public string BuildingId { get; set; }
        public string FloorId { get; set; }
        public string RoomId { get; set; }
        public string Comments { get; set; }
        public string ReportedBy { get; set; }
        public string Latitude { get; set; }
        public string Longitude { get; set; }

        /// <summary>
        /// Ids of the photos to add to a WorkOrder.
        /// Photo images are saved in a separate repo.
        /// </summary>
        public List<string> PhotoIds { get; set; }

    }
}
