﻿// -----------------------------------------------------------------------
// <copyright file="UserContent.cs" company="Mayo Clinic">
//     Copyright © Mayo Foundation. All rights reserved.
// </copyright>
// -----------------------------------------------------------------------

namespace Mayo.Mobile.Employee.Content.News.Model
{
    using System;
    using System.Collections.Generic;
    using System.Text;
    using Newtonsoft.Json;

    /// <summary>
    /// Initializes a new instance of the UserContent class.
    /// </summary>
    public class UserContent
    {
        /// <summary>
        /// Gets or sets the parameters
        /// </summary>
        [JsonProperty("parameters_passed")]
        public dynamic ParametersPassed { get; set; }

        /// <summary>
        /// Gets or sets a value indicating whether the user already is in the news center
        /// </summary>
        [JsonProperty("user_already_existed")]
        public bool UserAlreadyExisted { get; set; }

        /// <summary>
        /// Gets or sets the <see cref="User"/>
        /// </summary>
        [JsonProperty("data")]
        public User User { get; set; }
    }
}
