using Mayo.Mobile.Application.Azure.DataAccess.Interfaces;
using Mayo.Mobile.Application.Azure.DataAccess.Models;
using Mayo.Mobile.Application.Azure.DataAccess.Repositories;
using Mayo.Mobile.Employee.Azure.Search.Model;
using Mayo.Mobile.Employee.Content.Model;
using Mayo.Mobile.Employee.Search.Model;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Options;
using Microsoft.VisualStudio.TestTools.UnitTesting;

namespace Mayo.Mobile.Employee.Api.Int.Tests
{
    [TestClass]
    public class SearchTests
    {
        private SearchAdminService service = null;

        [TestInitialize]
        public void Init()
        {
            var config = new ConfigurationBuilder().AddJsonFile("appsettings.json").Build();
            var saRepoOptions = Options.Create<StorageAccountRepoOptions>(new StorageAccountRepoOptions() { ConnectionString = config.GetConnectionString("StorageAccount") });
            ITableStorageRepository tableStorageRepo = new TableStorageRepository(saRepoOptions);
            IBlobStorageRepository blobStorageRepo = new BlobStorageRepository(saRepoOptions);
            IQueueStorageRepository queueStorageRepo = new QueueStorageRepository(saRepoOptions);
            ISearchAdminRepository searchAdminRepository = new AzureAdminRepository(Options.Create<AzureAdminRepoOptions>(new AzureAdminRepoOptions()
            {
                    ServiceName = config["SearchServiceName"],
                    AdminApiKey = config["SearchServiceAdminKey"],
                     QueryApiKey = config["SearchServiceQueryKey"]
            }));
            IContentStorageRepository contentStorageRepository = new ContentStorageRepository(tableStorageRepo, blobStorageRepo);
            service = new SearchAdminService(searchAdminRepository, contentStorageRepository);
        }

        [TestMethod]
        public void LoadNewsContentSearchIndex_Test()
        {
            var t = service.LoadNewsCenterIndexAsync();
            t.Wait();
            Assert.IsTrue(t.IsCompletedSuccessfully);
        }
    }
}
