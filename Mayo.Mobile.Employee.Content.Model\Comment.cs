﻿//-----------------------------------------------------------------------
// <copyright file="Comment.cs" company="Mayo Foundation">
//     Copyright © Mayo Foundation. All rights reserved.
// </copyright>
//-----------------------------------------------------------------------

namespace Mayo.Mobile.Employee.Content.Model
{
    using System.Collections.Generic;
    using System.Runtime.Serialization;
    using Mayo.Mobile.Employee.Model;

    /// <summary>
    /// Initializes a new instance of the Comment class.
    /// </summary>
    [DataContract(Namespace = "http://www.mayo.edu/MayoMobile")]
    public class Comment
    {
        /// <summary>
        /// Gets or sets the identifier
        /// </summary>
        [DataMember]
        public string Id { get; set; }

        /// <summary>
        /// Gets or sets the content identifier
        /// </summary>
        [DataMember]
        public string ContentId { get; set; }

        /// <summary>
        /// Gets or sets the thread identifier
        /// </summary>
        [DataMember]
        public string ThreadId { get; set; }

        /// <summary>
        /// Gets or sets the <see cref="Person"/>
        /// </summary>
        [DataMember]
        public Person Commenter { get; set; }

        /// <summary>
        /// Gets or sets the comment text
        /// </summary>
        [DataMember]
        public string Text { get; set; }

        /// <summary>
        /// /// Gets or sets the date as date to string 's'
        /// </summary>
        [DataMember]
        public string Date { get; set; }

        /// <summary>
        /// Gets or sets any sub comments
        /// </summary>
        [DataMember]
        public List<Comment> Comments { get; set; }
    }
}

