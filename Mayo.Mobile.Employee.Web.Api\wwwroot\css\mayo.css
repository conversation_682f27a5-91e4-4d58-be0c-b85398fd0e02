﻿html, body, div, h1, h2, h3, h4, h5, h6, p, img, dl, dt, dd, ol, ul, li, table, caption, tbody, tfoot, thead, tr, th, td, form, fieldset, embed, object, applet {
	margin: 0;
	padding: 0;
	border: 0;
}
body {
	font-size: 62.5%;
	font-family: arial,sans-serif;
	color: #000;
	background: #fff;
}
a {
	color: #00c;
}
a:active {
	color: #f00;
}
a:visited {
	color: #551a8b;
}
table {
	border-collapse: collapse;
	border-width: 0;
	empty-cells: show;
}
ul {
	padding: 0 0 1em 1em;
}
ol {
	padding: 0 0 1em 1.3em;
}
li {
	line-height: 1.5em;
	padding: 0 0 .5em 0;
}
p {
	padding: 0 0 1em 0;
}
h1, h2, h3, h4, h5 {
	padding: 0 0 1em 0;
}
h1, h2 {
	font-size: 1.3em;
}
h3 {
	font-size: 1.1em;
}
h4, h5, table {
	font-size: 1em;
}
sup, sub {
	font-size: .7em;
}
input, select, textarea, option {
	font-family: inherit;
	font-size: inherit;
}
.g-doc, .g-doc-1024, .g-doc-800 {
	font-size: 130%;
}
