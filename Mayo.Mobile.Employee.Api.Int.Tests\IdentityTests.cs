﻿using System;
using Mayo.Mobile.Application.Azure.DataAccess.Interfaces;
using Mayo.Mobile.Application.Azure.DataAccess.Models;
using Mayo.Mobile.Application.Azure.DataAccess.Repositories;
using Mayo.Mobile.Employee.Content.Model;
using Mayo.Mobile.Employee.Identity.Model;
using Mayo.Mobile.Employee.Model;
using Mayo.Mobile.Employee.Web.Api.Controllers;
using Mayo.Mobile.Employee.Web.Api.Model;
using Mayo.Mobile.Employee.Web.Api.Models;
using Mayo.Mobile.Logging.Interfaces;
using Mayo.Mobile.Logging.Model;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Options;
using Microsoft.VisualStudio.TestTools.UnitTesting;
using Newtonsoft.Json.Linq;

namespace Mayo.Mobile.Employee.Api.Int.Tests
{
    [TestClass]
    public class IdentityTests
    {
        private IdentityService service = null;

        private IUserRepository userRepository = null;

        private IMayoMobileLogger logger = null;
        private IOptions<JWTOptions> jwtOptions;

        [TestInitialize]
        public void Init()
        {
            var config = new ConfigurationBuilder().AddJsonFile("appsettings.json").Build();

            var c = new System.Net.Http.HttpClient
            {
                BaseAddress = new Uri("https://dotnetdev.mayo.edu")
            };
            var saRepoOptions = Options.Create<StorageAccountRepoOptions>(new StorageAccountRepoOptions() { ConnectionString = config.GetConnectionString("StorageAccount") });
            ITableStorageRepository tableStorageRepo = new TableStorageRepository(saRepoOptions);
            IBlobStorageRepository blobStorageRepo = new BlobStorageRepository(saRepoOptions);
            IQueueStorageRepository queueStorageRepo = new QueueStorageRepository(saRepoOptions);
            jwtOptions = Options.Create<JWTOptions>(new JWTOptions() 
            {  
                SigningIssuer = config.GetConnectionString("JWTSignerIssuer"),
                SigningKey = config.GetConnectionString("JWTSigningKey")
            });
            logger = new MayoMobileLogger(tableStorageRepo, queueStorageRepo);
            service = new IdentityService(c);
            userRepository = new UserRepository(tableStorageRepo);

        }

        [TestMethod]
        public void Trouble_Signing_In_Test()
        {
            var controller = new IdentityController(service, userRepository, logger, jwtOptions)
            {
                ControllerContext = new ControllerContext()
            };
            controller.ControllerContext.HttpContext = new DefaultHttpContext();
            controller.ControllerContext.HttpContext.Request.Path = @"/api/identity/troublesigningin";

            JObject j = new JObject(
                new JProperty("UserName", "emb05"),
                new JProperty("ApplicationId", "Employee"),
                new JProperty("DeviceId", "00f879f5-4863-467b-aa85-079b2a468e7f"),
                new JProperty("Email", "<EMAIL>"));

            var result = controller.TroubleSigningInAsync(j).GetAwaiter().GetResult() as OkResult;
            Assert.IsNotNull(result);
            Assert.AreEqual(200, result.StatusCode);
        }

        /*
        [TestMethod]
        public void GetPhoto_Test()
        {
            var controller = new DirectoryController(this.service, this.logger) { ControllerContext = new ControllerContext().SetContext(accessToken, userId) };

            var result = controller.GetPhoto("16121007").GetAwaiter().GetResult() as OkObjectResult;
            Assert.IsNotNull(result);
            Assert.IsNotNull(result.Value);
        }
        */
    }
}
