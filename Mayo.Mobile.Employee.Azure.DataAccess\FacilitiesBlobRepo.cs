﻿using Azure.Storage.Blobs;
using Azure.Storage.Blobs.Models;
using Mayo.Mobile.Application.Azure.DataAccess.Models;
using Mayo.Mobile.Employee.Azure.DataAccess.Interfaces;
using Mayo.Mobile.Employee.Azure.DataAccess.Models;
using Microsoft.Extensions.Options;
using System.IO;
using System.Threading.Tasks;

namespace Mayo.Mobile.Employee.Azure.DataAccess
{
    public class FacilitiesBlobRepo: IWorkOrderAttachmentsRepo
    {
        private BlobContainerClient blobContainerClient;

        public FacilitiesBlobRepo(IOptions<StorageAccountRepoOptions> storageAccountOptions, IOptions<FacilityBlobOptions> configurationOptions)
        {
            blobContainerClient = InitializeBlobContainerClient(storageAccountOptions.Value.ConnectionString, configurationOptions.Value.BlobContainerName);
        }

        /// <summary>
        /// Retrieves the blob in this container with the past in blobName
        /// </summary>
        /// <typeparam name="Stream"></typeparam>
        /// <param name="blobName">name of blob you want to retrieve</param>
        /// <returns></returns>
        public async Task<Stream> GetImageFileAsync(string blobName)
        {
            var blobClient = blobContainerClient.GetBlobClient(blobName);
            BlobDownloadInfo download = await blobClient.DownloadAsync();
            return download.Content;
        }

        /// <summary>
        /// Inserts a new blob in this container or overwrites blob if it already exists
        /// </summary>
        /// <param name="photo"></param>
        /// <param name="photoId"></param>
        /// <returns></returns>
        public async Task InsertUpdateFileAsync(Stream photo, string photoId)
        {
            var blobClient = blobContainerClient.GetBlobClient(photoId);
            await blobClient.UploadAsync(photo);
        }

        /// <summary>
        /// Deletes Blob if it Exists
        /// </summary>
        /// <param name="photoId">Name of blob in this container to be deleted if it Exists</param>
        /// <returns></returns>
        public async Task DeleteFileAsync(string photoId)
        {
            var blobClient = blobContainerClient.GetBlobClient(photoId);
            await blobClient.DeleteIfExistsAsync();
        }
        
        private BlobContainerClient InitializeBlobContainerClient(string connectionString, string containerName)
        {
            var blobServiceClient = new BlobServiceClient(connectionString); //Authenticating that you can access StorageAccount
            var blobContainerClient = blobServiceClient.GetBlobContainerClient(containerName); //Setting up the service client
            return blobContainerClient;
        }

    }
}
