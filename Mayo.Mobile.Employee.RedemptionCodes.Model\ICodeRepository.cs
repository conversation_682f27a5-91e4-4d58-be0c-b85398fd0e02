﻿//-----------------------------------------------------------------------
// <copyright file="ICodeRepository.cs" company="Mayo Foundation">
//     Copyright © Mayo Foundation. All rights reserved.
// </copyright>
//-----------------------------------------------------------------------

namespace Mayo.Mobile.Employee.RedemptionCodes.Model
{
    using System;
    using System.Collections.Generic;
    using System.Text;
    using System.Threading.Tasks;

    /// <summary>
    /// Initializes a new instance of the ICodeRepository class.
    /// </summary>
    public interface ICodeRepository
    {
        /// <summary>
        /// Get the list of users
        /// </summary>
        /// <returns>The <see cref="Task{TResult}"/></returns>
        Task<List<(string UserId, string code, DateTimeOffset date)>> GetUsersAsync();

        /// <summary>
        /// Get the code for a user if they have one
        /// </summary>
        /// <param name="userId">The user identifier</param>
        /// <returns>The <see cref="Task{TResult}"/></returns>
        Task<string> GetCodeAsync(string userId);

        /// <summary>
        /// Get the url for the code
        /// </summary>
        /// <param name="code">The redemption code</param>
        /// <returns>The <see cref="Task{TResult}"/></returns>
        Task<(string Url, string UserId)> GetUrlAsync(string code);

        /// <summary>
        /// Get the code for a user if they do not have one
        /// </summary>
        /// <param name="userId">The user identifier</param>
        /// <returns>The <see cref="Task{TResult}"/></returns>
        Task<string> RedeemCodeAsync(string userId);
    }
}
