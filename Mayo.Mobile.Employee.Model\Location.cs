﻿//-----------------------------------------------------------------------
// <copyright file="Location.cs" company="Mayo Foundation">
//     Copyright © Mayo Foundation. All rights reserved.
// </copyright>
//-----------------------------------------------------------------------

namespace Mayo.Mobile.Employee.Model
{
    using System.Runtime.Serialization;

    /// <summary>
    /// Initializes a new instance of the StaffLocation class.
    /// </summary>
    [DataContract(Namespace = "http://www.mayo.edu/MayoMobile")]
    public class Location : Mayo.Mobile.Staff.Model.Location
    {
        /// <summary>
        /// Gets or sets the mail location
        /// </summary>
        [DataMember]
        public string Mail { get; set; }

        /// <summary>
        /// Gets or sets the country
        /// </summary>
        [DataMember]
        public string Country { get; set; }

        /// <summary>
        /// Gets or sets the zip code
        /// </summary>
        [DataMember]
        public string ZipCode { get; set; }
    }
}
