﻿// -----------------------------------------------------------------------
// <copyright file="IdentityService.cs" company="Mayo Clinic">
//     Copyright © Mayo Foundation. All rights reserved.
// </copyright>
// -----------------------------------------------------------------------

namespace Mayo.Mobile.Employee.Identity.Model
{
    using System.IO;
    using System.Net;
    using System.Net.Http;
    using System.Net.Http.Headers;
    using System.Text;
    using System.Threading.Tasks;
    using Newtonsoft.Json.Linq;

    /// <summary>
    /// Initializes a new instance of the IdentityService class.
    /// </summary>
    public class IdentityService : IIdentityService
    {

        private HttpClient Client;

        /// <summary>
        /// Initializes a new instance of the IdentityService class.
        /// </summary>
        /// <param name="client"></param>
        public IdentityService (HttpClient client)
        {
            Client = client;
        }

        /// <summary>
        /// Authenticate user credentials
        /// </summary>
        /// <param name="userName">The user name</param>
        /// <param name="password">The password</param>
        /// <returns>The <see cref="Task{TResult}"/></returns>
        public async Task<Employee.Model.Identity> LogonAsync (string userName, string password)
        {
            var j = new JObject (
                new JProperty ("ApplicationId", "Employee"),
                new JProperty ("Password", password),
                new JProperty ("UserId", userName),
                new JProperty("UserName", userName),
                new JProperty ("RequestType", "ProviderLogon"),
                new JProperty ("Environment", "PROD"));

            var content = new StringContent (j.ToString (), Encoding.UTF8, "application/json");
            var response = await Client.PostAsync ("/mobile/Identity/api/Identity/Employee/Logon", content);
            var json = (JObject.Parse(await response.Content.ReadAsStringAsync()));

            if (response.IsSuccessStatusCode)
            {
                var identity = new Employee.Model.Identity
                {
                    UserId = (string)json["Id"],
                    FullName = (string)json["DisplayName"],
                    FirstName = (string)json["FirstName"],
                    LastName = (string)json["LastName"],
                    IsActive = true,
                    IsValidated = true,
                    Email = (string)json["Email"],
                    Id = (string)json["Id"],
                    PasswordUpdateDate = (string)json["PasswordUpdateDate"]
                };

                return identity;
            }
            else
            {
                throw new IdentityException((string)json["Message"], response.StatusCode, userName:userName);
            }
        }

        /// <summary>
        /// Authenticate the user
        /// </summary>
        /// <param name="token">The access token</param>
        /// <returns>The <see cref="Task{TResult}"/></returns>
        public async Task<Employee.Model.Identity> TokenAsync(string token)
        {
            var j = new JObject(
                new JProperty("ApplicationId", "Employee"));

            var content = new StringContent(j.ToString(), Encoding.UTF8, "application/json");
            Client.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", token);
            var response = await Client.PostAsync("/mobile/Identity/api/Identity/Employee/Token", content);

            var json = (JObject.Parse(await response.Content.ReadAsStringAsync()));
            var identity = new Employee.Model.Identity
            {
                UserId = (string)json["Id"],
                FullName = (string)json["DisplayName"],
                FirstName = (string)json["FirstName"],
                LastName = (string)json["LastName"],
                IsActive = true,
                IsValidated = true,
                Email = (string)json["Email"],
                Id = (string)json["Id"],
                PasswordUpdateDate = (string)json["PasswordUpdateDate"]
            };

            return identity;
        }

        /// <summary>
        /// Gets person photo
        /// </summary>
        /// <param name="personId">The user id</param>
        /// <returns>The <see cref="Task{TResult}"/></returns>
        public async Task<Stream> GetPhotoAsync(string personId)
        {
            var response = await Client.GetAsync($"http://quarterly.mayo.edu/qtphotos/{personId}.jpg");
            return await response.Content.ReadAsStreamAsync();
        }
    }
}