﻿// -----------------------------------------------------------------------
// <copyright file="IdentityException.cs" company="Mayo Clinic">
//     Copyright © Mayo Foundation. All rights reserved.
// </copyright>
// -----------------------------------------------------------------------

namespace Mayo.Mobile.Employee.Identity.Model
{
    using System;
    using System.Collections.Generic;
    using System.Linq;
    using System.Runtime.Serialization;
    using System.Security.Permissions;
    using System.Text;

    /// <summary>
    /// Initializes a new instance of the IdentityException class.
    /// </summary>
    [SerializableAttribute]
    public class IdentityException : System.Exception
    {
        /// <summary>
        /// Gets or sets <see cref="HttpStatusCode"/>
        /// </summary>
        public System.Net.HttpStatusCode Status { get; set; }

        /// <summary>
        /// The source of the exception
        /// </summary>
        public string SystemId { get; set; }

        /// <summary>
        /// Gets or sets the error identifier
        /// </summary>
        public string ErrorId { get; set; }

        /// <summary>
        /// Gets or sets the user name
        /// </summary>
        public string UserName { get; set; }

        /// <summary>
        /// Initializes a new instance of the IdentityException class.
        /// </summary>
        /// <param name="message">The exception message</param>
        /// <param name="status">The <see cref="HttpStatusCode"/></param>
        /// <param name="errorId">The error identifier</param>
        /// <param name="systemId">The source of the exception</param>
        /// <param name="userName">The username</param>
        public IdentityException(string message, System.Net.HttpStatusCode status, string errorId = null, string systemId = null, string userName = null)
            : base(message)
        {
            this.ErrorId = errorId ?? string.Empty;
            this.Status = status;
            this.SystemId = systemId ?? string.Empty;
            this.UserName = userName ?? string.Empty;
        }
    }
}
