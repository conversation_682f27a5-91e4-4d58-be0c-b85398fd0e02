﻿//-----------------------------------------------------------------------
// <copyright file="Supervisor.cs" company="Mayo Foundation">
//     Copyright © Mayo Foundation. All rights reserved.
// </copyright>
//-----------------------------------------------------------------------

namespace Mayo.Mobile.Employee.Model
{
    using System.Runtime.Serialization;

    /// <summary>
    /// Initializes a new instance of the Supervisor class
    /// </summary>
    [DataContract(Namespace = "http://www.mayo.edu/MayoMobile")]
    public class Supervisor : Mayo.Mobile.Staff.Model.Supervisor
    {
        /// <summary>
        /// Gets or sets the initials
        /// </summary>
        [DataMember]
        public string Initials { get; set; }
    }
}
