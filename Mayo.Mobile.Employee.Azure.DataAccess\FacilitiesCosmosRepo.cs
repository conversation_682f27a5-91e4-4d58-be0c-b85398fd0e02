﻿using Mayo.Mobile.Application.Azure.DataAccess.Models;
using Mayo.Mobile.Employee.Azure.DataAccess.Interfaces;
using Mayo.Mobile.Employee.Azure.DataAccess.Models;
using Mayo.Mobile.Employee.Location.Model;
using Microsoft.Azure.Cosmos;
using Microsoft.Extensions.Options;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace Mayo.Mobile.Employee.Azure.DataAccess
{
    public class FacilitiesCosmosRepo: IFacilitiesLocationRepo
    {
        private CosmosClient cosmosClient;
        private Database database;
        private Container facilitiesContainer;

        public FacilitiesCosmosRepo(IOptions<CosmosRepoOptions> cosmosRepoOptions, IOptions<FacilityCosmosOptions> configurationOptions)
        {
            var clientOptions = new CosmosClientOptions()
            {
                SerializerOptions = new CosmosSerializationOptions()
                {
                    IgnoreNullValues = true
                },
                ConnectionMode = ConnectionMode.Gateway
            };
            cosmosClient = new CosmosClient(cosmosRepoOptions.Value.ConnectionString, clientOptions);
            database = cosmosClient.GetDatabase(configurationOptions.Value.CosmosDatabaseName);
            facilitiesContainer = database.GetContainer(configurationOptions.Value.CosmosContainerName);
        }

        /// <summary>
        /// Returns the a list of facility's campus
        /// </summary>
        /// <returns></returns>
        public async Task<List<Campus>> GetCampusListAsync()
        {
            var locationDocument = await facilitiesContainer.ReadItemAsync<FacilityLocationDocument>($"campuses", new PartitionKey("mayolocations"));

            return locationDocument.Resource.Campuses;
        }

        /// <summary>
        /// Returns the a list of facility's buildings associated with the campusId
        /// </summary>
        /// <param name="campusId"></param>
        /// <returns></returns>
        public async Task<List<Building>> GetBuildingListAsync(string campusId)
        {
            var locationDocument = await facilitiesContainer.ReadItemAsync<FacilityLocationDocument>($"{campusId}", new PartitionKey(campusId));

            return locationDocument.Resource.Buildings;
        }

        /// <summary>
        /// Returns the a list of facility's floors associated with the campusId and buildingId
        /// </summary>
        /// <param name="campusId"></param>
        /// <param name="buildingId"></param>
        /// <returns></returns>
        public async Task<List<Floor>> GetFloorListAsync(string campusId, string buildingId)
        {
            var locationDocument = await facilitiesContainer.ReadItemAsync<FacilityLocationDocument>($"{campusId}{buildingId}", new PartitionKey(campusId));

            return locationDocument.Resource.Floors;
        }

        /// <summary>
        /// Returns the a list of facility's rooms associated with the campusId, buildingId and floorId
        /// </summary>
        /// <param name="campusId">campus identifier</param>
        /// <param name="buildingId">building identifier</param>
        /// <param name="floorId">floor identifier</param>
        /// <returns></returns>
        public async Task<List<Room>> GetRoomListAsync(string campusId, string buildingId, string floorId)
        {
            var locationDocument = await facilitiesContainer.ReadItemAsync<FacilityLocationDocument>($"{campusId}{buildingId}{floorId}", new PartitionKey(campusId));

            return locationDocument.Resource.Rooms;
        }

        /// <summary>
        /// Returns the a list of facility's campus
        /// </summary>
        /// <returns></returns>
        public async Task InsertCampusListAsync(List<Campus> campus)
        {
            var facilityLocationDocument = new FacilityLocationDocument()
            {
                Id = "campuses",
                PartitionKey = "mayolocations",
                LocationId = "CAMPUSLIST",
                LocationName = "CAMPUSLIST",
                LocationType = "CAMPUSLIST",
                LocationSubType = "CAMPUSLIST",
                Campuses = campus
            };
            await facilitiesContainer.UpsertItemAsync(facilityLocationDocument);
        }

        /// <summary>
        /// Returns the a list of facility's buildings associated with the campusId
        /// </summary>
        /// <param name="campusId"></param>
        /// <param name="campusName"></param>
        /// <returns></returns>
        public async Task InsertBuildingListAsync(string campusId, string campusName, List<Building> buildings)
        {
            var facilityLocationDocument = new FacilityLocationDocument()
            {
                Id = campusId,
                PartitionKey = campusId,
                LocationId = campusId,
                LocationName = campusName,
                LocationType = "Campus",
                LocationSubType = "Campus",
                Buildings = buildings
            };

            await facilitiesContainer.UpsertItemAsync(facilityLocationDocument);
        }

        /// <summary>
        /// Returns the a list of facility's floors associated with the campusId and buildingId
        /// </summary>
        /// <param name="campusId"></param>
        /// <param name="buildingId"></param>
        /// <param name="buildingName"></param>
        /// <param name="locationSubType"></param>
        /// <returns></returns>
        public async Task InsertFloorListAsync(string campusId, string buildingId, string buildingName, string locationSubType, List<Floor> floors)
        {
            var facilityLocationDocument = new FacilityLocationDocument()
            {
                Id = $"{campusId}{buildingId}",
                PartitionKey = campusId,
                LocationId = buildingId,
                LocationName = buildingName,
                LocationType = "Building",
                LocationSubType = locationSubType,
                Floors = floors
            };
            await facilitiesContainer.UpsertItemAsync(facilityLocationDocument);
        }

        /// <summary>
        /// Returns the a list of facility's rooms associated with the campusId, buildingId and floorId
        /// </summary>
        /// <param name="campusId">campus identifier</param>
        /// <param name="buildingId">building identifier</param>
        /// <param name="floorId">floor identifier</param>
        /// <param name="floorName">floor Name</param>
        /// <param name="locationSubType">floor subType</param>
        /// <returns></returns>
        public async Task InsertRoomListAsync(string campusId, string buildingId, string floorId, string floorName, string locationSubType, List<Room> rooms)
        {
            var facilityLocationDocument = new FacilityLocationDocument()
            {
                Id = $"{campusId}{buildingId}{floorId}",
                PartitionKey = campusId,
                LocationId = floorId,
                LocationName = floorName,
                LocationType = "Floor",
                LocationSubType = locationSubType,
                Rooms = rooms
            };
            await facilitiesContainer.UpsertItemAsync(facilityLocationDocument);
        }


    }
}
