﻿// -----------------------------------------------------------------------
// <copyright file="IPersonService.cs" company="Mayo Clinic">
//     Copyright © Mayo Foundation. All rights reserved.
// </copyright>
// -----------------------------------------------------------------------

namespace Mayo.Mobile.Employee.Person.Model
{
    using System;
    using System.Collections.Generic;
    using System.Threading.Tasks;

    /// <summary>
    /// Initializes a new instance of the IPersonService class.
    /// </summary>
    public interface IPersonService
    {
        /// <summary>
        /// Get the person identifiers from the email address
        /// </summary>
        /// <param name="email">The email address</param>
        /// <returns>The <see cref="Task{TResult}"/></returns>
        Task<(string PersonId, string LanId)> GetIdentifiersAsync(string email);

        /// <summary>
        /// Get the person identifiers from the email address
        /// </summary>
        /// <param name="emails">The email addresses</param>
        /// <returns>The <see cref="Task{TResult}"/></returns>
        Task<List<(string Email, string PersonId, string LanId)>> GetIdentifiersAsync(List<string> emails);

        /// <summary>
        /// Get the person photo
        /// </summary>
        /// <param name="personId">The person identifier</param>
        /// <returns>The <see cref="Task{TResult}"/></returns>
        Task<string> GetPhotoAsync(string personId);
    }
}
