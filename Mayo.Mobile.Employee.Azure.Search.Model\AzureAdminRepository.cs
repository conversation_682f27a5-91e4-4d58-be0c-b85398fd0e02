﻿//-----------------------------------------------------------------------
// <copyright file="AzureAdminRepository.cs" company="Mayo Foundation">
//     Copyright © Mayo Foundation. All rights reserved.
// </copyright>
//-----------------------------------------------------------------------

namespace Mayo.Mobile.Employee.Azure.Search.Model
{
    using System;
    using System.Collections.Generic;
    using System.Linq;
    using System.Text;
    using System.Threading.Tasks;
    using Microsoft.Azure.Search;
    using Microsoft.Azure.Search.Models;
    using Microsoft.Extensions.Options;

    /// <summary>
    /// Initializes a new instance of the AzureAdminRepository class.
    /// </summary>
    public class AzureAdminRepository : AzureRepository, ISearchAdminRepository
    {
        /// <summary>
        /// The administrator api key
        /// </summary>
        protected string adminApiKey = string.Empty;

        /// <summary>
        /// The <see cref="SearchServiceClient"/>
        /// </summary>
        protected SearchServiceClient serviceClient = null;

        /// <summary>
        /// The retry count
        /// </summary>
        protected const int retryCount = 5;

        public AzureAdminRepository(IOptions<AzureAdminRepoOptions> configurationOptions)
            : base(configurationOptions.Value.ServiceName, configurationOptions.Value.QueryApiKey)
        {
            adminApiKey = configurationOptions.Value.AdminApiKey;
        }

        /// <summary>
        /// Gets the <see cref="SearchServiceClient"/>
        /// </summary>
        private SearchServiceClient ServiceClient
        {
            get
            {
                if (serviceClient == null)
                {
                    serviceClient = new SearchServiceClient(serviceName, new SearchCredentials(adminApiKey));
                }

                return serviceClient;
            }
        }

        /// <summary>
        /// Create the index
        /// </summary>
        /// <typeparam name="T">The type of object to build the index for</typeparam>
        /// <param name="name">The name of the index</param>
        /// <param name="deleteIndexIfExists">Optional value to indicate whether the index should be deleted and re-added</param>
        /// <param name="suggesterName">The name of the <see cref="Suggester"/></param>
        /// <param name="suggesters">The fields for the <see cref="Suggester"/></param>
        /// <param name="scoringProfiles">The <see cref="ScoringProfile"/> names</param>
        /// <param name="scoringWeights">The <see cref="TextWeights"/> of the <see cref="ScoringProfile"/>s</param>
        /// <returns>The <see cref="Task{SearchIndex}"/></returns>
        public async Task<SearchIndex> CreateIndexAsync<T>(
            string name,
            bool deleteIndexIfExists = false,
            string suggesterName = null,
            string[] suggesters = null,
            string[] scoringProfiles = null,
            Dictionary<string, double>[] scoringWeights = null)
        {
            Microsoft.Azure.Search.Models.Index index = null;

            if (ServiceClient.Indexes.Exists(name))
            {
                if (deleteIndexIfExists)
                {
                    await ServiceClient.Indexes.DeleteAsync(name);
                }
                else
                {
                    index = await ServiceClient.Indexes.GetAsync(name);
                }
            }

            if (ServiceClient.Indexes.Exists(name) == false)
            {
                var definition = new Microsoft.Azure.Search.Models.Index()
                {
                    Name = name,
                    Fields = FieldBuilder.BuildForType<T>(),
                    Suggesters = new List<Suggester>
                    {
                        new Suggester(suggesterName, suggesters)
                    }
                };

                if (scoringProfiles != null
                    && scoringProfiles.Length > 0
                    && scoringWeights != null
                    && scoringWeights.Length == scoringProfiles.Length)
                {
                    definition.ScoringProfiles = new List<ScoringProfile>();
                    for (int i = 0; i < scoringProfiles.Length; i++)
                    {
                        definition.ScoringProfiles.Add(new ScoringProfile(scoringProfiles[i], new TextWeights(scoringWeights[i])));

                        /*
                        definition.ScoringProfiles.Add(
                            new ScoringProfile(
                                scoringProfiles[i],
                                new TextWeights(scoringWeights[i]),
                                new List<ScoringFunction> { new ScoringFunction("createDatetime", 10, ScoringFunctionInterpolation.Quadratic) }));
                        */
                    }
                }

                index = await ServiceClient.Indexes.CreateAsync(definition);
            }

            return new SearchIndex { Name = index.Name };
        }

        /// <summary>
        /// Upload content to the index for removing from the search
        /// </summary>
        /// <typeparam name="T">The type of content to updload</typeparam>
        /// <param name="name">The name of the index</param>
        /// <param name="item">The item to upload to the index</param>
        /// <returns>The <see cref="Task"/></returns>
        public async Task RemoveAsync<T>(string name, T item) where T : class
        {
            searchClient = searchClient ?? new SearchIndexClient(serviceName, name, new SearchCredentials(adminApiKey));
            searchClient = searchClient.SearchCredentials.ApiKey == queryApiKey
                ? new SearchIndexClient(serviceName, name, new SearchCredentials(adminApiKey))
                : searchClient;

            var actions = new IndexAction<T>[] { IndexAction.Delete(item) };
            await UploadAsync<T>(searchClient, IndexBatch.New(actions), 0);
        }

        /// <summary>
        /// Upload content to the index for searching
        /// </summary>
        /// <typeparam name="T">The type of content to updload</typeparam>
        /// <param name="name">The name of the index</param>
        /// <param name="list">The list to upload to the index</param>
        /// <returns>The <see cref="Task"/></returns>
        public async Task UploadAsync<T>(string name, IEnumerable<T> list) where T : class
        {
            searchClient = searchClient ?? new SearchIndexClient(serviceName, name, new SearchCredentials(adminApiKey));
            searchClient = searchClient.SearchCredentials.ApiKey == queryApiKey
                ? new SearchIndexClient(serviceName, name, new SearchCredentials(adminApiKey))
                : searchClient;
            await UploadAsync<T>(searchClient, IndexBatch.MergeOrUpload<T>(list), 0);
        }

        /// <summary>
        /// Upload content to the index for searching with retry logic
        /// </summary>
        /// <typeparam name="T">The type of content to updload</typeparam>
        /// <param name="searchClient">The <see cref="ISearchIndexClient"/></param>
        /// <param name="batch">The <see cref="IndexBatch{T}"/></param>
        /// <param name="count">The count of the times the upload has been attempted</param>
        /// <returns>The <see cref="Task"/></returns>
        private async Task UploadAsync<T>(ISearchIndexClient searchClient, IndexBatch<T> batch, int count) where T : class
        {
            try
            {
                await searchClient.Documents.IndexAsync<T>(batch);
            }
            catch (IndexBatchException e)
            {
                // we will try to index n number times and give up if it still doesn't work.
                if (count < retryCount)
                {
                    batch = e.FindFailedActionsToRetry<T>(batch, x => x.ToString());
                    count = count++;
                    await Task.Delay(count * 2 * 1000);
                    await UploadAsync<T>(searchClient, batch, count);
                }
                else
                {
                    throw new Exception("IndexBatchException: Indexing Failed for some documents.");
                }
            }
        }

        /// <summary>
        /// This code added to correctly implement the disposable pattern.
        /// </summary>
        /// <param name="disposing">A value indicating whether the class is being disposed</param>
        protected override void Dispose(bool disposing)
        {
            if (!disposedValue)
            {
                if (disposing)
                {
                    if (serviceClient != null)
                    {
                        serviceClient.Dispose();
                    }
                }
            }

            //// dispose objects in base class
            base.Dispose();
        }
    }
}