﻿//-----------------------------------------------------------------------
// <copyright file="Floor.cs" company="Mayo Foundation">
//     Copyright © Mayo Foundation. All rights reserved.
// </copyright>
//-----------------------------------------------------------------------

namespace Mayo.Mobile.Employee.Location.Model
{
    using Newtonsoft.Json;
    using System.Collections.Generic;

    /// <summary>
    /// Initializes a new instance of the Floor class.
    /// </summary>
    public class Floor : FacilityLocation
    {

        public Floor() : base() { }

        /// <summary>
        /// Gets or sets the list of rooms
        /// </summary>
        [JsonIgnore]
        public List<Room> Rooms { get; set; }
    }

    public class FloorComparer : IComparer<Floor>
    {
        public int Compare(Floor floor1, Floor floor2)
        {
            return floor1.Id.CompareTo(floor2.Id);
        }
    }
}
