﻿//-----------------------------------------------------------------------
// <copyright file="IdentityController.cs" company="Mayo Foundation">
//     Copyright © Mayo Foundation. All rights reserved.
// </copyright>
//-----------------------------------------------------------------------

namespace Mayo.Mobile.Employee.Web.Api.Controllers
{
    using System;
    using System.Collections.Generic;
    using System.IdentityModel.Tokens.Jwt;
    using System.Linq;
    using System.Net;
    using System.Security.Claims;
    using System.Security.Cryptography;
    using System.Security.Principal;
    using System.Threading.Tasks;
    using Mayo.Mobile.Employee.Identity.Model;
    using Mayo.Mobile.Employee.Model;
    using Mayo.Mobile.Employee.Web.Api.Model;
    using Mayo.Mobile.Employee.Web.Api.Models;
    using Mayo.Mobile.Logging.Interfaces;
    using Mayo.Mobile.Logging.Model;
    using Microsoft.AspNetCore.Authorization;
    using Microsoft.AspNetCore.Mvc;
    using Microsoft.Extensions.Options;
    using Microsoft.IdentityModel.Tokens;
    using Newtonsoft.Json.Linq;

    /// <summary>
    /// Initializes a new instance of the IdentityController class.
    /// </summary>
    [Authorize]
    [ApiVersion("1.0")]
    [ApiController]
    [Route("api/[controller]")]
    [Route("api/v{version:apiVersion}/[controller]")]
    public class IdentityController : ControllerBase
    {

        private IIdentityService IdentityService;

        private IUserRepository UserRepo;
        private IMayoMobileLogger MayoMobileLogger;

        private JWTOptions ConfigurationOptions;
        /// <summary>
        /// Generate a refresh token randomly
        /// </summary>
        private readonly Func<string> generateRefreshToken = () =>
        {
            var randomNumber = new byte[128];
            using (var rng = RandomNumberGenerator.Create())
            {
                rng.GetBytes(randomNumber);
                return Convert.ToBase64String(randomNumber);
            }
        };

        public IdentityController (IIdentityService identityService, IUserRepository userRepo, IMayoMobileLogger mayoMobileLogger, IOptions<JWTOptions> configurationOptions)
        {
            IdentityService = identityService;
            UserRepo = userRepo;
            MayoMobileLogger = mayoMobileLogger;
            ConfigurationOptions = configurationOptions.Value;
        }

        /// <summary>
        /// Authenticate the username and password
        /// </summary>
        /// <param name="r">The <see cref="LogonRequest"/></param>
        /// <returns>The <see cref="Task{TResult}"/></returns>
        [AllowAnonymous]
        [Obsolete]
        [HttpPost, Route("Logon")]
        [ProducesResponseType (typeof (Mayo.Mobile.Employee.Model.Identity), 200)]
        [ProducesResponseType (400)]
        public async Task<IActionResult> LogonAsync([FromBody] LogonRequest r)
        {
            Identity i;
            if (r.UserName.Equals("mothermarymoes", StringComparison.OrdinalIgnoreCase) && r.Password.Equals("Fr@ncis"))
            {
                IIdentityService mockService = new MockIdentityService();
                i = await mockService.LogonAsync(r.UserName, r.Password);
            }
            else
            {
                i = await IdentityService.LogonAsync(r.UserName, r.Password);
            }
            //Adding To HttpContext to be retrieved by middleware if needed
            Request.HttpContext.Items.Add("UserId", i.UserId);
            var d = DateTimeOffset.UtcNow.AddHours(24);
            var t = GenerateToken(i.UserId, r.UserName, i.FullName, i.Email, d, i.PasswordUpdateDate, ConfigurationOptions.SigningKey, ConfigurationOptions.SigningIssuer);
            i.AccessToken = t;
            i.ExpirationDate = d.ToString("o");
            i.RefreshToken = generateRefreshToken();

            await UserRepo.SaveAsync(i);

            return Ok (i);

            static string GenerateToken (string userId, string userName, string name, string email, DateTimeOffset time, string passwordUpdateDate, string signingKey, string tokenIssuer)
            {
                // TODO: move value out of code
                byte[] key = Convert.FromBase64String (signingKey);

                var token = new JwtSecurityToken (
                    issuer: tokenIssuer,
                    audience: "Employee",
                    expires : time.DateTime,
                    claims : new List<Claim>
                    {
                        new Claim(ClaimTypes.NameIdentifier, userId),
                        new Claim(ClaimTypes.Name, name),
                        new Claim(ClaimTypes.Email, email ?? string.Empty),
                        new Claim("PasswordUpdateDate", passwordUpdateDate, null) 
                    },
                    signingCredentials : new SigningCredentials(new SymmetricSecurityKey (key), SecurityAlgorithms.HmacSha256Signature));

                return new JwtSecurityTokenHandler().WriteToken (token);
            }
        }

        /// <summary>
        /// Get the token and identity based on user credentials or tokens
        /// </summary>
        /// <param name="authorization">The Authorization header</param>
        /// <param name="s">The <see cref="Status"/></param>
        /// <returns>The <see cref="Task{TResult}"/></returns>
        [AllowAnonymous]
        [HttpPost, Route("Token")]
        [ProducesResponseType(typeof(Identity), 200)]
        [ProducesResponseType(400)]
        public async Task<IActionResult> TokenAsync([FromHeader] string authorization, [FromBody] LogonRequest r)
        {
            Identity i;
            if (string.IsNullOrEmpty(authorization))
            {
                if (r.UserName.Equals("mothermarymoes", StringComparison.OrdinalIgnoreCase) && r.Password.Equals("Fr@ncis"))
                {
                    IIdentityService mockService = new MockIdentityService();
                    i = await mockService.LogonAsync(r.UserName, r.Password);
                }
                else
                {
                    i = await IdentityService.LogonAsync(r.UserName, r.Password);
                }
                //Adding To HttpContext to be retrieved by middleware if needed
                Request.HttpContext.Items.Add("UserId", i.UserId);
            }
            else
            {
                var accessToken = authorization.GetAccessToken();

                //// validate access token
                var p = GetPrincipalFromToken(accessToken, ConfigurationOptions.SigningKey, ConfigurationOptions.SigningIssuer);

                //// validate token and refresh token
                i = await UserRepo.IdentityAsync(User.Identity.GetUserId(), accessToken, r.RefreshToken);
                
                DateTime dt = p.GetPasswordUpdateDate();
                if (dt.Date.AddMonths(6) < DateTime.UtcNow.Date)
                {
                    throw new SecurityTokenExpiredException("The authenticated person needs to update their password.");
                }

                var lastLogonTime = await UserRepo.LastLogonTimeAsync(User.Identity.GetUserId());
                if (lastLogonTime.DateTime < dt)
                {
                    throw new SecurityTokenExpiredException("The authenticated person has done a password change and needs to re-authenticate.");
                }

                if (User.Identity.GetUserId().Equals("00000001"))
                {
                    i.PasswordUpdateDate = DateTime.UtcNow.AddDays(-7).ToString("o");
                }
                else
                {
                    //// validate user person id in AD is valid and update user properties of identity
                    var identity1 = await IdentityService.TokenAsync(accessToken);
                    i.PasswordUpdateDate = identity1.PasswordUpdateDate;
                }

                //// invalidate refresh token/access token
                i.IsActive = false;
                await UserRepo.SaveAsync(i);
            }

            var d = DateTimeOffset.UtcNow.AddHours(24);
            var t = GenerateToken(i.UserId, r.UserName, i.FullName, i.Email, d, i.PasswordUpdateDate, ConfigurationOptions.SigningKey, ConfigurationOptions.SigningIssuer);
            i.IsActive = true;
            i.AccessToken = t;
            i.ExpirationDate = d.ToString("o");
            i.RefreshToken = generateRefreshToken();

            await UserRepo.SaveAsync(i);
            await UserRepo.SaveAsync(i.UserId, string.IsNullOrEmpty(authorization) ? "LOGON" : "REFRESH" , i.AccessToken, i.RefreshToken);

            return Ok(i);

            static string GenerateToken(string userId, string userName, string name, string email, DateTimeOffset time, string passwordUpdateDate, string signingKey, string tokenIssuer)
            {
                // TODO: move value out of code
                byte[] key = Convert.FromBase64String(signingKey);


                var token = new JwtSecurityToken(
                    issuer: tokenIssuer,
                    audience: "Employee",
                    expires: time.DateTime,
                    claims: new List<Claim>
                    {
                        new Claim (ClaimTypes.NameIdentifier, userId),
                        new Claim (ClaimTypes.Name, name),
                        new Claim (ClaimTypes.Email, email ?? string.Empty),
                        new Claim("PasswordUpdateDate", passwordUpdateDate, null)
                    },
                    signingCredentials: new SigningCredentials(new SymmetricSecurityKey(key), SecurityAlgorithms.HmacSha256Signature));

                return new JwtSecurityTokenHandler().WriteToken(token);
            }

            static ClaimsPrincipal GetPrincipalFromToken(string token, string signingKey, string tokenIssuer)
            {
                try
                {
                    var tokenValidationParameters = new TokenValidationParameters
                    {
                        RequireExpirationTime = false,
                        RequireSignedTokens = true,
                        ValidateLifetime = false,
                        ValidateAudience = true,
                        ValidateIssuer = true,

                        ValidAudience = "Employee",
                        ValidIssuer = tokenIssuer,
                        IssuerSigningKeys = new List<SecurityKey>
                        {
                            // TODO: move string value out of code to keyvault
                            { new SymmetricSecurityKey (Convert.FromBase64String(signingKey)) }
                        }
                    };

                    var tokenHandler = new JwtSecurityTokenHandler();
                    var principal = tokenHandler.ValidateToken(token, tokenValidationParameters, out SecurityToken securityToken);
                    if (!(securityToken is JwtSecurityToken jwtSecurityToken))
                    //// || !jwtSecurityToken.Header.Alg.Equals(SecurityAlgorithms.HmacSha256Signature, StringComparison.InvariantCultureIgnoreCase))
                    {
                        throw new SecurityTokenException("Invalid token");
                    }

                    return principal;
                }
                catch (Exception ex)
                {
                    throw new SecurityTokenException(ex.Message);
                }
            }
        }

        /// <summary>
        /// Logoff the user
        /// </summary>
        /// <param name="authorization">The Authorization header</param>
        /// <returns>The <see cref="Task{TResult}"/></returns>
        [HttpPost, Route("Logoff")]
        [ProducesResponseType(200)]
        public async Task<IActionResult> LogoffAsync([FromHeader] string authorization, [FromBody] IdentityRequest r)
        {
            var accessToken = authorization.GetAccessToken();
            var userId = User.Identity.GetUserId();

            //// validate token and refresh token
            var i = await UserRepo.IdentityAsync(userId, accessToken, r.RefreshToken);

            //// invalidate access token
            i.IsActive = false;
            await UserRepo.SaveAsync(i);

            return Ok();
        }

        /// <summary>
        /// Get the user status
        /// </summary>
        /// <param name="authorization">The Authorization header</param>
        /// <returns>The <see cref="Task{TResult}"/></returns>
        [HttpGet, Route("Status")]
        [ProducesResponseType(200)]
        public async Task<IActionResult> StatusAsync([FromHeader] string authorization)
        {
            var userId = User.Identity.GetUserId();
            var status = await UserRepo.StatusAsync(userId);

            return Ok(status);
        }

        /// <summary>
        /// Save the user status
        /// </summary>
        /// <param name="authorization">The Authorization header</param>
        /// <param name="s">The <see cref="Mayo.Mobile.Employee.Model.Status"/></param>
        /// <returns>The <see cref="Task{TResult}"/></returns>
        [HttpPost, Route("Status/Update")]
        [ProducesResponseType(200)]
        public async Task<IActionResult> UpdateStatusAsync([FromHeader] string authorization, [FromBody] Mayo.Mobile.Employee.Model.Status s)
        {
            var userId = User.Identity.GetUserId();

            s.IsActive = string.IsNullOrEmpty(s.Description) == false || string.IsNullOrEmpty(s.Emoji) == false
                ? true
                : false;

            await UserRepo.SaveAsync(userId, s);

            return Ok();
        }

        /// <summary>
        /// Save the information if the user is having trouble signing in
        /// </summary>
        /// <param name="j">The <see cref="JObject"/></param>
        /// <returns>The <see cref="Task{TResult}"/></returns>
        [AllowAnonymous]
        [HttpPost, Route("TroubleSigningIn")]
        [ProducesResponseType(200)]
        [ProducesResponseType(404)]
        public async Task<IActionResult> TroubleSigningInAsync([FromBody] JObject j)
        {
            _ = (string)j["ApplicationId"];
            var username = (string)j["UserName"];
            var email = (string)j["Email"];
            var deviceId = (string)j["DeviceId"];

            //// TODO: have function process the feedback message to trouble signing in flow and in separate table
            await MayoMobileLogger.LogFeedbackAsync(username, email, deviceId, "TroubleSigningIn", string.Empty);
            return Ok();
        }
    }
}