﻿// -----------------------------------------------------------------------
// <copyright file="NewsCenterRepository.cs" company="Mayo Clinic">
//     Copyright © Mayo Foundation. All rights reserved.
// </copyright>
// -----------------------------------------------------------------------

namespace Mayo.Mobile.Employee.Content.News.Model
{
    using System;
    using System.Collections.Generic;
    using System.Linq;
    using System.Net.Http;
    using System.Text;
    using System.Threading.Tasks;
    using System.Collections.Concurrent;
    using System.Threading;
    using System.Net;
    using Mayo.Mobile.Employee.Content.Model;
    using Newtonsoft.Json;
    using Microsoft.Extensions.Options;
    using Mayo.Mobile.Employee.Content.News.Model.Models;

    /// <summary>
    /// Initializes a new instance of the NewsCenterRepository class.
    /// </summary>
    public class NewsCenterRepository : INewsCenterRepository
    {
        /// <summary>
        /// The consumer key
        /// </summary>
        protected string consumerKey = string.Empty;

        /// <summary>
        /// The secret key
        /// </summary>
        protected string secretKey = string.Empty;
        private string BaseUrl;

        protected HttpClient Client { get; private set; }

        /// <summary>
        /// Create the hex string
        /// </summary>
        protected Func<byte[], bool, string> ToHex = (a, b) =>
        {
            StringBuilder result = new StringBuilder(a.Length * 2);

            for (int i = 0; i < a.Length; i++)
            {
                result.Append(a[i].ToString(b ? "X2" : "x2"));
            }

            return result.ToString();
        };

        /// <summary>
        /// Initializes a new instance of the NewsCenterRepository class.
        /// </summary>
        /// <param name="consumerKey">The consumer key</param>
        /// <param name="secretKey">The secret key</param>
        /// <param name="client">The <see cref="HttpClient"/></param>
        public NewsCenterRepository(HttpClient client, IOptions<NewsContentServiceOptions> configurationOptions)
        {
            consumerKey = configurationOptions.Value.ConsumerKey;
            secretKey = configurationOptions.Value.SecretKey;
            BaseUrl = configurationOptions.Value.ContentUrl;
            Client = client;
        }

        /// <summary>
        /// Post a comment
        /// </summary>
        /// <param name="contentId">The content identifier</param>
        /// <param name="comment">The plain text comment</param>
        /// <param name="threadId">The thread identifier</param>
        /// <param name="email">The user email</param>
        /// <param name="replyToId">The optional comment to reply to</param>
        /// <returns>The <see cref="Task"/></returns>
        public async Task PostCommentAsync(string contentId, string comment, string threadId, string email, string replyToId = null)
        {
            var optionalReplyTo = replyToId != null ? $"&reply_to={replyToId}" : "";
            //// The '/' before the query string parameters is required
            var url = $"{BaseUrl}api-v2/comment/?entity_type=wordpress_post&entity_id={contentId}&thread_id={threadId}&user_email={WebUtility.UrlEncode(email)}&content={WebUtility.UrlEncode(comment)}{optionalReplyTo}";
            var response = await PostRequestClientAsync<PostedComment>(url);
            if (response.Error == 1)
            {
                throw new Exception(string.Join(",", response.ErrorMessage));
            }
        }

        /// <summary>
        /// Get the lists of comments
        /// </summary>
        /// <param name="id"></param>
        /// <param name="pageNumber"></param>
        /// <returns></returns>
        public async Task<List<Comment>> GetCommentsAsync(string id, int? pageNumber = null)
        {
            ConcurrentBag<CommentsListItem> fullCommentList = new ConcurrentBag<CommentsListItem>();
            var maxPages = 1;

            var url = $"{BaseUrl}api-v2/comment/?entity_type=wordpress_post&entity_id={id}&page={pageNumber ?? 1}";
            var response = await GetRequestClientAsync<CommentsListContent>(url);
            if (response.Error == 1)
            {
                throw new Exception(string.Join(",", response.ErrorMessage));
            }

            maxPages = response.Content.MaxPages;
            response.Content.Comments.ForEach(x => { fullCommentList.Add(x); });

            if (maxPages > 1 && pageNumber == null)
            {
                var pages = Enumerable.Range(2, maxPages - 1);

                SemaphoreSlim throttler = new SemaphoreSlim(System.Environment.ProcessorCount, System.Environment.ProcessorCount);

                await Task.WhenAll(pages.Select(async x =>
                {
                    await throttler.WaitAsync();
                    try
                    {
                        var url1 = $"{BaseUrl}api-v2/comment/?entity_type=wordpress_post&entity_id={id}&page={x}";
                        var response1 = await GetRequestClientAsync<CommentsListContent>(url1);
                        if (response1.Error == 1)
                        {
                            throw new Exception(string.Join(",", response.ErrorMessage));
                        }

                        response1.Content.Comments.ForEach(y => { fullCommentList.Add(y); });
                    }
                    finally
                    {
                        throttler.Release();
                    }
                }));
            }

            //// supports comment and sub comment. not comments on the sub comment
            var list = (from x in fullCommentList.ToList()
                        group x by x.NestedOrder into g
                        orderby g.Key descending, g.First().Id descending
                        select new Comment
                        {
                            ContentId = id,
                            ThreadId = g.Where(y => y.Id.Equals(g.Key)).Select(y => y.ThreadId).FirstOrDefault(),
                            Text = g.Where(y => y.Id.Equals(g.Key)).Select(y => System.Web.HttpUtility.HtmlDecode(y.Content)).FirstOrDefault(),
                            Id = g.Where(y => y.Id.Equals(g.Key)).Select(y => y.Id).FirstOrDefault(),
                            Date = g.Where(y => y.Id.Equals(g.Key)).Select(y => $"{y.DateCreated}T{y.TimeCreated}").FirstOrDefault(),
                            Commenter = new Employee.Model.Person(
                                 g.Where(y => y.Id.Equals(g.Key)).Select(y => y.User.Email).FirstOrDefault(),
                                 g.Where(y => y.Id.Equals(g.Key)).Select(y => y.User.DisplayName).FirstOrDefault(),
                                 string.Empty),
                            Comments = g.Where(y => y.Id.Equals(g.Key) == false).OrderBy(y => y.Id).Select(y =>
                                new Comment
                                {
                                    ContentId = id,
                                    ThreadId = y.ThreadId,
                                    Text = System.Web.HttpUtility.HtmlDecode(y.Content),
                                    Id = y.Id,
                                    Date = $"{y.DateCreated}T{y.TimeCreated}",
                                    Commenter = new Employee.Model.Person(y.User.Email, y.User.DisplayName, string.Empty),
                                    Comments = new List<Comment>()
                                }).ToList()
                        }).ToList();

            return list;

            /*
            response.Content.Comments.ForEach(comment => {
                var newComment = new Comment
                {
                    Id = comment.Id,
                    ThreadId = comment.ThreadId,
                    Commenter = new Employee.Model.Person
                    {
                        Name = comment.User.DisplayName
                    },
                    Text = comment.Content,
                    Date = comment.DateCreated,
                    Comments = new List<Comment>()
                };

                // if nestedOrder == the Id then it is the parent comment
                // and should be added to the main list
                // else we need to add comment to the dictionarys list
                if (comment.Id == comment.NestedOrder)
                {
                    fullCommentList.Add(newComment);
                }
                else
                {
                    // initialize list set ref of tempList to what is in the dictionary 
                    // anything we add will be added in the dictionary
                    if (!listDictionary.TryGetValue(comment.NestedOrder, out List<Comment> tempList))
                    {
                        tempList = new List<Comment>();
                        listDictionary[comment.NestedOrder] = tempList;
                    }
                    tempList.Add(newComment);
                }
            });
            */
        }

        /// <summary>
        /// Get the user name
        /// </summary>
        /// <param name="userEmail">The email address</param>
        /// <returns>The <see cref="Task{TResult}"/></returns>
        public async Task<string> GetUserAsync(string userEmail)
        {
            var urlEnding = $"{BaseUrl}api-v2/user/single/?user_email={WebUtility.UrlEncode(userEmail)}";
            var response = await GetRequestClientAsync<UserContent>(urlEnding);
            if (response.Error == 1)
            {
                throw new Exception(string.Join(",", response.ErrorMessage));
            }

            return response.Content.User.UserName;
        }

        /// <summary>
        /// Create a user email address
        /// </summary>
        /// <param name="userEmail">The user email address</param>
        /// <param name="displayName">The display name</param>
        /// <returns>The <see cref="Task{TResult}"/></returns>
        public async Task<string> CreateUserAsync(string userEmail, string displayName = null)
        {
            _ = displayName != null ? $"&display_Name={displayName}" : "";
            var urlEnding = $"{BaseUrl}api-v2/user/single/?user_email={WebUtility.UrlEncode(userEmail)}";
            var response = await PostRequestClientAsync<UserContent>(urlEnding);
            if (response.Error == 1)
            {
                throw new Exception(string.Join(",", response.ErrorMessage));
            }

            return response.Content.User.UserName;
        }

        /// <summary>
        /// Make a GET request to a url
        /// </summary>
        /// <typeparam name="T">The <see cref="T"/></typeparam>
        /// <param name="url">The url</param>
        /// <returns>The <see cref="Task{TResult}"/></returns>
        protected async Task<NewsCenterResponse<T>> GetRequestClientAsync<T>(string url)
        {
            using (var requestMessage = CreateRequestMessage(url, HttpMethod.Get))
            {
                return await SendRequestAsync<T>(requestMessage);
            }
        }

        /// <summary>
        /// Make a POST request to a url
        /// </summary>
        /// <typeparam name="T">The <see cref="T"/></typeparam>
        /// <param name="url">The url</param>
        /// <returns>The <see cref="Task{TResult}"/></returns>
        protected async Task<NewsCenterResponse<T>> PostRequestClientAsync<T>(string url)
        {
            using (var requestMessage = CreateRequestMessage(url, HttpMethod.Post))
            {
                return await SendRequestAsync<T>(requestMessage);
            }
        }

        /// <summary>
        /// Send the request
        /// </summary>
        /// <typeparam name="T">The <see cref="T"/></typeparam>
        /// <param name="requestMessage">The <see cref="HttpRequestMessage"/></param>
        /// <returns>The <see cref="Task{TResult}"/></returns>
        protected async Task<NewsCenterResponse<T>> SendRequestAsync<T>(HttpRequestMessage requestMessage)
        {
            using (var response = await Client.SendAsync(requestMessage))
            {
                response.EnsureSuccessStatusCode();

                var content = Encoding.UTF8.GetString(await response.Content.ReadAsByteArrayAsync());
                return JsonConvert.DeserializeObject<NewsCenterResponse<T>>(content);
            }
        }

        /// <summary>
        /// Create the <see cref="HttpRequestMessage"/>
        /// </summary>
        /// <param name="url">The url</param>
        /// <param name="method">The <see cref="HttpMethod"/></param>
        /// <returns>The <see cref="HttpRequestMessage"/></returns>
        protected HttpRequestMessage CreateRequestMessage(string url, HttpMethod method)
        {
            var requestMessage = new HttpRequestMessage(method, url);

            var (Token, Time) = GetHeaderValues();

            requestMessage.Headers.Add("HTTP-X-CAREHUBS-CONSUMER-KEY", consumerKey);
            requestMessage.Headers.Add("HTTP-X-CAREHUBS-TIMESTAMP", Time);
            requestMessage.Headers.Add("HTTP-X-CAREHUBS-TOKEN", Token);

            return requestMessage;
        }

        /// <summary>
        /// Get the values for the request header
        /// </summary>
        /// <returns>The token and time</returns>
        protected (string Token, string Time) GetHeaderValues()
        {
            var time = DateTimeOffset.UtcNow;
            var temp = consumerKey + time.ToUnixTimeSeconds();
            string token = null;

            using (var hmac = new System.Security.Cryptography.HMACSHA256(Convert.FromBase64String(secretKey)))
            {
                // From string to byte array
                byte[] buffer = System.Text.Encoding.UTF8.GetBytes(temp);

                // Compute the hash of the input file.
                byte[] hashValue = hmac.ComputeHash(buffer);

                // Output in HEX
                token = ToHex(hashValue, false);

                return (token, time.ToString("s"));
            }
        }
    }
}
