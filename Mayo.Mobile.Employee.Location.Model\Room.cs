﻿//-----------------------------------------------------------------------
// <copyright file="Room.cs" company="Mayo Foundation">
//     Copyright © Mayo Foundation. All rights reserved.
// </copyright>
//-----------------------------------------------------------------------

using System.Collections.Generic;

namespace Mayo.Mobile.Employee.Location.Model
{

    /// <summary>
    /// Initializes a new instance of the Room class.
    /// </summary>
    public class Room : FacilityLocation
    {
        public Room() :base() {}
    }

    public class RoomComparer: IComparer<Room>
    {
        public int Compare(Room room1, Room room2)
        {
            return room1.Name.CompareTo(room2.Name);
        }
    }
}
