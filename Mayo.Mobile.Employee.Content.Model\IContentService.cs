﻿// -----------------------------------------------------------------------
// <copyright file="IContentService.cs" company="Mayo Clinic">
//     Copyright © Mayo Foundation. All rights reserved.
// </copyright>
// -----------------------------------------------------------------------

namespace Mayo.Mobile.Employee.Content.Model
{
    using System;
    using System.Collections.Generic;
    using System.IO;
    using System.Net.Http;
    using System.Text;
    using System.Threading.Tasks;
    using Mayo.Mobile.Employee.Model;

    /// <summary>
    /// Initializes a new instance of the IContentService class.
    /// </summary>
    public interface IContentService
    {
        /// <summary>
        /// Get the list of alerts
        /// </summary>
        /// <returns>The <see cref="Task{TResult}"/></returns>
        Task<Package> GetAlertsAsync();

        /// <summary>
        /// Gets the content list
        /// </summary>
        /// <param name="id">The category identifier</param>
        /// <param name="count">The number of items</param>
        /// <returns></returns>
        Task<List<Package>> GetListAsync(string id, int? count);

        /// <summary>
        /// Gets the content by id
        /// </summary>
        /// <param name="id">The content identifier</param>
        /// <param name="formatted">A value indicating whether the content should be formatted</param>
        /// <returns>The <see cref="Task{TResult}"/></returns>
        Task<Content> GetContentAsync(string id, bool formatted = true);

        /// <summary>
        /// Gets the image by name
        /// </summary>
        /// <param name="name">The name od the image</param>
        /// <returns>The <see cref="Task{TResult}"/></returns>
        Task<Stream> GetImageAsync(string name);

        /// <summary>
        /// Gets the favorites list
        /// </summary>
        /// <param name="userId">The user identifier</param>
        /// <param name="count">The number of items</param>
        /// <returns>The <see cref="Task{TResult}"/></returns>
        Task<List<Package>> GetFavoritesAsync(string userId, int? count);

        /// <summary>
        /// Add to the favorites list
        /// </summary>
        /// <param name="userId">The user identifier</param>
        /// <param name="categoryId">The category identifier</param>
        /// <param name="id">The content identifier</param>
        /// <returns>The <see cref="Task"/></returns>
        Task AddFavoriteAsync(string userId, string categoryId, string id);

        /// <summary>
        /// Delete from the favorites list
        /// </summary>
        /// <param name="userId">The user identifier</param>
        /// <param name="categoryId">The category identifier</param>
        /// <param name="id">The content identifier</param>
        /// <returns>The <see cref="Task"/></returns>
        Task DeleteFavoriteAsync(string userId, string categoryId, string id);

        /// <summary>
        /// Gets the list of comments
        /// </summary>
        /// <param name="id">The content identifier</param>
        /// <param name="pageNumber">The page number for paging if provided</param>
        /// <returns>The <see cref="Task{TResult}"/></returns>
        Task<List<Comment>> CommentsAsync(string id, string pageNumber = null);

        /// <summary>
        /// Add a comment to the thread
        /// </summary>
        /// <param name="contentId">The content identifier</param>
        /// <param name="threadId">The thread identifier</param>
        /// <param name="userId">The user identifier</param>
        /// <param name="comment">The plain text comment</param>
        /// <param name="replyId">The identifier of the comment to reply to</param>
        /// <returns>The <see cref="Task"/></returns>
        Task PostCommentAsync(string contentId, string threadId, string userId, string comment, string replyId = null);
    }
}
