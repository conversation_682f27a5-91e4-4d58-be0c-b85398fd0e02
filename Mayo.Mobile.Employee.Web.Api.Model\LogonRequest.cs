﻿// -----------------------------------------------------------------------
// <copyright file="LogonRequest.cs" company="Mayo Clinic">
//     Copyright © Mayo Foundation. All rights reserved.
// </copyright>
// -----------------------------------------------------------------------

namespace Mayo.Mobile.Employee.Web.Api.Model
{
    using System;
    using System.Runtime.Serialization;

    /// <summary>
    /// Initializes a new instance of the LogonRequest class.
    /// </summary>
    [DataContract(Namespace = "http://www.mayo.edu/MayoMobile")]
    public class LogonRequest : IdentityRequest
    {
        /// <summary>
        /// Gets or sets the type of the request
        /// </summary>
        [DataMember]
        public string RequestType { get; set; }

        /// <summary>
        /// Gets or sets the user name
        /// </summary>
        [DataMember]
        public string UserName { get; set; }

        /// <summary>
        /// Gets or sets the password
        /// </summary>
        [DataMember]
        public string Password { get; set; }

        /// <summary>
        /// Gets or sets the tag
        /// </summary>
        [DataMember]
        public string Tag { get; set; }
    }
}
