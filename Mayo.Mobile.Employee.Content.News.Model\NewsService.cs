﻿// -----------------------------------------------------------------------
// <copyright file="NewsService.cs" company="Mayo Clinic">
//     Copyright © Mayo Foundation. All rights reserved.
// </copyright>
// -----------------------------------------------------------------------

namespace Mayo.Mobile.Employee.Content.News.Model
{
    using System;
    using System.Collections.Generic;
    using System.Linq;
    using System.Text;
    using System.Threading.Tasks;
    using Mayo.Mobile.Employee.Content.Model;
    using Mayo.Mobile.Employee.Person.Model;

    /// <summary>
    /// Initializes a new instance of the NewsService class
    /// </summary>
    public class NewsService : ContentService, IContentService
    {
        /// <summary>
        /// The <see cref="IPersonService"/>
        /// </summary>
        private readonly IPersonService personService = null;

        /// <summary>
        /// The <see cref="INewsCenterRepository"/>
        /// </summary>
        private readonly INewsCenterRepository newsRepository = null;

        /// <summary>
        /// Initializes a new instance of the NewsService class.
        /// </summary>
        /// <param name="personService">The <see cref="IPersonService"/></param>
        /// <param name="repository">The <see cref="IContentStorageRepository"/></param>
        /// <param name="newsRepository">The <see cref="INewsCenterRepository"/></param>
        public NewsService(IPersonService personService, IContentStorageRepository repository, INewsCenterRepository newsRepository)
            : base(repository)
        {
            this.personService = personService;
            this.newsRepository = newsRepository;
        }

        /// <summary>
        /// Gets the list of comments
        /// </summary>
        /// <param name="id">The content identifier</param>
        /// <param name="pageNumber">The page number for paging if provided</param>
        /// <returns>The <see cref="Task{TResult}"/></returns>
        public override async Task<List<Comment>> CommentsAsync(string id, string pageNumber = null)
        {
            var page = pageNumber != null ? int.Parse(pageNumber) : new int?();
            var comments = await newsRepository.GetCommentsAsync(id, page);

            var emails = comments.SelectMany(x => x.Comments).Select(x => x.Commenter.Email)
                .Union(comments.Select(x => x.Commenter.Email))
                .Distinct(StringComparer.CurrentCultureIgnoreCase).ToList();

            var users = await personService.GetIdentifiersAsync(emails);

            comments.ForEach(x =>
            {
                var (Email, PersonId, LanId) = users.Where(y => y.Email.Equals(x.Commenter.Email, StringComparison.OrdinalIgnoreCase)).FirstOrDefault();
                if (string.IsNullOrEmpty(Email) == false)
                {
                    x.Commenter.Id = PersonId;
                }

                x.Comments.ForEach(y =>
                {
                    var u = users.Where(z => z.Email.Equals(y.Commenter.Email, StringComparison.OrdinalIgnoreCase)).FirstOrDefault();
                    if (string.IsNullOrEmpty(u.Email) == false)
                    {
                        y.Commenter.Id = u.PersonId;
                    }
                });
            });

            return comments;
        }

        /// <summary>
        /// Add a comment to the thread
        /// </summary>
        /// <param name="contentId">The content identifier</param>
        /// <param name="threadId">The thread identifier</param>
        /// <param name="userId">The user identifier</param>
        /// <param name="comment">The plain text comment</param>
        /// <param name="replyId">The identifier of the comment to reply to</param>
        /// <returns>The <see cref="Task"/></returns>
        public override async Task PostCommentAsync(string contentId, string threadId, string userId, string comment, string replyId = null)
        {
            if (string.IsNullOrEmpty(userId))
            {
                throw new Exception($"Invalid email: {userId}");
            }

            //// if already there it just returns it
            var newsUserId = await this.newsRepository.CreateUserAsync(userId);

            //// var user = await this.newsRepository.GetUserAsync(userId);
            await this.newsRepository.PostCommentAsync(contentId, comment, threadId, userId, replyId);
        }
    }
}
