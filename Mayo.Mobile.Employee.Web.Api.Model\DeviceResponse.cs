﻿//-----------------------------------------------------------------------
// <copyright file="DeviceResponse.cs" company="Mayo Foundation">
//     Copyright © Mayo Foundation. All rights reserved.
// </copyright>
//-----------------------------------------------------------------------

namespace Mayo.Mobile.Employee.Web.Api.Model
{
    using System.Runtime.Serialization;
    using Mayo.Mobile.Application.Models;

    /// <summary>
    /// Initializes a new instance of the DeviceResponse class.
    /// </summary>
    [DataContract(Namespace = "http://www.mayo.edu/MayoMobile")]
    public class DeviceResponse
    {
        /// <summary>
        /// Initializes a new instance of the DeviceResponse class.
        /// </summary>
        public DeviceResponse()
        {
        }

        /// <summary>
        /// Initializes a new instance of the DeviceResponse class.
        /// </summary>
        /// <param name="device">The <see cref="Device"/></param>
        /// <param name="update">The <see cref="ApplicationUpdate"/></param>
        public DeviceResponse(Device device, ApplicationUpdate update)
        {
            this.Device = device;
            this.Update = update;
        }

        /// <summary>
        /// Gets or sets the <see cref="Device"/>
        /// </summary>
        [DataMember]
        public Device Device { get; set; }

        /// <summary>
        /// Gets or sets the update information
        /// </summary>
        [DataMember]
        public ApplicationUpdate Update { get; set; }
    }
}
