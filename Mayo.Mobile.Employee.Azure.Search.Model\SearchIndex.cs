﻿//-----------------------------------------------------------------------
// <copyright file="SearchIndex.cs" company="Mayo Foundation">
//     Copyright © Mayo Foundation. All rights reserved.
// </copyright>
//-----------------------------------------------------------------------

namespace Mayo.Mobile.Employee.Azure.Search.Model
{
    using System;
    using System.Collections.Generic;
    using System.Linq;
    using System.Text;
    using System.Threading.Tasks;

    /// <summary>
    /// Initializes a new instance of the SearchIndex class.
    /// </summary>
    public class SearchIndex
    {
        /// <summary>
        /// Gets or sets the name of the index
        /// </summary>
        public string Name { get; set; }
    }
}
