﻿using Azure.Storage.Queues;
using Mayo.Mobile.Application.Azure.DataAccess.Models;
using Mayo.Mobile.Employee.Azure.DataAccess.Interfaces;
using Mayo.Mobile.Employee.Azure.DataAccess.Models;
using Mayo.Mobile.Employee.Location.Model;
using Microsoft.Extensions.Options;
using Newtonsoft.Json;
using System.Threading.Tasks;

namespace Mayo.Mobile.Employee.Azure.DataAccess
{
    public class FacilitiesQueueRepo: IWorkOrderQueueRepo
    {
        private string queueName;
        private string attachmentQueueName { get { return $"{queueName}attachments"; } }
        private QueueServiceClient QueueSvcClient;

        public FacilitiesQueueRepo(IOptions<StorageAccountRepoOptions> storageAccountRepoOptions, IOptions<FacilityQueueOptions> configurationOptions)
        {
            queueName = configurationOptions.Value.QueueName;
            var options = new QueueClientOptions() { MessageEncoding = QueueMessageEncoding.Base64 };
            QueueSvcClient = new QueueServiceClient(storageAccountRepoOptions.Value.ConnectionString, options);
        }

        /// <summary>
        /// Used to add the FacilityIssue to the WorkOrder Processing Queue
        /// </summary>
        /// <param name="issue"></param>
        /// <returns></returns>
        public async Task EnqueueWOAsync(FacilityIssue issue)
        {
            var workOrder = new FacilityWorkOrderQueueMessage()
            {
                Id = issue.Id,
                ReportedBy = issue.UserId,
                CampusId = issue.CampusId,
                BuildingId = issue.BuildingId,
                FloorId = issue.FloorId,
                RoomId = issue.RoomId,
                Comments = issue.Comments,
                Latitude = issue.ReportedLocation.Latitude,
                Longitude = issue.ReportedLocation.Longitude,
                PhotoIds = issue.PhotoIds
            };
            var jsonMessage = JsonConvert.SerializeObject(workOrder);
            var queueClient = await CreateGetQueueClientAsync(queueName);

            await queueClient.SendMessageAsync(jsonMessage);
        }

        /// <summary>
        /// Used to enqueue Attachments to the attachment Queue to be processed
        /// </summary>
        /// <param name="attachment"></param>
        /// <returns></returns>
        public async Task EnqueueWOAttachmentAsync(FacilityWorkOrderAttachment attachment)
        {
            var jsonMessage = JsonConvert.SerializeObject(attachment);
            var queueClient = await CreateGetQueueClientAsync(attachmentQueueName);

            await queueClient.SendMessageAsync(jsonMessage);
        }

        /// <summary>
        /// Used with Test Cases to test processing an Attachment
        /// </summary>
        /// <returns></returns>
        public async Task<FacilityWorkOrderAttachment> DequeueAttachmentAsync()
        {
            var queueClient = await CreateGetQueueClientAsync(attachmentQueueName);

            var message = await queueClient.ReceiveMessageAsync();
            var attachment = JsonConvert.DeserializeObject<FacilityWorkOrderAttachment>(message.Value.Body.ToString());
            return attachment;
        }

        private async Task<QueueClient> CreateGetQueueClientAsync(string queueName)
        {
            var queueClient = QueueSvcClient.GetQueueClient(queueName);
            await queueClient.CreateIfNotExistsAsync();
            return queueClient;
        }
    }
}
