﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>netstandard2.1</TargetFramework>
  </PropertyGroup>

  <ItemGroup>
    <Content Remove="C:\Users\<USER>\.nuget\packages\microsoft.azure.cosmos\3.16.0\contentFiles\any\netstandard2.0\ThirdPartyNotice.txt" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="Azure.Storage.Blobs" Version="12.8.1" />
    <PackageReference Include="Azure.Storage.Queues" Version="12.6.1" />
    <PackageReference Include="Mayo.Mobile.Application.Azure.DataAccess" Version="3.1.28" />
    <PackageReference Include="Microsoft.Azure.Cosmos" Version="3.17.1" />
    <PackageReference Include="Microsoft.Extensions.Http.Polly" Version="3.1.17" />
    <PackageReference Include="Microsoft.Extensions.Options" Version="3.1.17" />
    <PackageReference Include="Newtonsoft.Json" Version="13.0.1" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\Mayo.Mobile.Employee.Location.Model\Mayo.Mobile.Employee.Location.Model.csproj" />
  </ItemGroup>

</Project>
