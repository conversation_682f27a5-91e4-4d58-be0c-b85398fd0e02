﻿//-----------------------------------------------------------------------
// <copyright file="PersonViewModel.cs" company="Mayo Foundation">
//     Copyright © Mayo Foundation. All rights reserved.
// </copyright>
//-----------------------------------------------------------------------

namespace Mayo.Mobile.Employee.Web.Api.Model
{
    using System;
    using System.Collections.Generic;
    using System.Text;

    /// <summary>
    /// Initializes a new instance of the PersonViewModel class.
    /// </summary>
    public class PersonViewModel : Mayo.Mobile.Employee.Model.Person
    {
        /// <summary>
        /// Gets or sets the lan identifier
        /// </summary>
        public string LanId { get; set; }

        /// <summary>
        /// Gets or sets the redemption code
        /// </summary>
        public string RedemptionCode { get; set; }

        /// <summary>
        /// Gets or sets the redemption url
        /// </summary>
        public string RedemptionUrl { get; set; }
    }
}
