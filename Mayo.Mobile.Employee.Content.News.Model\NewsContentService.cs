﻿// -----------------------------------------------------------------------
// <copyright file="NewsContentService.cs" company="Mayo Clinic">
//     Copyright © Mayo Foundation. All rights reserved.
// </copyright>
// -----------------------------------------------------------------------

namespace Mayo.Mobile.Employee.Content.News.Model
{
    using System;
    using System.Collections.Concurrent;
    using System.Collections.Generic;
    using System.IO;
    using System.Linq;
    using System.Net;
    using System.Net.Http;
    using System.Text;
    using System.Threading;
    using System.Threading.Tasks;
    using System.Threading.Tasks.Dataflow;
    using Mayo.Mobile.Application.Azure.DataAccess.Interfaces;
    using Mayo.Mobile.Employee.Content.Model;
    using Mayo.Mobile.Employee.Content.News.Model.Interfaces;
    using Mayo.Mobile.Employee.Content.News.Model.Models;
    using Mayo.Mobile.Employee.Model;
    using Microsoft.Azure.Cosmos.Table;
    using Microsoft.Extensions.Options;
    using Newtonsoft.Json.Linq;

    /// <summary>
    /// Initializes a new instance of the NewsContentService class.
    /// </summary>
    public class NewsContentService : INewsContentService
    {
        /// <summary>
        /// The content list service url
        /// </summary>
        private readonly string contentUrl = string.Empty;

        /// <summary>
        /// The consumer key
        /// </summary>
        private readonly string consumerKey = string.Empty;

        /// <summary>
        /// The secret key
        /// </summary>
        private readonly string secretKey = string.Empty;

        /// <summary>
        /// The <see cref="Action{T}"/>
        /// </summary>
        private readonly Action<string> callback = null;

        /// <summary>
        /// The <see cref="HttpClient"/>
        /// </summary>
        private readonly HttpClient webClient;

        /// <summary>
        /// The cached list of categories
        /// </summary>
        private readonly ConcurrentDictionary<string, string> categories = new ConcurrentDictionary<string, string>();
        private string Env; 
        /// <summary>
        /// The list of saved images
        /// </summary>
        private readonly ConcurrentBag<string> savedImages = new ConcurrentBag<string>();

        private IContentStorageRepository ContentStorageRepo;
        private ITableStorageRepository TableStorageRepo;
        /// <summary>
        /// Function to map Editorial to PackageItem
        /// </summary>
        private readonly Func<Editorial, string, string, string, string, PackageItem> transform = (x, categoryId, categoryName, type, image) =>
        {
            return new PackageItem
            {
                Id = x.Id,
                Category = new Category
                {
                    Id = categoryId,
                    Name = categoryName,
                    Type = type
                },
                Name = WebUtility.HtmlDecode(x.Title),
                Description = WebUtility.HtmlDecode(x.Excerpt),
                Type = type.ToUpper(),
                Images = new List<Element>
                {
                    new Element("IMAGELISTURL", image),
                    new Element("IMAGEHEADERURL", image),
                },
                Author = new Person { Name = x.Author },
                Date = DateTime.Parse(x.PostDate).ToString("yyyyMMdd"),
                SortDate = DateTime.Parse(x.PostDate).ToString("s")
            };
        };

        /// <summary>
        /// Create a hex string from byte array
        /// </summary>
        private readonly Func<byte[], bool, string> ToHex = (a, b) =>
        {
            StringBuilder result = new StringBuilder(a.Length * 2);

            for (int i = 0; i < a.Length; i++)
            {
                result.Append(a[i].ToString(b ? "X2" : "x2"));
            }

            return result.ToString();
        };

        /// <summary>
        /// Initializes a new instance of the NewsContentService class.
        /// Documention on api:
        /// https://newscenter.mayo.edu/api-v2/docs/
        /// </summary>
        /// <param name="applicationId">The application idenification</param>
        /// <param name="contentUrl"></param>
        /// <param name="consumerKey"></param>
        /// <param name="secretKey"></param>
        /// <param name="repository"></param>
        //public NewsContentService(HttpClient httpClient, IOptions<NewsContentServiceOptions> configurationOptions, IContentStorageRepository contentStorageRepo, ITableStorageRepository tableStorageRepo, Action<string> callback = null)
        //{
        //    contentUrl = configurationOptions.Value.ContentUrl;
        //    consumerKey = configurationOptions.Value.ConsumerKey;
        //    secretKey = configurationOptions.Value.SecretKey;
        //    ContentStorageRepo = contentStorageRepo;
        //    TableStorageRepo = tableStorageRepo;
        //    Env = configurationOptions.Value.Env;
        //    webClient = httpClient;
        //    this.callback = callback;
        //}

        /// <summary>
        /// Initializes a new instance of the NewsContentService class.
        /// Documention on api:
        /// https://newscenter.mayo.edu/api-v2/docs/
        /// </summary>
        /// <param name="applicationId">The application idenification</param>
        /// <param name="contentUrl"></param>
        /// <param name="consumerKey"></param>
        /// <param name="secretKey"></param>
        /// <param name="repository"></param>
        public NewsContentService(HttpClient httpClient, IOptions<NewsContentServiceOptions> configurationOptions, IContentStorageRepository contentStorageRepo, ITableStorageRepository tableStorageRepo)
        {
            contentUrl = configurationOptions.Value.ContentUrl;
            consumerKey = configurationOptions.Value.ConsumerKey;
            secretKey = configurationOptions.Value.SecretKey;
            ContentStorageRepo = contentStorageRepo;
            TableStorageRepo = tableStorageRepo;
            Env = configurationOptions.Value.Env;
            webClient = httpClient;
        }

        /// <summary>
        /// Save the content
        /// </summary>
        /// <returns>The <see cref="Task"/></returns>
        public async Task SaveAsync()
        {
            var sortOrder = await SortOrderAsync("49");

            var editorialList = await EditorialsAsync("49");
            var packages = await ProcessAsync(editorialList, new Category { Id = "49", Name = "FEATURED", Type = "FEATURED" });
            await SaveAsync(packages);

            //// remove the existing sorted news
            await ContentStorageRepo.DeleteEntitiesAsync("FEATUREDNEWSSORTED");

            sortOrder.Reverse();
            int i = 0;
            var sortList = sortOrder.Select(x => new { Id = x, SortDate = string.Format("{0}.{1}", DateTime.UtcNow.ToString("yyyy-MM-ddTHH:mm.ss"), i++.ToString("00")) });

            //// limit the number of tasks using the semaphore instead of ActionBlock
            SemaphoreSlim throttler = new SemaphoreSlim(System.Environment.ProcessorCount, System.Environment.ProcessorCount);

            await Task.WhenAll(sortList.Select(async x =>
            {
                await throttler.WaitAsync();
                try
                {
                    var p = await TableStorageRepo.GetEntityAsync("ContentStorage", "49", x.Id);
                    if (p != null)
                    {
                        p.PartitionKey = "FEATUREDNEWSSORTED";
                        p.Properties["SortDate"] = new EntityProperty(x.SortDate);
                        await TableStorageRepo.SaveAsync("ContentStorage", p);
                    }
                }
                finally
                {
                    throttler.Release();
                }
            }));

            //// campuses
            await RetrieveSaveAsync(await ContentStorageRepo.CampusesAsync());

            //// sections
            await RetrieveSaveAsync(await ContentStorageRepo.SectionsAsync());
        }

        /// <summary>
        /// Save the html wrapper
        /// </summary>
        /// <returns>The <see cref="Task"/></returns>
        public async Task SaveWrapperAsync()
        {
            var (Open, Close) = await HtmlWrapperAsync();

            var j = new JObject(
                new JProperty("Open", Open),
                new JProperty("Close", Close));

            await ContentStorageRepo.SaveAsync("content", "carehubshtmlwrapper.json", j.ToString());
        }

        /// <summary>
        /// Save the html wrapper
        /// </summary>
        /// <param name="list">The list of ids to refresh</param>
        /// <returns>The <see cref="Task"/></returns>
        public async Task RefeshBlobsAsync(List<string> list = null)
        {
            list = list ?? await ContentStorageRepo.GetBlobsAsync("content");

            var block = new ActionBlock<string>(
            async c =>
            {
                try
                {
                    if (int.TryParse(c, out int num))
                    {
                        var content = await ContentStorageRepo.GetBlobAsync<Content>("content", c);
                        var h = content.Page.Elements.Where(x => x.Type.Equals("HTML")).FirstOrDefault();

                        var url = $"{contentUrl}api-v2/editorial/?p={c}";
                        var j = await GetRequestClientAsync(url);

                        var y = j["content"]["data"].AsJEnumerable().Select(x =>
                        {
                            return new Editorial
                            {
                                Content = (string)x["post_content"]
                            };
                        }).First();

                        h.Value = y.Content;

                        await ContentStorageRepo.SaveAsync("content", c, content);
                    }
                }
                catch (Exception ex)
                {
                    callback?.Invoke(string.Join("-", c, ex.Message));
                }
            },
            new ExecutionDataflowBlockOptions { MaxDegreeOfParallelism = 3 });

            list.ForEach(async x =>
            {
                await block.SendAsync(x);
            });

            block.Complete();
            await block.Completion;
        }

        /// <summary>
        /// Save the content
        /// </summary>
        /// <param name="source">The source list</param>
        /// <param name="categoryId">The category identifier</param>
        /// <param name="id">The content identifier</param>
        /// <returns>The <see cref="Task"/></returns>
        public async Task SaveAsync(string source, string categoryId, string id)
        {
            var editorialList = (await EditorialsAllAsync(categoryId, id)).Take(1).ToList();

            if (source.Equals("FEATURED"))
            {
                await SaveAsync(await ProcessAsync(editorialList, new Category { Id = "49", Name = "FEATURED", Type = "FEATURED" }));
            }
            else if (source.Equals("CAMPUSES"))
            {
                await SaveAsync(await ProcessAsync(editorialList, new Category(categoryId, "campus", "Campus")));
            }
            else if (source.Equals("SECTIONS"))
            {
                await SaveAsync(await ProcessAsync(editorialList, new Category(categoryId, "section", "Section")));
            }
        }

        /// <summary>
        /// Get content and save it
        /// </summary>
        /// <param name="list">The <see cref="List{T}"/></param>
        /// <returns>The <see cref="Task"/></returns>
        public async Task RetrieveSaveAsync(List<PackageItem> list)
        {
            var block = new ActionBlock<PackageItem>(
            async x =>
            {
                var editorialList = await EditorialsAsync(x.Category.Id, 25);
                var packageList = await ProcessAsync(editorialList, x.Category);
                await SaveAsync(packageList);
            },
            new ExecutionDataflowBlockOptions { MaxDegreeOfParallelism = 3 });

            list.ForEach(async x =>
            {
                await block.SendAsync(x);
            });

            block.Complete();
            await block.Completion;
        }

        /// <summary>
        /// Get the sort order for featured and more
        /// </summary>
        /// <param name="id">The category identifier</param>
        /// <returns>The <see cref="Task{TResult}"/></returns>
        public async Task<List<string>> SortOrderAsync(string id)
        {
            var url = $"{contentUrl}api-v2/site-options/single?option=intranet_news_center_headlines&specific_key={id}";
            var j = await GetRequestClientAsync(url);

            var featured = ((JArray)j["content"]["data"]["carousel"]).Select(x => (string)x);
            var more = ((JArray)j["content"]["data"]["latest-news"]).Select(x => (string)x);

            return featured.Concat(more).ToList();
        }

        /// <summary>
        /// Get the list of content fro mthe api
        /// </summary>
        /// <param name="id">The category identifier</param>
        /// <param name="count">The number of items to get</param>
        /// <returns>The <see cref="Task{TResult}"/></returns>
        public async Task<List<Editorial>> EditorialsAsync(string id, int count = 10)
        {
            var url = $"{contentUrl}api-v2/editorial/?cat={id}&posts_per_page={count}&include_featured_image=hub-news-center-16x9&include_terms&include_meta";

            var j = await GetRequestClientAsync(url);

            return j["content"]["data"].AsJEnumerable().Select(x =>
            {
                return new Editorial
                {
                    Author = (string)x["post_author"]["display_name"],
                    CanComment = false,
                    Content = (string)x["post_content"],
                    Excerpt = (string)x["post_excerpt"],
                    Guid = (string)x["guid"],
                    Id = (string)x["ID"],
                    ModifiedDate = (string)x["post_modified"],
                    ModifiedDateUtc = (string)x["post_modified_gmt"],
                    Name = (string)x["post_name"],
                    PostDate = (string)x["post_date"],
                    PostDateUtc = (string)x["post_date_gmt"],
                    Title = (string)x["post_title"],
                    Image = (string)x["featured_image"],
                    Tags = x["categories"] != null ? FilterCategories(x["categories"].ToObject<List<string>>()) : new List<string>(),
                    ////MarkDown = (string)x["markdown"]
                };
            }).ToList();
        }

        /// <summary>
        /// Get the list of content fro mthe api
        /// </summary>
        /// <param name="id">The category identifier</param>
        /// <param name="page">The page number</param>
        /// <returns>The <see cref="Task{TResult}"/></returns>
        public async Task<List<Editorial>> EditorialsPageAsync(string id, string page)
        {
            var url = $"{contentUrl}api-v2/editorial/?cat={id}&posts_per_page=25&include_featured_image=hub-news-center-16x9&include_terms&include_meta&page={page}";

            var j = await GetRequestClientAsync(url);

            return j["content"]["data"].AsJEnumerable().Select(x =>
            {
                return new Editorial
                {
                    Author = (string)x["post_author"]["display_name"],
                    CanComment = false,
                    Content = (string)x["post_content"],
                    Excerpt = (string)x["post_excerpt"],
                    Guid = (string)x["guid"],
                    Id = (string)x["ID"],
                    ModifiedDate = (string)x["post_modified"],
                    ModifiedDateUtc = (string)x["post_modified_gmt"],
                    Name = (string)x["post_name"],
                    PostDate = (string)x["post_date"],
                    PostDateUtc = (string)x["post_date_gmt"],
                    Title = (string)x["post_title"],
                    Image = (string)x["featured_image"],
                    Tags = x["categories"] != null ? FilterCategories(x["categories"].ToObject<List<string>>()) : new List<string>(),
                    ////MarkDown = (string)x["markdown"]
                };
            }).ToList();
        }

        /// <summary>
        /// Get the list of content fro mthe api
        /// </summary>
        /// <param name="id">The category identifier</param>
        /// <returns>The <see cref="Task{TResult}"/></returns>
        public async Task<List<Editorial>> EditorialsPageAllAsync(string id)
        {
            ConcurrentBag<Editorial> list = new ConcurrentBag<Editorial>();

            var url = $"{contentUrl}api-v2/editorial/?cat={id}&posts_per_page=25&include_featured_image=hub-news-center-16x9&include_terms&include_meta&page=1";

            var j = await GetRequestClientAsync(url);

            j["content"]["data"].AsJEnumerable().Select(x =>
            {
                return new Editorial
                {
                    Author = (string)x["post_author"]["display_name"],
                    CanComment = false,
                    Content = (string)x["post_content"],
                    Excerpt = (string)x["post_excerpt"],
                    Guid = (string)x["guid"],
                    Id = (string)x["ID"],
                    ModifiedDate = (string)x["post_modified"],
                    ModifiedDateUtc = (string)x["post_modified_gmt"],
                    Name = (string)x["post_name"],
                    PostDate = (string)x["post_date"],
                    PostDateUtc = (string)x["post_date_gmt"],
                    Title = (string)x["post_title"],
                    Image = (string)x["featured_image"],
                    Tags = x["categories"] != null ? FilterCategories(x["categories"].ToObject<List<string>>()) : new List<string>(),
                    ////MarkDown = (string)x["markdown"]
                };
            }).ToList().ForEach(y => list.Add(y));

            var maxPages = (int)j["content"]["max_pages"];
            //maxPages = maxPages > 4 ? 4 : maxPages - 1;

            if (maxPages > 1)
            {
                url = $"{contentUrl}api-v2/editorial/?cat={id}&posts_per_page=25&include_featured_image=hub-news-center-16x9&include_terms&include_meta&page=2";

                j = await GetRequestClientAsync(url);

                j["content"]["data"].AsJEnumerable().Select(x =>
                {
                    return new Editorial
                    {
                        Author = (string)x["post_author"]["display_name"],
                        CanComment = false,
                        Content = (string)x["post_content"],
                        Excerpt = (string)x["post_excerpt"],
                        Guid = (string)x["guid"],
                        Id = (string)x["ID"],
                        ModifiedDate = (string)x["post_modified"],
                        ModifiedDateUtc = (string)x["post_modified_gmt"],
                        Name = (string)x["post_name"],
                        PostDate = (string)x["post_date"],
                        PostDateUtc = (string)x["post_date_gmt"],
                        Title = (string)x["post_title"],
                        Image = (string)x["featured_image"],
                        Tags = x["categories"] != null ? FilterCategories(x["categories"].ToObject<List<string>>()) : new List<string>(),
                        ////MarkDown = (string)x["markdown"]
                    };
                }).ToList().ForEach(y => list.Add(y));
            }

            return list.ToList();
        }

        /// <summary>
        /// Get the list of content fro mthe api
        /// </summary>
        /// <param name="id">The category identifier</param>
        /// <param name="postId">The post identifier</param>
        /// <returns>The <see cref="Task{TResult}"/></returns>
        public async Task<List<Editorial>> EditorialsAllAsync(string id, string postId)
        {
            
            ConcurrentBag<Editorial> list = new ConcurrentBag<Editorial>();

            int[] pagenumbers = new int[] { 1, 2, 3, 4, 5, 6, 7, 8, 9, 10 };

            await Task.WhenAll(pagenumbers.Select(async a =>
            {
                var url = $"{contentUrl}api-v2/editorial/?cat={id}&posts_per_page=25&include_featured_image=hub-news-center-16x9&include_terms&include_meta&paged={a}";

                var j = await GetRequestClientAsync(url);


                j["content"]["data"].AsJEnumerable().Select(x =>
                {
                    return new Editorial
                    {
                        Author = (string)x["post_author"]["display_name"],
                        CanComment = false,
                        Content = (string)x["post_content"],
                        Excerpt = (string)x["post_excerpt"],
                        Guid = (string)x["guid"],
                        Id = (string)x["ID"],
                        ModifiedDate = (string)x["post_modified"],
                        ModifiedDateUtc = (string)x["post_modified_gmt"],
                        Name = (string)x["post_name"],
                        PostDate = (string)x["post_date"],
                        PostDateUtc = (string)x["post_date_gmt"],
                        Title = (string)x["post_title"],
                        Image = (string)x["featured_image"],
                        Tags = x["categories"] != null ? FilterCategories(x["categories"].ToObject<List<string>>()) : new List<string>(),
                    };
                }).ToList().ForEach(y => list.Add(y));
            }));

            return list.Where(x => x.Id.Equals(postId)).ToList();
        }

        /// <summary>
        /// Process the content
        /// </summary>
        /// <param name="editorials">The <see cref="List{T}"/></param>
        /// <param name="category">The <see cref="Category"/></param>
        /// <returns>The <see cref="Task{TResult}"/></returns>
        public async Task<List<PackageItem>> ProcessAsync(List<Editorial> editorials, Category category)
        {
            ConcurrentBag<PackageItem> list = new ConcurrentBag<PackageItem>();

            var block = new ActionBlock<Editorial>(
            async x =>
            {
                string categoryName = category.Name;

                if (x.Tags.Count > 0)
                {
                    if (categories.TryGetValue(x.Tags.First(), out categoryName) == false)
                    {
                        try
                        {
                            var c = await GetRequestClientAsync($"{contentUrl}api-v2/editorial/taxonomy/?search={System.Web.HttpUtility.UrlEncode(x.Tags.First())}");
                            categoryName = c["content"]["data"].ToArray().Length > 0 ? (string)c["content"]["data"].ToArray().First()["name"] : category.Name;

                            categories.TryAdd(x.Tags.First(), categoryName);
                        }
                        catch
                        {
                            //// TODO: track and treat as an error to re-process
                            ////category.Name = category.Name;
                            //// category.Id = category.Id;
                            categories.TryAdd(x.Tags.First(), categoryName);
                        }
                    }
                }

                list.Add(transform(x, category.Id, categoryName, category.Type, x.Image));

            },
            new ExecutionDataflowBlockOptions { MaxDegreeOfParallelism = 3 });

            editorials.ForEach(async x =>
            {
                await block.SendAsync(x);
            });

            block.Complete();
            await block.Completion;

            return list.ToList();
        }

        /// <summary>
        /// Save the content to azure
        /// </summary>
        /// <param name="list">The <see cref="List{T}"/></param>
        /// <returns>The <see cref="Task"/></returns>
        public async Task SaveAsync(List<PackageItem> list)
        {
            var block = new ActionBlock<PackageItem>(
            async x =>
            {
                try
                {
                    var url = x.Images.First().Value;

                    if (string.IsNullOrEmpty(url) == false)
                    {
                        //// images are the same
                        var uri = new Uri(url);
                        var t = uri.Segments.Last();
                        await SaveImageAsync(t, url);

                        foreach (var y in x.Images)
                        {
                            y.Value = $"https://ccs-{Env}-ema.mayo.edu/api/content/image/{t.ToLower()}";
                        }
                    }

                    //// Save the blob
                    await SaveContentAsync(x);

                    //// save the table storage
                    await SaveEntityAsync(x.Category.Id, x);
                }
                catch (Exception ex)
                {
                    callback?.Invoke(string.Join("-", x.Category.Id, x.Id, ex.Message));

                    var e = new DynamicTableEntity(
                        DateTime.UtcNow.Date.ToString("yyyyMMdd"),
                        x.Id,
                        "*",
                        new Dictionary<string, EntityProperty>
                        {
                            { "Id", new EntityProperty(x.Id) },
                            { "Name", new EntityProperty(x.Name) },
                            { "ExceptionMessage", new EntityProperty(ex.Message) },
                            { "StackTrace", new EntityProperty(ex.StackTrace) }
                        });

                    await TableStorageRepo.SaveAsync("BatchAuditStorage", e);
                }
            },
            new ExecutionDataflowBlockOptions { MaxDegreeOfParallelism = 3 });

            list.ForEach(async x =>
            {
                await block.SendAsync(x);
            });

            block.Complete();
            await block.Completion;
        }

        /// <summary>
        /// Save a content article
        /// </summary>
        /// <param name="item">The <see cref="PackageItem"/></param>
        /// <returns>The <see cref="Task"/></returns>
        public async Task SaveContentAsync(PackageItem item)
        {
            var c = await GetContentAsync(item.Id);
            c.Page.Elements.AddRange(item.Images);
            c.Page.Categories = new List<Category>
            {
                item.Category
            };
            await ContentStorageRepo.SaveAsync<Content>("content", item.Id, c);
        }

        /// <summary>
        /// Save the image to blob
        /// </summary>
        /// <param name="name">The name of the image</param>
        /// <param name="url">The url to the image</param>
        /// <returns>The <see cref="Task"/></returns>
        public async Task SaveImageAsync(string name, string url)
        {
            if (string.IsNullOrEmpty(url) == false)
            {
                if (savedImages.Contains(url) == false)
                {
                    try
                    {
                        var response = await webClient.GetAsync(url);
                        var data = await response.Content.ReadAsStreamAsync();
                        if (data != null && data.Length > 0)
                        {
                            name = name.ToLower();
                            await ContentStorageRepo.SaveStreamAsync("images", name, data);

                            savedImages.Add(url);
                        }
                    }
                    catch
                    {
                        throw;
                    }
                }
            }
        }

        /// <summary>
        /// Save the package to table storage
        /// </summary>
        /// <param name="partitionKey">The partition key</param>
        /// <param name="x">The <see cref="PackageItem"/></param>
        /// <returns>The <see cref="Task{TResult}"/></returns>
        public async Task SaveEntityAsync(string partitionKey, PackageItem x)
        {
            var e = new DynamicTableEntity(
                partitionKey,
                x.Id,
                "*",
                new Dictionary<string, EntityProperty>
                {
                    { "Id", new EntityProperty(x.Id) },
                    { "Name", new EntityProperty(x.Name) },
                    { "Description", new EntityProperty(x.Description) },
                    { "Type", new EntityProperty(x.Type) },
                    { "Version", new EntityProperty(x.Version) },
                    { "Category", new EntityProperty(await TableStorageRepo.SerializeAsync<Category>(x.Category)) },
                    { "Images", new EntityProperty(await TableStorageRepo.SerializeAsync<List<Element>>(x.Images)) },
                    { "IsScheduled", new EntityProperty(false) },
                    { "Author", new EntityProperty(await TableStorageRepo.SerializeAsync<Person>(x.Author)) },
                    { "ReadTime", new EntityProperty(x.ReadTime) },
                    { "Date", new EntityProperty(x.Date) },
                    { "SortDate", new EntityProperty(x.SortDate) }
                });

            await TableStorageRepo.SaveAsync("ContentStorage", e);
        }

        /// <summary>
        /// Get the image
        /// </summary>
        /// <param name="name">The image name</param>
        /// <returns>The <see cref="Task{TResult}"/></returns>
        public async Task<Stream> GetImageAsync(string name)
        {
            await Task.Delay(0);
            throw new NotImplementedException();
        }

        /// <summary>
        /// Gets the favorites list
        /// </summary>
        /// <param name="userId">The user identifier</param>
        /// <param name="count">The number of items</param>
        /// <returns>The <see cref="Task{TResult}"/></returns>
        public async Task<List<Package>> GetFavoritesAsync(string userId, int? count)
        {
            await Task.Delay(0);
            throw new NotImplementedException();
        }

        /// <summary>
        /// Add to the favorites list
        /// </summary>
        /// <param name="userId">The user identifier</param>
        /// <param name="categoryId">The category identifier</param>
        /// <param name="id">The content identifier</param>
        /// <returns>The <see cref="Task"/></returns>
        public async Task AddFavoriteAsync(string userId, string categoryId, string id)
        {
            await Task.Delay(0);
            throw new NotImplementedException();
        }

        /// <summary>
        /// Delete from the favorites list
        /// </summary>
        /// <param name="userId">The user identifier</param>
        /// <param name="categoryId">The category identifier</param>
        /// <param name="id">The content identifier</param>
        /// <returns>The <see cref="Task"/></returns>
        public async Task DeleteFavoriteAsync(string userId, string categoryId, string id)
        {
            await Task.Delay(0);
            throw new NotImplementedException();
        }

        /// <summary>
        /// Get the content
        /// </summary>
        /// <param name="id">The content identifier</param>
        /// <param name="formatted">A value indicating whether the content should be formatted</param>
        /// <returns>The <see cref="Task{TResult}"/></returns>
        public async Task<Content> GetContentAsync(string id, bool formatted = true)
        {
            try
            {
                var url = $"{contentUrl}api-v2/editorial/?p={id}";
                var j = await GetRequestClientAsync(url);

                var y = j["content"]["data"].AsJEnumerable().Select(x =>
                {
                    return new Editorial
                    {
                        Author = (string)x["post_author"]["display_name"],
                        CanComment = false,
                        Content = (string)x["post_content"],
                        Excerpt = (string)x["post_excerpt"],
                        Guid = (string)x["guid"],
                        Id = (string)x["ID"],
                        ModifiedDate = (string)x["post_modified"],
                        ModifiedDateUtc = (string)x["post_modified_gmt"],
                        Name = (string)x["post_name"],
                        PostDate = (string)x["post_date"],
                        PostDateUtc = (string)x["post_date_gmt"],
                        Title = (string)x["post_title"],
                        //// MarkDown = (string)x["markdown"]
                    };
                }).First();

                return new Content
                {
                    Categories = new List<Category>
                {
                    new Category
                    {
                        Id = "ARTICLE",
                        Name = "Article",
                        Type = "article"
                    }
                },
                    Date = DateTime.Parse(y.PostDateUtc),
                    Id = y.Id,
                    Page = new Page
                    {
                        Author = new Person
                        {
                            Name = y.Author
                        },
                        Categories = new List<Category>
                    {
                        new Category
                        {
                            Id = "ARTICLE",
                            Name = "Article",
                            Type = "article"
                        }
                    },
                        Date = DateTime.Parse(y.ModifiedDateUtc),
                        Elements = new List<Element>
                    {
                        new Element ("TITLE", WebUtility.HtmlDecode(y.Title)),
                        new Element ("DESCRIPTION", WebUtility.HtmlDecode(y.Excerpt)),
                        new Element ("HTML", y.Content)
                    },
                        Id = y.Id,
                        Type = "ARTICLE"
                    }
                };
            }
            catch
            {
                throw;
            }
        }

        /// <summary>
        /// Get the content metadata
        /// </summary>
        /// <param name="id"></param>
        /// <returns>The <see cref="Task{TResult}"/></returns>
        public async Task<(string Likes, string Reads)> GetMetaDataAsync(string id)
        {
            var url = $"{contentUrl}api-v2/editorial/?p={id}&include_meta";
            var j = await GetRequestClientAsync(url);

            var likes = (string)j["content"]["data"][0]["meta"]["inc_recommendation_counter"].AsEnumerable().FirstOrDefault() ?? "0";
            var reads = (string)j["content"]["data"][0]["meta"]["inc_read_counter"].AsEnumerable().FirstOrDefault() ?? "0";

            return (likes, reads);
        }

        /// <summary>
        /// Get the front page content
        /// </summary>
        /// <returns>The <see cref="Task{TResult}"/></returns>
        public async Task<List<Package>> GetListAsync(string id, int? count)
        {
            count = count ?? 10;

            var url = $"{contentUrl}api-v2/editorial/";

            if (string.IsNullOrEmpty(id))
            {
                url += $"?cat={31}&posts_per_page=10"; // featured

                var j = await GetRequestClientAsync(url);

                var editorialList = j["content"]["data"].AsJEnumerable().Select(x =>
                {
                    return new Editorial
                    {
                        Author = (string)x["post_author"]["display_name"],
                        CanComment = false,
                        Content = (string)x["post_content"],
                        Excerpt = (string)x["post_excerpt"],
                        Guid = (string)x["guid"],
                        Id = (string)x["ID"],
                        ModifiedDate = (string)x["post_modified"],
                        ModifiedDateUtc = (string)x["post_modified_gmt"],
                        Name = (string)x["post_name"],
                        PostDate = (string)x["post_date"],
                        PostDateUtc = (string)x["post_date_gmt"],
                        Title = (string)x["post_title"]
                    };
                }).ToList();

                // First 4 are the featured
                var featured = (await Task.WhenAll(editorialList.Take(4).Select(async x =>
                {
                    var h = await GetUrlContentsAsync(x.Guid);

                    return new PackageItem
                    {
                        Id = x.Id,
                        Category = new Category
                        {
                            Id = h.Item1.Replace(" ", "").ToUpper(),
                            Name = h.Item1,
                            Type = h.Item1.Replace(" ", "").ToLower()
                        },
                        Name = x.Title,
                        Description = x.Excerpt,
                        Type = "FEATURED",
                        Images = new List<Element>
                                {
                                new Element ("FEATUREDIMAGELISTURL", h.Item2),
                                new Element ("FEATUREDIMAGEHEADERURL", h.Item2),
                                },
                        Date = DateTime.Parse(x.PostDate).ToString("yyyyMMdd")
                    };
                }))).ToList();

                // Anything afterwards is more news
                var more = (await Task.WhenAll(editorialList.Skip(4).Take(5).Select(async x =>
                {
                    var h = await GetUrlContentsAsync(x.Guid);

                    return new PackageItem
                    {
                        Id = x.Id,
                        Category = new Category
                        {
                            Id = h.Item1.Replace(" ", "").ToUpper(),
                            Name = h.Item1,
                            Type = h.Item1.Replace(" ", "").ToLower()
                        },
                        Name = x.Title,
                        Description = x.Excerpt,
                        Type = "MORE",
                        Images = new List<Element>
                                {
                                new Element ("FEATUREDIMAGELISTURL", h.Item2),
                                new Element ("FEATUREDIMAGEHEADERURL", h.Item2),
                                },
                        Date = DateTime.Parse(x.PostDate).ToString("yyyyMMdd")
                    };
                }))).ToList();

                return new List<Package>
                {
                    new Package
                    {
                        Id = DateTime.UtcNow.ToString ("yyyyMMdd"),
                            Items = featured,
                            Type = "FEATURED"
                    },
                    new Package
                    {
                        Id = DateTime.UtcNow.ToString ("yyyyMMdd"),
                            Items = more,
                            Type = "MORE"
                    },
                    new Package
                    {
                        Id = DateTime.UtcNow.ToString ("yyyyMMdd"),
                            Items = await ContentStorageRepo.CampusesAsync(),
                            Type = "CAMPUSES"
                    },
                    new Package
                    {
                        Id = DateTime.UtcNow.ToString ("yyyyMMdd"),
                            Items = await ContentStorageRepo.SectionsAsync(),
                            Type = "SECTIONS"
                    }
                };
            }
            else
            {
                var category = (await ContentStorageRepo.CampusesAsync()).FirstOrDefault(x => x.Id.Equals(id));
                if (category == null)
                {
                    category = (await ContentStorageRepo.SectionsAsync()).First(x => x.Id.Equals(id));
                }

                url += $"?cat={id}&posts_per_page=10";

                var j = await GetRequestClientAsync(url);

                var editorialList = j["content"]["data"].AsJEnumerable().Select(x =>
                {
                    return new Editorial
                    {
                        Author = (string)x["post_author"]["display_name"],
                        CanComment = false,
                        Content = (string)x["post_content"],
                        Excerpt = (string)x["post_excerpt"],
                        Guid = (string)x["guid"],
                        Id = (string)x["ID"],
                        ModifiedDate = (string)x["post_modified"],
                        ModifiedDateUtc = (string)x["post_modified_gmt"],
                        Name = (string)x["post_name"],
                        PostDate = (string)x["post_date"],
                        PostDateUtc = (string)x["post_date_gmt"],
                        Title = (string)x["post_title"]
                    };
                }).ToList();

                // if Id is given, it's a sub-list
                var subList = (await Task.WhenAll(editorialList.Select(async x =>
                {
                    var h = await GetUrlContentsAsync(x.Guid);

                    return new PackageItem
                    {
                        Id = x.Id,
                        Category = new Category
                        {
                            Id = h.Item1.Replace(" ", "").ToUpper(),
                            Name = h.Item1,
                            Type = h.Item1.Replace(" ", "").ToLower()
                        },
                        Name = x.Title,
                        Description = x.Excerpt,
                        Type = category.Type,
                        Images = new List<Element>
                                {
                                new Element ("IMAGELISTURL", h.Item2),
                                new Element ("IMAGEHEADERURL", h.Item2),
                                },
                        Date = DateTime.Parse(x.PostDate).ToString("yyyyMMdd")
                    };
                }))).ToList();

                return new List<Package>
                {
                    new Package
                    {
                        Id = DateTime.UtcNow.ToString ("yyyyMMdd"),
                            Items = subList,
                            Type = category.Type
                    }
                };
            }
        }

        /// <summary>
        /// Parse the html from the api
        /// </summary>
        /// <param name="url">The url</param>
        /// <returns>The <see cref="Task{TResult}"/></returns>
        private async Task<Tuple<string, string>> GetUrlContentsAsync(string url)
        {
            string category = "";
            string imageUrl = "";
            using (var response = await webClient.GetAsync(url))
            {
                var htmlCode = await response.Content.ReadAsStringAsync();

                var cat = "<div class=\"post-category\">";
                var i = htmlCode.IndexOf(cat, StringComparison.OrdinalIgnoreCase) + cat.Length;
                if (i > cat.Length)
                {
                    var t = htmlCode.Substring(i);
                    var nextTag = "rel=\"category tag\">";
                    var nextIndex = t.IndexOf(nextTag, StringComparison.OrdinalIgnoreCase) + nextTag.Length;
                    t = t.Substring(nextIndex);
                    nextTag = "</a>";
                    nextIndex = t.IndexOf(nextTag, StringComparison.OrdinalIgnoreCase);
                    category = t.Substring(0, nextIndex);
                }

                cat = "<figure class=\"featured-image\">";
                i = htmlCode.IndexOf(cat, StringComparison.OrdinalIgnoreCase) + cat.Length;
                if (i > cat.Length)
                {
                    var t = htmlCode.Substring(i);
                    var nextTag = "img src=\"";
                    var nextIndex = t.IndexOf(nextTag, StringComparison.OrdinalIgnoreCase) + nextTag.Length;
                    t = t.Substring(nextIndex);
                    nextTag = "\" class";
                    nextIndex = t.IndexOf(nextTag, StringComparison.OrdinalIgnoreCase);
                    imageUrl = t.Substring(0, nextIndex);
                }
            }

            return new Tuple<string, string>(category, imageUrl);
        }

        /// <summary>
        /// Gets the list of comments
        /// </summary>
        /// <param name="id">The content identifier</param>
        /// <returns>The <see cref="Task{TResult}"/></returns>
        public virtual async Task<List<Comment>> CommentsAsync(string id, string pageNumber = null)
        {
            await Task.Delay(0);
            throw new NotImplementedException();
        }

        /// <summary>
        /// Add a comment to the thread
        /// </summary>
        /// <param name="contentId">The content identifier</param>
        /// <param name="threadId">The thread identifier</param>
        /// <param name="userId">The user identifier</param>
        /// <param name="comment">The plain text comment</param>
        /// <param name="replyId">The identifier of the comment to reply to</param>
        /// <returns>The <see cref="Task"/></returns>
        public virtual async Task PostCommentAsync(string contentId, string threadId, string userId, string comment, string replyId = null)
        {
            await Task.Delay(0);
            throw new NotImplementedException();
        }

        /// <summary>
        /// Get the user from the news center
        /// </summary>
        /// <param name="email">The email address</param>
        /// <returns>The <see cref="Task{TResult}"/></returns>
        public async Task<(string UserName, string Name, string ProfileUrl)> UserAsync(string email)
        {
            // https://newscenter.mayo.edu/api-v2/user/single/
            var url = $"{contentUrl}api-v2/user/single?user_email={email}";
            var j = await GetRequestClientAsync(url);

            return j["content"]["data"] != null
                ? ((string)j["content"]["data"]["user_name"], (string)j["content"]["data"]["display_name"], (string)j["content"]["data"]["permalink"]["canonical"])
                : (string.Empty, string.Empty, string.Empty);
        }

        /// <summary>
        /// Add the user to the news center
        /// </summary>
        /// <param name="email">The email address</param>
        /// <param name="name">The display name</param>
        /// <returns>The <see cref="Task{TResult}"/></returns>
        public async Task<(string UserName, string Name, string ProfileUrl)> AddUserAsync(string email, string name = null)
        {
            // https://newscenter.mayo.edu/api-v2/user/single/
            var url = $"{contentUrl}api-v2/user/single";
            var j = await PostRequestClientAsync(url, new Dictionary<string, string> { { "user_email", email } });

            return j["content"]["data"] != null
                ? ((string)j["content"]["data"]["user_name"], (string)j["content"]["data"]["display_name"], (string)j["content"]["data"]["permalink"]["canonical"])
                : (string.Empty, string.Empty, string.Empty);
        }

        /// <summary>
        /// Get the content from the api using the http client
        /// </summary>
        /// <param name="url">The api url</param>
        /// <returns>The <see cref="Task{TResult}"/></returns>
        public async Task<JObject> GetRequestClientAsync(string url)
        {
            using (var requestMessage = new HttpRequestMessage(HttpMethod.Get, url))
            {
                requestMessage.Method = HttpMethod.Get;

                var time = DateTimeOffset.UtcNow;
                var temp = consumerKey + time.ToUnixTimeSeconds();
                string token = null;

                using (var hmac = new System.Security.Cryptography.HMACSHA256(Convert.FromBase64String(secretKey)))
                {
                    // From string to byte array
                    byte[] buffer = System.Text.Encoding.UTF8.GetBytes(temp);

                    // Compute the hash of the input file.
                    byte[] hashValue = hmac.ComputeHash(buffer);

                    // Output in HEX
                    token = ToHex(hashValue, false);
                }

                requestMessage.Headers.Add("HTTP-X-CAREHUBS-CONSUMER-KEY", consumerKey);
                requestMessage.Headers.Add("HTTP-X-CAREHUBS-TIMESTAMP", time.ToString("s"));
                requestMessage.Headers.Add("HTTP-X-CAREHUBS-TOKEN", token);

                using (var response = await webClient.SendAsync(requestMessage))
                {
                    if (response.IsSuccessStatusCode)
                    {
                        var s = Encoding.UTF8.GetString(await response.Content.ReadAsByteArrayAsync());
                        return JObject.Parse(s);
                    }
                    else
                    {
                        throw new Exception($"Error: {response.StatusCode} - {response.ReasonPhrase}");
                    }
                }
            }
        }

        /// <summary>
        /// Post the content to the api using the http client
        /// </summary>
        /// <param name="url">The api url</param>
        /// <param name="form">The request body</param>
        /// <returns>The <see cref="Task{TResult}"/></returns>
        public async Task<JObject> PostRequestClientAsync(string url, Dictionary<string, string> form)
        {
            using (var requestMessage = new HttpRequestMessage(HttpMethod.Post, url))
            {
                requestMessage.Method = HttpMethod.Post;

                var (Token, Time) = GetHeaderValues();

                requestMessage.Headers.Add("HTTP-X-CAREHUBS-CONSUMER-KEY", consumerKey);
                requestMessage.Headers.Add("HTTP-X-CAREHUBS-TIMESTAMP", Time);
                requestMessage.Headers.Add("HTTP-X-CAREHUBS-TOKEN", Token);

                requestMessage.Content = new FormUrlEncodedContent(form);

                using (var response = await webClient.SendAsync(requestMessage))
                {
                    if (response.IsSuccessStatusCode)
                    {
                        var s = Encoding.UTF8.GetString(await response.Content.ReadAsByteArrayAsync());
                        return JObject.Parse(s);
                    }
                    else
                    {
                        throw new Exception($"Error: {response.StatusCode} - {response.ReasonPhrase}");
                    }
                }
            }
        }

        /// <summary>
        /// Get the content from the api using the http client
        /// </summary>
        /// <param name="url">The api url</param>
        /// <returns>The <see cref="Task{TResult}"/></returns>
        public HttpRequestMessage GetRequestMessage(string url)
        {
            var requestMessage = new HttpRequestMessage(HttpMethod.Get, url);

            var time = DateTimeOffset.UtcNow;
            var temp = consumerKey + time.ToUnixTimeSeconds();
            string token = null;

            using (var hmac = new System.Security.Cryptography.HMACSHA256(Convert.FromBase64String(secretKey)))
            {
                // From string to byte array
                byte[] buffer = Encoding.UTF8.GetBytes(temp);

                // Compute the hash of the input file.
                byte[] hashValue = hmac.ComputeHash(buffer);

                // Output in HEX
                token = ToHex(hashValue, false);
            }

            requestMessage.Headers.Add("HTTP-X-CAREHUBS-CONSUMER-KEY", consumerKey);
            requestMessage.Headers.Add("HTTP-X-CAREHUBS-TIMESTAMP", time.ToString("s"));
            requestMessage.Headers.Add("HTTP-X-CAREHUBS-TOKEN", token);

            return requestMessage;
        }

        /// <summary>
        /// Get the content from the api using the http client
        /// </summary>
        /// <param name="url">The api url</param>
        /// <returns>The <see cref="Task{TResult}"/></returns>
        public async Task<JObject> GetResponseAsync(HttpRequestMessage requestMessage)
        {
            using (var response = await webClient.SendAsync(requestMessage))
            {
                if (response.IsSuccessStatusCode)
                {
                    var s = Encoding.UTF8.GetString(await response.Content.ReadAsByteArrayAsync());
                    return JObject.Parse(s);
                }
                else
                {
                    throw new Exception($"Error: {response.StatusCode} - {response.ReasonPhrase}");
                }
            }
        }

        /// <summary>
        /// Get the list of alerts
        /// </summary>
        /// <returns>The <see cref="Task{TResult}"/></returns>
        public async Task<Package> GetAlertsAsync()
        {
            await Task.Delay(0);
            throw new NotImplementedException();
        }

        /// <summary>
        /// Get the html wrapper for the post_content body
        /// </summary>
        /// <returns>The <see cref="Task{TResult}"/></returns>
        private async Task<(string Open, string Close)> HtmlWrapperAsync()
        {
            ////api-v2/site-options/single/?option=news_center_article_open
            ////api-v2/site-options/single/?option=news_center_article_close

            var url = $"{contentUrl}api-v2/site-options/single/?option=news_center_article_open";
            var j = await GetRequestClientAsync(url);

            var open = (string)j["content"]["data"];

            url = $"{contentUrl}api-v2/site-options/single/?option=news_center_article_close";
            j = await GetRequestClientAsync(url);

            var close = (string)j["content"]["data"];

            return (open, close);
        }

        /// <summary>
        /// Get the token and the time for the request
        /// </summary>
        /// <returns>The Token and Time string</returns>
        private (string Token, string Time) GetHeaderValues()
        {
            var time = DateTimeOffset.UtcNow;
            var temp = consumerKey + time.ToUnixTimeSeconds();
            string token = null;

            using (var hmac = new System.Security.Cryptography.HMACSHA256(Convert.FromBase64String(secretKey)))
            {
                // From string to byte array
                byte[] buffer = System.Text.Encoding.UTF8.GetBytes(temp);

                // Compute the hash of the input file.
                byte[] hashValue = hmac.ComputeHash(buffer);

                // Output in HEX
                token = ToHex(hashValue, false);

                return (token, time.ToString("s"));
            }
        }

        /// <summary>
        /// Match to a known category
        /// </summary>
        /// <param name="list">The list of tagged categories</param>
        /// <returns>The list of categories</returns>
        private List<string> FilterCategories(List<string> list)
        {
            var l = list.Where(x =>
            x.Equals("Arizona") == false &&
            x.Equals("Featured") == false &&
            x.Equals("Florida") == false &&
            x.Equals("Homepage") == false &&
            x.Equals("Northwest Wisconsin") == false &&
            x.Equals("Rochester") == false &&
            x.Equals("Southeast Minnesota") == false &&
            x.Equals("Southwest Minnesota") == false &&
            x.Equals("Southwest Wisconsin") == false
            ).ToList();

            if (l.Count() == 0 && list.Count() > 0)
            {
                l.Add(list.First());
            }

            return l;
        }
    }
}
