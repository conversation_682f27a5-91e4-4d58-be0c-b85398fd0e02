﻿//-----------------------------------------------------------------------
// <copyright file="FacilityIssue.cs" company="Mayo Foundation">
//     Copyright © Mayo Foundation. All rights reserved.
// </copyright>
//-----------------------------------------------------------------------

namespace Mayo.Mobile.Employee.Location.Model
{
    using System;
    using System.Collections.Generic;

    /// <summary>
    /// Initializes a new instance of the Issue class.
    /// </summary>
    public class FacilityIssue
    {
        /// <summary>
        /// Initializes a new instance of the FacilityIssue class.
        /// </summary>
        /// <param name="userId">The user identifier</param>
        /// <param name="campusId">required</param>
        /// <param name="buildingId">required</param>
        /// <param name="floorId">optional</param>
        /// <param name="roomId">optional</param>
        /// <param name="reportedLocation"><see cref="FacilityLocation"/>optional</param>
        /// <param name="comments">The comment string/></param>
        /// <param name="photos">The list of photo identifiers, optional</param>
        public FacilityIssue(string userId, string campusId, string buildingId, string floorId, string roomId, FacilityLocation reportedLocation, string comments, List<string> photos = null)
        {
            Id = Guid.NewGuid().ToString();
            UserId = userId;
            CampusId = campusId;
            BuildingId = buildingId;
            FloorId = floorId;
            RoomId = roomId;
            ReportedLocation = reportedLocation;
            Comments = comments;
            PhotoIds = photos ?? new List<string>();
        }

        /// <summary>
        /// Gets or sets the issue identifier
        /// </summary>
        public string Id { get; set; }

        /// <summary>
        /// Gets or sets the user identifier
        /// </summary>
        public string UserId { get; set; }

        /// <summary>
        /// Gets or sets the campusId
        /// </summary>
        public string CampusId { get; set; }

        /// <summary>
        /// Gets or sets the buildingId
        /// </summary>
        public string BuildingId { get; set; }

        /// <summary>
        /// Gets or sets the floorId
        /// </summary>
        public string FloorId { get; set; }

        /// <summary>
        /// Gets or Sets the RoomId
        /// </summary>
        public string RoomId { get; set; }

        /// <summary>
        /// Gets or sets where the issue was reported
        /// </summary>
        public FacilityLocation ReportedLocation { get; set; }

        /// <summary>
        /// Gets or sets the list of photo identifiers
        /// </summary>
        public List<string> PhotoIds { get; set; }

        /// <summary>
        /// Gets or sets the comment
        /// </summary>
        public string Comments { get; set; }
    }
}
