﻿//-----------------------------------------------------------------------
// <copyright file="IssueRequest.cs" company="Mayo Foundation">
//     Copyright © Mayo Foundation. All rights reserved.
// </copyright>
//-----------------------------------------------------------------------

namespace Mayo.Mobile.Employee.Web.Api.Model
{
    using System;
    using System.Collections.Concurrent;
    using System.Collections.Generic;
    using System.IO;
    using System.Runtime.Serialization;
    using System.Text;
    using System.Threading;
    using System.Threading.Tasks;
    using Mayo.Mobile.Employee.Location.Model;
    using Mayo.Mobile.Employee.Model;

    /// <summary>
    /// Initializes a new instance of the IssueRequest class.
    /// </summary>
    [DataContract(Namespace = "http://www.mayo.edu/MayoMobile")]
    public class IssueRequest : Request
    {
        /// <summary>
        /// Gets or sets the campus location
        /// </summary>
        [DataMember]
        public string CampusId { get; set; }

        /// <summary>
        /// Gets or sets the building location
        /// </summary>
        [DataMember]
        public string BuildingId { get; set; }

        /// <summary>
        /// Gets or sets the floor of the build
        /// </summary>
        [DataMember]
        public string FloorId { get; set; }

        /// <summary>
        /// Gets or sets the floor of the build
        /// </summary>
        [DataMember]
        public string RoomId { get; set; }

        /// <summary>
        /// Gets or sets where the issue was reported
        /// </summary>
        [DataMember]
        public FacilityLocation Location { get; set; } = new FacilityLocation();

        /// <summary>
        /// Gets or sets the <see cref="Photo"/>
        /// </summary>
        [DataMember]
        public List<Photo> Photos { get; set; } = new List<Photo>();

        /// <summary>
        /// Gets or sets the comment
        /// </summary>
        [DataMember]
        public string Comments { get; set; }

        /// <summary>
        /// Validate the request
        /// </summary>
        /// <param name="imageCount">The optional image count</param>
        public void Validate(int imageCount = 0)
        {
            if (Photos.Count > 3 || imageCount > 3)
            {
                throw new Exception("Too many images in request");
            }

            if (Photos.Count != imageCount)
            {
                throw new Exception("The number of images are not the same");
            }

            if (string.IsNullOrEmpty(Comments))
            {
                throw new Exception("Comments are required");
            }

            if (string.IsNullOrEmpty(CampusId))
            {
                throw new Exception("CampusId is required");
            }

            if (string.IsNullOrEmpty(BuildingId))
            {
                throw new Exception("BuildingId is required");
            }
        }
    }
}
