﻿//-----------------------------------------------------------------------
// <copyright file="Person.cs" company="Mayo Foundation">
//     Copyright © Mayo Foundation. All rights reserved.
// </copyright>
//-----------------------------------------------------------------------

namespace Mayo.Mobile.Employee.Content.Model
{
    using System;
    using System.Collections.Generic;
    using System.Linq;
    using System.Runtime.Serialization;
    using System.Text;

    /// <summary>
    /// Initializes a new instance of the Source class
    /// </summary>
    [DataContract(Namespace = "http://www.mayo.edu/MayoMobile")]
    public class Source
    {
        /// <summary>
        /// The identifier
        /// </summary>
        [DataMember(Name = "Id")]
        public string Id { get; set; }

        /// <summary>
        /// The title
        /// </summary>
        [DataMember(Name = "Title")]
        public string Title { get; set; }

        /// <summary>
        /// The url
        /// </summary>
        [DataMember(Name = "Url")]
        public string Url { get; set; }

        /// <summary>
        /// The original url from the source
        /// </summary>
        [DataMember(Name = "SourceUrl")]
        public string SourceUrl { get; set; }
    }
}
