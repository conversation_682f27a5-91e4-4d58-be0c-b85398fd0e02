﻿//-----------------------------------------------------------------------
// <copyright file="SearchService.cs" company="Mayo Foundation">
//     Copyright © Mayo Foundation. All rights reserved.
// </copyright>
//-----------------------------------------------------------------------


namespace Mayo.Mobile.Employee.Search.Model
{
    using System;
    using System.Collections.Generic;
    using System.Linq;
    using System.Threading.Tasks;
    using Mayo.Mobile.Employee.Azure.Search.Model;
    using Mayo.Mobile.Employee.Model;

    /// <summary>
    /// Initializes a new instance of the SearchService class.
    /// </summary>
    public class SearchService : ISearchService
    {
        /// <summary>
        /// The <see cref="ISearchRepository"/>
        /// </summary>
        protected readonly ISearchRepository repository;

        /// <summary>
        /// Detect redundant calls
        /// </summary>
        protected bool disposedValue = false;

        /// <summary>
        /// Initializes a new instance of the SearchService class.
        /// </summary>
        /// <param name="repository">The <see cref="ISearchRepository"/></param>
        public SearchService(ISearchRepository repository)
        {
            this.repository = repository;
        }

        /// <summary>
        /// Search the content
        /// </summary>
        /// <param name="name">The name of the index to search</param>
        /// <param name="query">The text querying for</param>
        /// <param name="version">The application version</param>
        /// <returns>The <see cref="Task{TResult}"/></returns>
        public async Task<List<SearchResult>> SearchAsync(string name, string query, double version = 0.0)
        {
            var results = await this.repository.SearchAsync(name, query, string.Empty);
            return results;
        }

        /// <summary>
        /// Search the content
        /// </summary>
        /// <param name="name">The name of the index to search</param>
        /// <param name="query">The text querying for</param>
        /// <returns>The <see cref="Task{TResult}"/></returns>
        public async Task<List<SearchResult>> SuggestAsync(string name, string query)
        {
            return await this.repository.SuggestAsync(name, query);
        }

        /// <summary>
        /// This code added to correctly implement the disposable pattern.
        /// </summary>
        public void Dispose()
        {
            Dispose(true);
        }

        /// <summary>
        /// This code added to correctly implement the disposable pattern.
        /// </summary>
        /// <param name="disposing">A value indicating whether the class is being disposed</param>
        protected virtual void Dispose(bool disposing)
        {
            if (!disposedValue)
            {
                if (disposing)
                {
                    if (this.repository != null)
                    {
                        this.repository.Dispose();
                    }
                }

                disposedValue = true;
            }
        }
    }
}