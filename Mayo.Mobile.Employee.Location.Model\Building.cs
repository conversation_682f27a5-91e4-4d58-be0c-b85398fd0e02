﻿//-----------------------------------------------------------------------
// <copyright file="Building.cs" company="Mayo Foundation">
//     Copyright © Mayo Foundation. All rights reserved.
// </copyright>
//-----------------------------------------------------------------------

namespace Mayo.Mobile.Employee.Location.Model
{
    using Newtonsoft.Json;
    using System.Collections.Generic;

    /// <summary>
    /// Initializes a new instance of the Building class.
    /// </summary>
    public class Building : FacilityLocation
    {

        /// <summary>
        /// Gets or sets the <see cref="Address"/>
        /// </summary>
        public Address Address { get; set; }

        /// <summary>
        /// Gets or sets the list of floors
        /// </summary>
        [JsonIgnore]
        public List<Floor> Floors { get; set; }

        public Building() : base() { }

    }

    public class BuildingComparer : IComparer<Building>
    {
        public int Compare(Building building1, Building building2)
        {
            return building1.Name.CompareTo(building2.Name);
        }
    }
}
