﻿// -----------------------------------------------------------------------
// <copyright file="PersonService.cs" company="Mayo Clinic">
//     Copyright © Mayo Foundation. All rights reserved.
// </copyright>
// -----------------------------------------------------------------------

namespace Mayo.Mobile.Employee.Person.Model
{
    using System;
    using System.Collections.Concurrent;
    using System.Collections.Generic;
    using System.Linq;
    using System.Threading.Tasks;
    using System.Threading.Tasks.Dataflow;
    using Mayo.Mobile.Staff.Model;

    public class PersonService : IPersonService
    {
        private readonly IStaffService service;
        private const string PlatformSessionIdPROD = "wVxAoPD3ip2BcEGzxHOOKNgcb0NjGKxNDcqxUw47YypiB+ENhptuPeCY1EDVWw9WdaWa5zmUTLjOwscOPOLJZn5DZBNF8eZyV9AvUUl4xhIPD0cPyuVlANIOXET/olqQlvPuHnbkc6pcMwf7FZVNePswZYlW4krNFrMXwXrh9vtuAArZU3uXvWeCvGTE28NL+Mk044SJ7E863Mtzx4GbTqVIjQ3g6xoLf5vFDbXrrX05M+KytFQKwDSqkex6nEKopk3tMyg3EIEjLtB5BjUEeTjz03u0Mlk+6lO30aBULQt3Pt7GtGkX3mttYgq4kvRfWQYhwIxlbrN0G6U1Gz/JHbRF2JPLDJoCfyZ1n+LX3E6Abf+w6np2+CzQirMZda+HHf/MzEfJrYcCtkGQZDWcEDMOg8mSCKHWLICHfrsf15meB9JZuFsEFVNN4g/6UcvRWWIstV84tli+5VagNOkFyo6RJ6xY0WpDDt3az+F9QMFDWs7Vz8EUpfW9UA+pZ2GE0B+adg10nyLrYPqt6rSIR076fCBjBRB+OgJkQUWEbjs5ZkOAHFZ5Y9JB5hHQCH442K60xWsMDTSnoG8ILPAtYcT7ekgXQ8ROOumnRnKGIXzWs1YxTv4yIQOFDBYKZfi4ifpq9xapQkNoAJH59dpMJjp6jtSxh5pSvESKwDsN3Es=";

        /// <summary>
        /// Initializes a new instance of the PersonService class.
        /// </summary>
        /// <param name="url">The url string</param>
        public PersonService(IStaffService staffService)
        {
            this.service = staffService;
        }

        /// <summary>
        /// Get the person identifiers from the email address
        /// </summary>
        /// <param name="email">The email address</param>
        /// <returns>The <see cref="Task{TResult}"/></returns>
        public async Task<(string PersonId, string LanId)> GetIdentifiersAsync(string email)
        {
            var p = await this.service.GetStaffDetailAsync(PlatformSessionIdPROD, email, "EMAIL");
            return (p.Identifiers.Where(x => x.Type.Equals("PERSONID")).Select(x => x.Id).FirstOrDefault(), p.Identifiers.Where(x => x.Type.Equals("LANID")).Select(x => x.Id).FirstOrDefault());
        }

        /// <summary>
        /// Get the person identifiers from the email address
        /// </summary>
        /// <param name="emails">The email addresses</param>
        /// <returns>The <see cref="Task{TResult}"/></returns>
        public async Task<List<(string Email, string PersonId, string LanId)>> GetIdentifiersAsync(List<string> emails)
        {
            ConcurrentBag<(string Email, string PersonId, string LanId)> list = new ConcurrentBag<(string Email, string PersonId, string LanId)>();

            var block = new ActionBlock<string>(
            async c =>
            {
                try
                {
                    var p = await this.service.GetStaffDetailAsync(PlatformSessionIdPROD, c, "EMAIL");
                    list.Add((c, p.Identifiers.Where(x => x.Type.Equals("PERSONID")).Select(x => x.Id).FirstOrDefault(), p.Identifiers.Where(x => x.Type.Equals("LANID")).Select(x => x.Id).FirstOrDefault()));
                }
                catch
                {
                }
            },
            new ExecutionDataflowBlockOptions { MaxDegreeOfParallelism = 5 });

            emails.ForEach(async x =>
            {
                await block.SendAsync(x);
            });

            block.Complete();
            await block.Completion;

            return list.ToList();
        }

        /// <summary>
        /// Get the person photo
        /// </summary>
        /// <param name="personId">The person identifier</param>
        /// <returns>The <see cref="Task{TResult}"/></returns>
        public async Task<string> GetPhotoAsync(string personId)
        {
            var (_, _, Photo) = await this.service.GetStaffImageAsync(PlatformSessionIdPROD, personId, "PERSONID");
            return Photo;
        }
    }
}
