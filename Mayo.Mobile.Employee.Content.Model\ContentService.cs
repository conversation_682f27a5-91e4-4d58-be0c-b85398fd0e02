﻿// -----------------------------------------------------------------------
// <copyright file="ContentService.cs" company="Mayo Clinic">
//     Copyright © Mayo Foundation. All rights reserved.
// </copyright>
// -----------------------------------------------------------------------

namespace Mayo.Mobile.Employee.Content.Model
{
    using System;
    using System.Collections.Generic;
    using System.IO;
    using System.Linq;
    using System.Net;
    using System.Net.Http;
    using System.Text;
    using System.Threading.Tasks;
    using Mayo.Mobile.Employee.Model;
    using Newtonsoft.Json.Linq;

    /// <summary>
    /// Initializes a new instance of the ContentService class.
    /// </summary>
    public class ContentService : IContentService
    {
        /// <summary>
        /// The html wrapper open
        /// </summary>
        private string wrapperOpen = null;

        /// <summary>
        /// The html wrapper close
        /// </summary>
        private string wrapperClose = null;

        /// <summary>
        /// Gets the wrapper html for the content
        /// </summary>
        private (string Open, string Close) Wrapper
        {
            get
            {
                if (this.wrapperOpen == null || this.wrapperClose == null)
                {
                    var (Open, Close) = this.GetWrapperAsync().GetAwaiter().GetResult();
                    this.wrapperOpen = Open;
                    this.wrapperClose = Close;
                }

                return (this.wrapperOpen, this.wrapperClose);
            }
        }
        private IContentStorageRepository ContentStorageRepo { get; set; }

        public ContentService(IContentStorageRepository contentStorageRepo)
        {
            ContentStorageRepo = contentStorageRepo;
        }

        /// <summary>
        /// Get the list of alerts
        /// </summary>
        /// <returns>The <see cref="Task{TResult}"/></returns>
        public async Task<Package> GetAlertsAsync()
        {
            try
            {
                //// add the alerts
                return await this.ContentStorageRepo.GetBlobAsync<Package>("application", "newsalerts.json");
            }
            catch
            {
                return new Package { Id = DateTime.UtcNow.ToString("yyyyMMdd"), Items = new List<PackageItem>(), Type = "ALERTS" };
            }
        }

        /// <summary>
        /// Gets the content list
        /// </summary>
        /// <param name="id">The category identifier</param>
        /// <param name="count">The number of items</param>
        /// <returns>The <see cref="Task{TResult}"/></returns>
        public async Task<List<Package>> GetListAsync (string id, int? count)
        {
            _ = new List<PackageItem>();
            List<PackageItem> items;

            if (string.IsNullOrEmpty(id))
            {
                id = string.IsNullOrEmpty(id) ? "FEATUREDNEWSSORTED" : id;
                items = (await ContentStorageRepo.ItemsAsync(id)).Take(count ?? 14).ToList();

                if (items.Count < 4)
                {
                    throw new Exception("There need to be four itmes to fill the FEATURED list.");
                }

                for (int i = 0; i < items.Count; i++)
                {
                    items[i].Type = i < 4 ? "FEATURED" : "MORE";
                }

                var packages = new List<Package>
                {
                    new Package
                    {
                        Id = DateTime.UtcNow.ToString ("yyyyMMdd"),
                            Items = items.Take(4).ToList(),
                            Type = "FEATURED"
                    },
                    new Package
                    {
                        Id = DateTime.UtcNow.ToString ("yyyyMMdd"),
                            Items = items.Skip(4).ToList(),
                            Type = "MORE"
                    },
                    new Package
                    {
                        Id = DateTime.UtcNow.ToString ("yyyyMMdd"),
                            Items = await ContentStorageRepo.CampusesAsync(),
                            Type = "CAMPUSES"
                    },
                    new Package
                    {
                        Id = DateTime.UtcNow.ToString ("yyyyMMdd"),
                            Items = await ContentStorageRepo.SectionsAsync(),
                            Type = "SECTIONS"
                    }
                };

                return packages;
            }
            else
            {
                items = count.HasValue
                    ? (await ContentStorageRepo.ItemsAsync(id)).Take(count.Value).ToList()
                    : await ContentStorageRepo.ItemsAsync(id);

                return new List<Package>
                {
                    new Package
                    {
                        Id = DateTime.UtcNow.ToString ("yyyyMMdd"),
                            Items = items,
                            Type = items.First().Type.Equals("CAMPUS")
                                ? "CAMPUSES"
                                : items.First().Type.Equals("SECTION")
                                ? "SECTIONS"
                                : items.First().Type
                    }
                };
            }
        }

        /// <summary>
        /// Gets the favorites list
        /// </summary>
        /// <param name="userId">The user identifier</param>
        /// <param name="count">The number of items</param>
        /// <returns>The <see cref="Task{TResult}"/></returns>
        public async Task<List<Package>> GetFavoritesAsync(string userId, int? count)
        {
            var items = await ContentStorageRepo.FavoritesAsync(userId);

            return new List<Package>
                {
                    new Package
                    {
                        Id = DateTime.UtcNow.ToString("yyyyMMdd"),
                            Items = items,
                            Type = "FAVORITES"
                    }
            };
        }

        /// <summary>
        /// Add to the favorites list
        /// </summary>
        /// <param name="userId">The user identifier</param>
        /// <param name="categoryId">The category identifier</param>
        /// <param name="id">The content identifier</param>
        /// <returns>The <see cref="Task"/></returns>
        public async Task AddFavoriteAsync(string userId, string categoryId, string id)
        {
            var items = await ContentStorageRepo.FavoritesAsync(userId);

            var item = items.Where(x => x.Id.Equals(id) && x.Category.Id.Equals(categoryId)).FirstOrDefault();
            if (item != null)
            {
                items.Remove(item);
            }

            var p = await ContentStorageRepo.ItemAsync(categoryId, id);
            items.Insert(0, p);

            await ContentStorageRepo.SaveFavoritesAsync(userId, items);
        }

        /// <summary>
        /// Delete from the favorites list
        /// </summary>
        /// <param name="userId">The user identifier</param>
        /// <param name="categoryId">The category identifier</param>
        /// <param name="id">The content identifier</param>
        /// <returns>The <see cref="Task"/></returns>
        public virtual async Task DeleteFavoriteAsync(string userId, string categoryId, string id)
        {
            var items = await this.ContentStorageRepo.FavoritesAsync(userId);

            var item = items.Where(x => x.Id.Equals(id) && x.Category.Id.Equals(categoryId)).FirstOrDefault();
            if (item != null)
            {
                items.Remove(item);

                await this.ContentStorageRepo.SaveFavoritesAsync(userId, items);
            }
        }

        /// <summary>
        /// Get the content
        /// </summary>
        /// <param name="id">The content identifier</param>
        /// <param name="formatted">A value indicating whether the content should be formatted</param>
        /// <returns>The <see cref="Task{TResult}"/></returns>
        public virtual async Task<Content> GetContentAsync (string id, bool formatted = true)
        {
            var content = await ContentStorageRepo.GetBlobAsync<Content>("content", id);
            var h = content.Page.Elements.Where(x => x.Type.Equals("HTML")).FirstOrDefault();

            if (formatted)
            {
                var body = h.Value;
                h.Value = string.Concat(this.Wrapper.Open, body, this.wrapperClose);
            }

            return content;
        }

        /// <summary>
        /// Get the image
        /// </summary>
        /// <param name="name">The name of the image</param>
        /// <returns>The <see cref="Task{TResult}"/></returns>
        public virtual async Task<Stream> GetImageAsync(string name)
        {
            return await ContentStorageRepo.ImageAsync(name);
        }

        /// <summary>
        /// Gets the list of comments
        /// </summary>
        /// <param name="id">The content identifier</param>
        /// <param name="pageNumber">The page number for paging if provided</param>
        /// <returns>The <see cref="Task{TResult}"/></returns>
        public virtual Task<List<Comment>> CommentsAsync(string id, string pageNumber = null)
        {
            throw new NotImplementedException();
        }

        /// <summary>
        /// Add a comment to the thread
        /// </summary>
        /// <param name="contentId">The content identifier</param>
        /// <param name="threadId">The thread identifier</param>
        /// <param name="userId">The user identifier</param>
        /// <param name="comment">The plain text comment</param>
        /// <param name="replyId">The identifier of the comment to reply to</param>
        /// <returns>The <see cref="Task"/></returns>
        public virtual Task PostCommentAsync(string contentId, string threadId, string userId, string comment, string replyId = null)
        {
            throw new NotImplementedException();
        }

        /// <summary>
        /// Gets the wrapper for the html content
        /// </summary>
        /// <returns>The <see cref="Task{TResult}"/></returns>
        private async Task<(string Open, string Close)> GetWrapperAsync()
        {
            var b = await this.ContentStorageRepo.GetBlobContentsAsync("content", "carehubshtmlwrapper.json");
            var j = JObject.Parse(b);
            return ((string)j["Open"], (string)j["Close"]);
        }
    }
}