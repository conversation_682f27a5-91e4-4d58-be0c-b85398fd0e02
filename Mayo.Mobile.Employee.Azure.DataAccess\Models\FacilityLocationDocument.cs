﻿using Mayo.Mobile.Employee.Location.Model;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Text;

namespace Mayo.Mobile.Employee.Azure.DataAccess.Models
{
    /// <summary>
    /// Wrapper object for the location repo
    /// </summary>
    public class FacilityLocationDocument
    {
        [JsonProperty("id")]
        public string Id { get; set; }
        public string PartitionKey { get; set; }
        public string DocType { get; set; } = "Location";
        public string LocationId { get; set; }
        public string LocationName { get; set; }
        public string LocationType { get; set; }
        public string LocationSubType { get; set; }
        public List<Campus> Campuses { get; set; }
        public List<Building> Buildings { get; set; }
        public List<Floor> Floors { get; set; }
        public List<Room> Rooms { get; set; }
    }
}
