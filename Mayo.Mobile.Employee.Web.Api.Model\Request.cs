﻿// -----------------------------------------------------------------------
// <copyright file="Request.cs" company="Mayo Clinic">
//     Copyright © Mayo Foundation. All rights reserved.
// </copyright>
// -----------------------------------------------------------------------

namespace Mayo.Mobile.Employee.Web.Api.Model
{
    using System;
    using System.Collections.Generic;
    using System.ComponentModel;
    using System.Linq;
    using System.Runtime.Serialization;
    using System.Text;

    /// <summary>
    /// Initializes a new instance of the Request class.
    /// </summary>
    [DataContract(Namespace = "http://www.mayo.edu/MayoMobile")]
    public class Request
    {
        /// <summary>
        /// Gets or sets the application id
        /// </summary>
        [DataMember]
        public string ApplicationId { get; set; }

        /// <summary>
        /// Gets or sets the application version
        /// </summary>
        [DataMember]
        public string ApplicationVersion { get; set; }

        /// <summary>
        /// Gets or sets the device id
        /// </summary>
        [DataMember]
        public string DeviceId { get; set; }
    }
}
