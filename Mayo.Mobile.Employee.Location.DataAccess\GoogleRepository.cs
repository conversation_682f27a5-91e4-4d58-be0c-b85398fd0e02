﻿//-----------------------------------------------------------------------
// <copyright file="GoogleRepository.cs" company="Mayo Foundation">
//     Copyright © Mayo Foundation. All rights reserved.
// </copyright>
//-----------------------------------------------------------------------
namespace Mayo.Mobile.Employee.Location.DataAccess
{
    using System;
    using System.Linq;
    using System.Net.Http;
    using System.Threading.Tasks;
    using Mayo.Mobile.Employee.Location.DataAccess.Interfaces;
    using Microsoft.Extensions.Options;
    using Newtonsoft.Json.Linq;

    public class GoogleRepository: IGoogleMapRepo
    {
        private HttpClient httpClient;
        private string googleApiKey;

        public GoogleRepository(HttpClient client, IOptions<GoogleRepoOptions> configurationOptions)
        {
            httpClient = client;
            googleApiKey = configurationOptions.Value.GoogleApiKey;
            httpClient.BaseAddress = new Uri(configurationOptions.Value.GoogleBaseUrl);
        }

        /// <summary>
        /// Constructor for use with Testing purposes
        /// </summary>
        /// <param name="httpClient"></param>
        /// <param name="apiKey"></param>
        /// <param name="baseUrl"></param>
        public GoogleRepository(GoogleRepoOptions configurationOptions)
        {
            httpClient = new HttpClient();
            googleApiKey = configurationOptions.GoogleApiKey;
            httpClient.BaseAddress = new Uri(configurationOptions.GoogleBaseUrl);
        }

        /// <summary>
        /// Method to convert an address to latitude and longitude coordinates
        /// </summary>
        /// <param name="address">The address to be mapped</param>
        /// <returns>The <see cref="Task{TResult}"</returns>
        public async Task<(string Latitude, string Longitude)> GetLatidueAndLongitudeAsync(string address)
        {
            var response = await this.httpClient.GetAsync($"?address={Uri.EscapeDataString(address)}&key={this.googleApiKey}");

            var googleLocations = JObject.Parse(await response.Content.ReadAsStringAsync());

            var x = googleLocations["results"].First();
            var coordinates = ((string)x["geometry"]["location"]["lat"], (string)x["geometry"]["location"]["lng"]);

            return coordinates;
        }
    }
}