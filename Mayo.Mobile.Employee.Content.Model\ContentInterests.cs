﻿//-----------------------------------------------------------------------
// <copyright file="ContentInterests.cs" company="Mayo Foundation">
//     Copyright © Mayo Foundation. All rights reserved.
// </copyright>
//-----------------------------------------------------------------------

namespace Mayo.Mobile.Employee.Content.Model
{
    using System;
    using System.Collections.Generic;
    using System.Linq;
    using System.Runtime.Serialization;
    using System.Text;
    using Mayo.Mobile.Employee.Model;

    /// <summary>
    /// Initializes a new instance of the ContentInterests class.
    /// </summary>
    [DataContract(Namespace = "http://www.mayo.edu/MayoMobile")]
    public class ContentInterests
    {
        /// <summary>
        /// Gets or sets the question
        /// </summary>
        [DataMember(Name = "Question")]
        public string Question { get; set; }

        /// <summary>
        /// Gets or sets the interests
        /// </summary>
        [DataMember(Name = "Interests")]
        public List<Interest> Interests { get; set; }
    }
}
