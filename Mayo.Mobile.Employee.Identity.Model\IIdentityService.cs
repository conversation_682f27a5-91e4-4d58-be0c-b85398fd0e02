﻿// -----------------------------------------------------------------------
// <copyright file="IIdentityService.cs" company="Mayo Clinic">
//     Copyright © Mayo Foundation. All rights reserved.
// </copyright>
// -----------------------------------------------------------------------

namespace Mayo.Mobile.Employee.Identity.Model 
{
    using System.Collections.Generic;
    using System.Text;
    using System.Threading.Tasks;
    using System;
    using System.Net.Http;
    using System.IO;

    /// <summary>
    /// Initializes a new instance of the IIdentityService class.
    /// </summary>
    public interface IIdentityService
    {

        /// <summary>
        /// Authenticate user credentials
        /// </summary>
        /// <param name="userName">The user name</param>
        /// <param name="password">The password</param>
        /// <returns>The <see cref="Task{TResult}"/></returns>
        Task<Employee.Model.Identity> LogonAsync (string userName, string password);

        /// <summary>
        /// Authenticate the user
        /// </summary>
        /// <param name="token">The access token</param>
        /// <returns>The <see cref="Task{TResult}"/></returns>
        Task<Employee.Model.Identity> TokenAsync(string token);

        /// <summary>
        /// Gets person photo
        /// </summary>
        /// <param name="personId">The user id</param>
        /// <returns>The <see cref="Task{TResult}"/></returns>
        Task<Stream> GetPhotoAsync(string personId);
    }
}