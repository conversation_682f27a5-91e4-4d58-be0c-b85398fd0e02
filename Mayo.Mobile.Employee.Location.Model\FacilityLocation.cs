﻿//-----------------------------------------------------------------------
// <copyright file="FacilityLocation.cs" company="Mayo Foundation">
//     Copyright © Mayo Foundation. All rights reserved.
// </copyright>
//-----------------------------------------------------------------------

namespace Mayo.Mobile.Employee.Location.Model
{

    /// <summary>
    /// Initializes a new instance of the Location class.
    /// </summary>
    public class FacilityLocation
    {

        /// <summary>
        /// Gets or sets the identifier
        /// </summary>
        public string Id { get; set; }

        /// <summary>
        /// Gets or sets the name
        /// </summary>
        public string Name { get; set; }

        /// <summary>
        /// Gets or sets the description
        /// </summary>
        public string Description { get; set; }

        /// <summary>
        /// Gets or sets the latitude
        /// </summary>
        public string Latitude { get; set; }

        /// <summary>
        /// Gets or sets the longitude
        /// </summary>
        public string Longitude { get; set; }

        /// <summary>
        /// Used to determine id the location is a Campus, Building, Floor, Room
        /// </summary>
        public string LocationType { get; set; }

        /// <summary>
        /// Used to determine what subtype of the LocationType this is, primaraly for filtering
        /// </summary>
        public string LocationSubType { get; set; }


        public FacilityLocation() {}

    }
}
