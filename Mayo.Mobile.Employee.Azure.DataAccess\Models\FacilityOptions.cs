﻿namespace Mayo.Mobile.Employee.Azure.DataAccess.Models
{
    public class FacilityCosmosOptions
    {
        public string CosmosDatabaseName { get; set; }
        public string CosmosContainerName { get; set; }
        public string CosmosPartitionKeyProperty { get; set; }
    }

    public class WorkOrderCosmosOptions
    {
        public string CosmosDatabaseName { get; set; }
        public string CosmosContainerName { get; set; }
        public string CosmosPartitionKeyProperty { get; set; }
    }

    public class FacilityQueueOptions
    {
        public string QueueName { get; set; }
    }

    public class FacilityBlobOptions
    {
        public string BlobContainerName { get; set; }
    }
}
