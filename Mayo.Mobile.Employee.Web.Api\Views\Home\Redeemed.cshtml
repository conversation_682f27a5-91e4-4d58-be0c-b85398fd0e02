﻿@model Mayo.Mobile.Employee.Web.Api.Model.PersonViewModel
<!DOCTYPE html>
<html>
<head prefix="og: http://ogp.me/ns#">
    <meta content="text/html; charset=UTF-8" name="Content-Type" />
    <meta name="viewport" content="width=device-width" />
    <!-- Open Graph meta tags -->
    <meta property="og:title" content="Download the Mayo Clinic Employee app!">
    <meta property="og:type" content="website">
    <meta property="og:description" content="The Mayo Clinic Employee app is a new app for iOS and Android, designed with practical, helpful features to make work more 'joyful' for Mayo Clinic employees on-the-go.">
    <meta property="og:image" content="~ogimage.jpg">
    <meta property="og:image:height" content="440">
    <meta property="og:image:width" content="640">
    <link rel="stylesheet" href="~/css/bootstrap.css">
    <link rel="stylesheet" href="~/css/bootstrap-grid.css">
    <link rel="stylesheet" href="~/interstitial.css">
    <title>Download the Mayo Clinic Employee app!</title>
</head>
<body>
    <script>
        function CopyToClipboard(containerid) {
            var textArea;

            function isOS() {
                //can use a better detection logic here
                return navigator.userAgent.match(/ipad|iphone/i);
            }

            function createTextArea(text) {
                textArea = document.createElement('textArea');
                textArea.readOnly = true;
                textArea.contentEditable = true;
                textArea.value = text;
                document.body.appendChild(textArea);
            }

            function selectText() {
                var range, selection;

                if (isOS()) {
                    range = document.createRange();
                    range.selectNodeContents(textArea);
                    selection = window.getSelection();
                    selection.removeAllRanges();
                    selection.addRange(range);
                    textArea.setSelectionRange(0, 999999);
                } else {
                    textArea.select();
                }
            }

            function copyTo() {
                document.execCommand('copy');
                document.body.removeChild(textArea);
            }

            createTextArea(document.getElementById(containerid).textContent);
            selectText();
            copyTo();
        }
    </script>
    <div class="container-lg">
        <div class="row h-100">
            <div class="col-lg-7 content-height text-center nopadding">
                <div id="content">
                    <div class="card card-border h-100">
                        <!--Content Image-->
                        <img class="card-img h-100 shadow-lg nopadding" src="~/EMPAPPHero.png" />
                        <a>
                            <div class="mask content-gradient"></div>
                        </a>
                        <div class="today-image"></div>
                        <!--Title and Icon Treatment-->
                        <div class="card-img-overlay d-flex flex-column justify-content-end align-items-start">
                            <div class="content-title">
                                Download the Mayo Clinic Employee app!
                            </div>
                            <div class="content-category">
                                <span class="today">Available on iOS and Android.</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-lg-5 h-100 text-center nopadding">
                <div id="marketing">
                    <!--Mobile View-->
                    <div class="marketing-margin d-lg-none">
                        <!--App Icon-->
                        <img src="~/EMP-app-icon-1024.png" width="80" class="img-responsive center-block" />
                        <!--Marketing Title-->
                        <div class="marketing-title">Mayo Clinic Employee app</div>
                        <!--Marketing Description-->
                        <div style="padding:1.2em">The Mayo Clinic Employee app is a new app for iOS and Android, designed with practical, helpful features to make information more accessible for Mayo Clinic employees on-the-go.</div>

                        <div style="padding: 2.0em 1.5em 0.5em 1.5em">
                            <div class="image-cropper">
                                <div class="image-rounded" width="100" style="background-image:url('@Model.ImageUrl')"></div>
                            </div>
                            <div class="name-title">@Model.Name</div>
                            <div class="name-id">@Model.LanId</div>
                            <div class="div-empty">&nbsp;</div>
                            <div class="name-title">Download Code:</div>
                            <div class="code-id">@Model.RedemptionCode</div>
                            <a href="@Model.RedemptionUrl" class="btn btn-primary-mobile">Redeem Code</a>
                        </div>
                    </div>
                    <!--Desktop View-->
                    <div class="marketing-margin d-none d-lg-block" style="margin:auto">
                        <!--App Icon-->
                        <img src="~/EMP-app-icon-1024.png" width="80" class="img-responsive center-block" />
                        <!--Marketing Title-->
                        <div class="marketing-title">Mayo Clinic Employee app</div>
                        <!--Marketing Description-->
                        <div style="padding:1.2em">The Mayo Clinic Employee app is a new app for iOS and Android, developed by the same internal team responsible for the award-winning Mayo Clinic app for patients and consumers. This app is designed with practical, helpful features to make information more accessible for Mayo Clinic employees on-the-go.</div>

                        <div style="padding: 2.0em 1.5em 0.5em 1.5em">
                            <div class="image-cropper">
                                <div class="image-rounded" width="100" style="background-image:url('@Model.ImageUrl')"></div>
                            </div>
                            <div class="name-title">@Model.Name</div>
                            <div class="name-id">@Model.LanId</div>
                            <div class="div-empty">&nbsp;</div>
                            <div class="name-title">Download Code:</div>
                            <div class="code-id">@Model.RedemptionCode</div>
                            @if (ViewData["Tablet"] as string == "tablet")
                            {
                                <a href="@Model.RedemptionUrl" class="btn btn-primary-mobile">Redeem Code</a>
                            }
                            else
                            {
                                <div id="copydiv" class="code-url">@Model.RedemptionUrl</div>
                                <button onclick="CopyToClipboard('copydiv')" class="btn btn-primary-mobile">Copy to Clipboard</button>
                            }
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>


