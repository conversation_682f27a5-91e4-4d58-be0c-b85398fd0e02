﻿// -----------------------------------------------------------------------
// <copyright file="IDirectoryService.cs" company="Mayo Clinic">
//     Copyright © Mayo Foundation. All rights reserved.
// </copyright>
// -----------------------------------------------------------------------

namespace Mayo.Mobile.Employee.Directory.Model
{
    using Mayo.Mobile.Employee.Model;
    using System;
    using System.Collections.Generic;
    using System.IO;
    using System.Net.Http;
    using System.Text;
    using System.Threading.Tasks;

    /// <summary>
    /// Initializes a new instance of the IDirectoryService class.
    /// </summary>
    public interface IDirectoryService
    {

        /// <summary>
        /// Search the quarterly service can be by last name or phone number
        /// </summary>
        /// <param name="searchTerm">The search term</param>
        /// <returns>The <see cref="Task{TResult}"/></returns>
        Task<List<SearchResult>> SearchPersonAsync(string searchTerm);

        /// <summary>
        /// Gets a person details
        /// </summary>
        /// <param name="personId">The person identifier</param>
        /// <returns>The <see cref="Task{TResult}"/></returns>
        Task<Employee> GetPersonAsync(string personId);

        /// <summary>
        /// Pages the pager number
        /// </summary>
        /// <param name="pagerNumber">The pager number</param>
        /// <param name="entity">The entity</param>
        /// <param name="message">The message</param>
        /// <param name="senderPersonId">The sender person identifier</param>
        /// <returns>The <see cref="Task"/></returns>
        Task PageAsync(string pagerNumber, string entity, string message, string senderPersonId);

            /// <summary>
        /// Pages the pager number
        /// </summary>
        /// <param name="pagerNumber">The pager number</param>
        /// <param name="entity">The entity</param>
        /// <param name="message">The message</param>
        /// <param name="senderPersonId">The sender person identifier</param>
        /// <returns>The <see cref="Task"/></returns>
        Task PageAsyncUsingSP(string pagerNumber, string entity, string message, string senderPersonId);

        /// <summary>
        /// Gets person photo
        /// </summary>
        /// <param name="personId">The user id</param>
        /// <returns>The <see cref="Task{TResult}"/></returns>
        Task<Stream> GetPhotoAsync(string personId);

        /// <summary>
        /// Gets the favorites list
        /// </summary>
        /// <param name="userId">The user identifier</param>
        /// <param name="count">The number of items</param>
        /// <returns>The <see cref="Task{TResult}"/></returns>
        Task<List<Person>> GetFavoritesAsync(string userId, int? count);

        /// <summary>
        /// Add to the favorites list
        /// </summary>
        /// <param name="userId">The user identifier</param>
        /// <param name="p">The <see cref="Person"/></param>
        /// <returns>The <see cref="Task"/></returns>
        Task AddFavoriteAsync(string userId, Person p);

        /// <summary>
        /// Delete from the favorites list
        /// </summary>
        /// <param name="userId">The user identifier</param>
        /// <param name="p">The <see cref="Person"/></param>
        /// <returns>The <see cref="Task"/></returns>
        Task DeleteFavoriteAsync(string userId, Person p);

        /// Get the status
        /// </summary>
        /// <param name="userId">The user identifier</param>
        /// <returns>The <see cref="Task{TResult}"/></returns>
        Task<Status> StatusAsync(string userId);
    }
}
