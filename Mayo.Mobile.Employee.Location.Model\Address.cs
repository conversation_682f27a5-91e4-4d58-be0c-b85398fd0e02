﻿//-----------------------------------------------------------------------
// <copyright file="Address.cs" company="Mayo Foundation">
//     Copyright © Mayo Foundation. All rights reserved.
// </copyright>
//-----------------------------------------------------------------------

namespace Mayo.Mobile.Employee.Location.Model
{
    using System;

    /// <summary>
    /// Initializes a new instance of the Address class.
    /// </summary>
    public class Address
    {
        /// <summary>
        /// Gets or sets the street address
        /// </summary>
        public string StreetAddress { get; set; }

        /// <summary>
        /// Gets or sets the city
        /// </summary>
        public string City { get; set; }

        /// <summary>
        /// Gets or sets the state
        /// </summary>
        public string State { get; set; }

        /// <summary>
        /// Gets or sets the postal code
        /// </summary>
        public string PostalCode { get; set; }


        /// <summary>
        /// Gets or sets the formatted address
        /// </summary>
        public string FormattedAddress
        {
            get
            {
                return string.Format(
                                "{0}{1}{2}, {3} {4}",
                                string.Join(Environment.NewLine, this.StreetAddress),
                                Environment.NewLine,
                                this.City,
                                this.State,
                                this.PostalCode);
            }
        }
    }
}
