﻿using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Text;

namespace Mayo.Mobile.Employee.Azure.DataAccess.Models
{
    public class FacilityWorkOrderDocument
    {
        [JsonProperty("id")]
        public string DocumentId { get; set; }
        public string PartitionKey { get; set; }
        /// <summary>
        /// What Level this was Logged as by the c# logger
        /// Debug, Info, Warning, Error, Fatal
        /// Logging Level i.e Debug, Info, Warning, Error, Fatal
        /// </summary>
        public string LoggingLevel { get; set; } = "Info";
        /// <summary>
        /// Metadata to query for Audit data by
        /// AuditLog, AppLog
        /// </summary>
        public string LoggingType { get; set; } = "AppLog";
        public string DocType { get; set; } = "WorkOrder";
        public string DateTimeRecieved { get; set; }
        public string CampusId { get; set; }
        public string BuildingId { get; set; }
        public string FloorId { get; set; }
        public string RoomId { get; set; }
        public string Comment { get; set; }
        public string ReportedBy { get; set; }

        /// <summary>
        /// Id used to attach images to the work order
        /// </summary>
        public string FacilitiesSysId { get; set; }

        /// <summary>
        /// Id of the WorkOrder in FacilitiesWorkOrder Repo
        /// Will be null until WorkOrder is successfully created
        /// </summary>
        public string WorkOrderId { get; set; }

        /// <summary>
        /// Ids of the photos to added as attachments to Work Order
        /// Photo images are saved in a separate repo.
        /// </summary>
        public List<string> PhotoIds { get; set; }

        /// <summary>
        /// Last Exception thrown.
        /// Used if Document can't be processed successfully
        /// ExceptionMessage == null means no Exception
        /// WorkOrderId == null means Exception was when creating WorkOrder
        /// WorkOrderId != null means Exception was when adding Photos to WorkOrder
        /// </summary>
        public string ExceptionMessage { get; set; }

        public FacilityWorkOrderDocument() { }

        public FacilityWorkOrderDocument(string id, string reportedBy, string comment, string campusId, string buildingId, string floorId = "", string roomId = "")
        {
            DocumentId = id;
            PartitionKey = $"{reportedBy}";
            DateTimeRecieved = DateTime.UtcNow.ToString("u");
            ReportedBy = reportedBy;
            Comment = comment;
            CampusId = campusId;
            BuildingId = buildingId;
            FloorId = floorId;
            RoomId = roomId;
            PhotoIds = new List<string>();
        }
    }
}
