﻿//-----------------------------------------------------------------------
// <copyright file="UpdateController.cs" company="Mayo Foundation">
//     Copyright © Mayo Foundation. All rights reserved.
// </copyright>
//-----------------------------------------------------------------------

namespace Mayo.Mobile.Employee.Web.Api.Controllers
{
    using Mayo.Mobile.Employee.Application.Model;
    using Microsoft.AspNetCore.Mvc;

    /// <summary>
    /// Initializes a new instance of the UpdateController class.
    /// </summary>
    [Route("Update")]
    public class UpdateController : Controller
    {
        /// <summary>
        /// The <see cref="IUpdateService"/>
        /// </summary>
        private readonly IUpdateService UpdateService = null;

        /// <summary>
        /// Initializes a new instance of the UpdateController class.
        /// </summary>
        /// <param name="service">The <see cref="IUpdateService"/></param>
        public UpdateController(IUpdateService service)
        {
            UpdateService = service;
        }

        /// <summary>
        /// Show the update information view
        /// </summary>
        /// <param name="os">The os the device runs</param>
        /// <returns>The <see cref="ViewResult"/></returns>
        [HttpGet("Employee")]
        public ViewResult Index([FromQuery] string os = "")
        {
            var url = UpdateService.GetAppStoreUrl(os, (string)Request.Headers["User-Agent"] ?? string.Empty);
            ViewBag.AppStoreUrl = url;
            ViewBag.Platform = url.Equals(UpdateService.DefaultUrl) ? "Desktop" : "Mobile";
            return View("Employee");
        }
    }
}
