﻿using Mayo.Mobile.Employee.Azure.DataAccess.Models;
using System.Threading.Tasks;

namespace Mayo.Mobile.Employee.Functions.Interfaces
{
    public interface IFacilityFunctionsService
    {
        /// <summary>
        /// Used to Process a WorkOrder Message and create a new work order in ServiceNow
        /// </summary>
        /// <param name="issue"></param>
        /// <returns></returns>
        public Task ProcessFacilityIssueAsync(FacilityWorkOrderQueueMessage issue);

        /// <summary>
        /// Use to Process a WorkOrderAttachment Queue Message and attach it to a Service now WorkOrder
        /// </summary>
        /// <param name="attachment"></param>
        /// <returns></returns>
        public Task ProcessWorkOrderAttachmentAsync(FacilityWorkOrderAttachment attachment);

        /// <summary>
        /// Used to Load location Data from UDP into our cosmos repo
        /// </summary>
        /// <returns></returns>
        public Task ProcessFacilityLocationLoadingAsync();
    }
}
