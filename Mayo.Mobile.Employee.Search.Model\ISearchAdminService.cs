﻿//-----------------------------------------------------------------------
// <copyright file="ISearchAdminService.cs" company="Mayo Foundation">
//     Copyright © Mayo Foundation. All rights reserved.
// </copyright>
//-----------------------------------------------------------------------

namespace Mayo.Mobile.Employee.Search.Model
{
    using System;
    using System.Collections.Generic;
    using System.Text;
    using System.Threading.Tasks;

    /// <summary>
    /// Initializes a new instance of the ISearchAdminService class.
    /// </summary>
    public interface ISearchAdminService
    {
        /// <summary>
        /// Add the content from the blob to the the search index
        /// </summary>
        /// <param name="c">The <see cref="Mayo.Mobile.Employee.Content.Model.Content"/></param>
        /// <param name="indexes">The list of indexes to add the content to</param>
        /// <returns>The <see cref="Task"/></returns>
        Task AddToIndexAsync(Mayo.Mobile.Employee.Content.Model.Content c, List<string> indexes);
    }
}
