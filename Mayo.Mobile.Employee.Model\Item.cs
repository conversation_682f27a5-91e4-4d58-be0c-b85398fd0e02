﻿//-----------------------------------------------------------------------
// <copyright file="Item.cs" company="Mayo Foundation">
//     Copyright © Mayo Foundation. All rights reserved.
// </copyright>
//-----------------------------------------------------------------------

namespace Mayo.Mobile.Employee.Model
{
    using System;
    using System.Collections.Generic;
    using System.Globalization;
    using System.Linq;
    using System.Runtime.Serialization;
    using System.Text;

    /// <summary>
    /// Initializes a new instance of the Item class.
    /// </summary>
    [DataContract(Namespace = "http://www.mayo.edu/MayoMobile")]
    public class Item
    {
        /// <summary>
        /// The current datetime
        /// </summary>
        private string datetime = DateTime.UtcNow.ToString("s");

        /// <summary>
        /// Gets or sets the content identifier
        /// </summary>
        [DataMember(Name = "Id")]
        public virtual string Id { get; set; }

        /// <summary>
        /// Gets or sets the <see cref="Category"/>
        /// </summary>
        [DataMember]
        public virtual Category Category { get; set; }

        /// <summary>
        /// Gets or sets the name
        /// </summary>
        [DataMember(Name = "Name")]
        public virtual string Name { get; set; }

        /// <summary>
        /// Gets or sets the abbreviated name or abbreviated value
        /// </summary>
        [DataMember(Name = "Abbreviation")]
        public virtual string Abbreviation { get; set; }

        /// <summary>
        /// Gets or sets the description
        /// </summary>
        [DataMember(Name = "Description")]
        public virtual string Description { get; set; }

        /// <summary>
        /// Gets or sets the type
        /// </summary>
        [DataMember(Name = "Type")]
        public virtual string Type { get; set; }

        /// <summary>
        /// Gets or sets the version
        /// </summary>
        [DataMember(Name = "Version")]
        public virtual string Version { get; set; }

        /// <summary>
        /// Gets or sets the date
        /// </summary>
        [DataMember(Name = "Date")]
        public string Date 
        {
            get
            {
                return this.datetime;
            }

            set
            {
                this.datetime = string.IsNullOrEmpty(value) ? DateTime.UtcNow.ToString("s") : value;
            }
        }
    }
}
