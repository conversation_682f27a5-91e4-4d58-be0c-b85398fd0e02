﻿//-----------------------------------------------------------------------
// <copyright file="Package.cs" company="Mayo Foundation">
//     Copyright © Mayo Foundation. All rights reserved.
// </copyright>
//-----------------------------------------------------------------------

namespace Mayo.Mobile.Employee.Content.Model
{
    using System;
    using System.Collections.Generic;
    using System.Globalization;
    using System.Linq;
    using System.Runtime.Serialization;
    using System.Text;

    /// <summary>
    /// Initializes a new instance of the Package class.
    /// </summary>
    [DataContract(Namespace = "http://www.mayo.edu/MayoMobile")]
    public class Package
    {
        /// <summary>
        /// Gets or sets the package identifier
        /// </summary>
        [DataMember]
        public string Id { get; set; }

        /// <summary>
        /// Gets or sets the package type
        /// </summary>
        [DataMember]
        public string Type { get; set; }

        /// <summary>
        /// Gets or sets the list of <see cref="PackageItem"/>
        /// </summary>
        [DataMember]
        public List<PackageItem> Items { get; set; }
    }
}
