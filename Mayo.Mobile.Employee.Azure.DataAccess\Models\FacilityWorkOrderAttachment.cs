﻿namespace Mayo.Mobile.Employee.Azure.DataAccess.Models
{
    public class FacilityWorkOrderAttachment
    {
        /// <summary>
        /// Id of matching Mobile Team Document
        /// </summary>
        public string MobileDocumentId { get; set; }

        /// <summary>
        /// Document DB partition key for use with Audit updating document
        /// </summary>
        public string PartitionKey { get; set; }

        /// <summary>
        /// Facility Sys_Id for API call
        /// </summary>
        public string SystemId { get; set; }
        
        /// <summary>
        /// Facility WorkorderId for lookup in ServiceNow
        /// </summary>
        public string WorkOrderId { get; set; }
        
        /// <summary>
        /// Id of the Photo to retrieve from blob storage and attach to WorkOrder
        /// </summary>
        public string PhotoId { get; set; }
    }
}
