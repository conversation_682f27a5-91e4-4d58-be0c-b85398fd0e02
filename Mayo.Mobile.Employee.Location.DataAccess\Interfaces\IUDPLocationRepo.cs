﻿using Mayo.Mobile.Employee.Location.DataAccess.Models;
using System;
using System.Collections.Generic;
using System.Data;
using System.Text;
using System.Threading.Tasks;

namespace Mayo.Mobile.Employee.Location.DataAccess.Interfaces
{
    public interface IUDPLocationRepo
    {
        /// <summary>
        /// Query the database
        /// </summary>
        /// <param name="id"></param>
        /// <returns>The <see cref="Task{TResult}"/></returns>
        public Task<List<UDPLocationTableRow>> GetCampusDataAsync();

        /// <summary>
        /// Query the database
        /// </summary>
        /// <param name="query"></param>
        /// <returns>The <see cref="Task{TResult}"/></returns>
        public Task<DataTable> ExecuteDataTableQueryAsync(string query);
    }
}
