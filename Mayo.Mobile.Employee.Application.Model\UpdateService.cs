﻿//-----------------------------------------------------------------------
// <copyright file="{.cs" company="Mayo Foundation">
//     Copyright © Mayo Foundation. All rights reserved.
// </copyright>
//-----------------------------------------------------------------------


namespace Mayo.Mobile.Employee.Application.Model
{
    using System;

    /// <summary>
    /// Initializes a new instance of the UpdateService class.
    /// </summary>
    public class UpdateService : IUpdateService
    {
        /// <summary>
        /// The defaul mobile apps site
        /// </summary>
        private string defaultUrl = "http://www.mayoclinic.org/apps/mayo-clinic";

        /// <summary>
        /// Initializes a new instance of the UpdateService class.
        /// </summary>
        /// <param name="appstoreurl">The app store url for apple devices</param>
        /// <param name="playstoreurl">The google play store url for android devices</param>
        public UpdateService(string appstoreurl, string playstoreurl)
        {
            this.AppStoreUrl = appstoreurl ?? string.Empty;
            this.PlayStoreUrl = playstoreurl ?? string.Empty;
        }

        /// <summary>
        /// Gets the app store url for apple devices
        /// </summary>
        public string AppStoreUrl { get; internal set; }

        /// <summary>
        /// Gets the google play store url for android devices
        /// </summary>
        public string PlayStoreUrl { get; internal set; }

        /// <summary>
        /// Gets the defaul mobile apps site
        /// </summary>
        public string DefaultUrl
        {
            get
            {
                return this.defaultUrl;
            }
        }

        /// <summary>
        /// Set the app store url in the view
        /// </summary>
        /// <param name="os">The os the device runs</param>
        /// <param name="appStores">The list of app stores</param>
        public string GetAppStoreUrl(string os, string userAgent)
        {
            var url = string.Empty;

            var appStores = Tuple.Create(
                "https://employeeapp.mayo.edu/",
                "https://apps.apple.com/us/app/mayo-clinic-employees/id1670501850",
                "https://play.google.com/store/apps/details?id=com.mayoclinichealthsolutions.employee");

            this.AppStoreUrl = appStores.Item2;
            this.PlayStoreUrl = appStores.Item3;

            if (os.Equals("ios", StringComparison.InvariantCultureIgnoreCase))
            {
                url = this.AppStoreUrl;
            }
            else if (os.Equals("android", StringComparison.InvariantCultureIgnoreCase))
            {
                url = this.PlayStoreUrl;
            }
            else
            {
                url = (userAgent.ToLowerInvariant().Contains("iphone") || userAgent.ToLowerInvariant().Contains("ipad"))
                    ? this.AppStoreUrl
                    : userAgent.Contains("android") || userAgent.Contains("Android")
                    ? this.PlayStoreUrl
                    : this.defaultUrl;
            }

            return string.IsNullOrEmpty(url) ? this.DefaultUrl : url;
        }
    }
}
