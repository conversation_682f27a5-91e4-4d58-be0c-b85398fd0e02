﻿//-----------------------------------------------------------------------
// <copyright file="HttpRequestExtensions.cs" company="Mayo Foundation">
//     Copyright © Mayo Foundation. All rights reserved.
// </copyright>
//-----------------------------------------------------------------------

namespace System
{
    using System;
    using System.Linq;

    /// <summary>
    /// Initializes a new instance that extends the string class
    /// </summary>
    public static class StringExtensions
    {
        /// <summary>
        /// Get the access token value
        /// </summary>
        /// <param name="s">The <see cref="string"/></param>
        /// <returns>The token value</returns>
        public static string GetAccessToken(this string s)
        {
            return string.Join(string.Empty, s.Skip(6)).Trim();
        }
    }
}
