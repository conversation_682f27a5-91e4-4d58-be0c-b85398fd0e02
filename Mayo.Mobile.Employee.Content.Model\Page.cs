﻿//-----------------------------------------------------------------------
// <copyright file="Page.cs" company="Mayo Foundation">
//     Copyright © Mayo Foundation. All rights reserved.
// </copyright>
//-----------------------------------------------------------------------

namespace Mayo.Mobile.Employee.Content.Model
{
    using System;
    using System.Collections.Generic;
    using System.Globalization;
    using System.Linq;
    using System.Runtime.Serialization;
    using System.Text;
    using System.Text.RegularExpressions;
    using Mayo.Mobile.Employee.Model;

    /// <summary>
    /// Initializes a new instance of the Page class.
    /// </summary>
    [DataContract(Namespace = "http://www.mayo.edu/MayoMobile")]
    public class Page
    {
        /// <summary>
        /// The read time
        /// </summary>
        private string readTime = null;

        /// <summary>
        /// The list of sub-categories
        /// </summary>
        private List<Category> subcategories = null;

        /// <summary>
        /// Gets or sets the content identifier
        /// </summary>
        [DataMember(Name = "Id")]
        public string Id { get; set; }

        /// <summary>
        /// Gets or sets the type
        /// </summary>
        [DataMember(Name = "Type")]
        public string Type { get; set; }

        /// <summary>
        /// Gets or sets the list of <see cref="Element"/>s
        /// </summary>
        [DataMember(Name = "Elements")]
        public List<Element> Elements { get; set; }

        /// <summary>
        /// Gets or sets the categories the content is available in
        /// </summary>
        [DataMember(Name = "Categories")]
        public List<Category> Categories { get; set; }

        /// <summary>
        /// Gets or sets the sub-categories the content is available in
        /// </summary>
        [DataMember(Name = "SubCategories", IsRequired = false)]
        public List<Category> SubCategories
        {
            get
            {
                return this.subcategories ?? new List<Category>();
            }

            set
            {
                this.subcategories = value;
            }
        }

        /// <summary>
        /// Gets or sets the sources of the content
        /// </summary>
        [DataMember(Name = "Source")]
        public List<Source> Source { get; set; }

        /// <summary>
        /// Gets or sets related content pertaining to potential interests
        /// </summary>
        [DataMember(Name = "Interests")]
        public ContentInterests Interests { get; set; }

        /// <summary>
        /// Gets or sets the time it takes to read/view the content
        /// </summary>
        [DataMember(Name = "ReadTime")]
        public string ReadTime
        {
            get
            {
                return string.IsNullOrEmpty(this.readTime) ? "0" : this.readTime;
            }

            set
            {
                this.readTime = value;
            }
        }

        /// <summary>
        /// Gets or sets an image for the content
        /// </summary>
        [DataMember(Name = "Images")]
        public List<Element> Images { get; set; }

        /// <summary>
        /// Gets or sets an image identifier for the content
        /// </summary>
        [DataMember]
        public string ImageId { get; set; }

        /// <summary>
        /// Gets or sets a value indicating whether the content has been marked as liked
        /// </summary>
        [DataMember(Name = "IsLiked")]
        public bool IsLiked { get; set; }

        /// <summary>
        /// Gets or sets a value indicating whether the content has been marked as saved
        /// </summary>
        [DataMember(Name = "IsSaved")]
        public bool IsSaved { get; set; }

        /// <summary>
        /// Gets or sets the author
        /// </summary>
        [DataMember]
        public Person Author { get; set; }

        /// <summary>
        /// Gets or sets the date the content was posted
        /// </summary>
        [IgnoreDataMember]
        public DateTime Date
        {
            get
            {
                this.FormattedDate = string.IsNullOrEmpty(this.FormattedDate) ? "1900-01-01" : this.FormattedDate;
                return DateTime.ParseExact(this.FormattedDate, "yyyy-MM-dd", CultureInfo.InvariantCulture);
            }

            set
            {
                this.FormattedDate = value.ToString("yyyy-MM-dd");
            }
        }

        /// <summary>
        /// Gets or sets the date time
        /// This can be private because it's only ever accessed by the serializer.
        /// </summary>
        [DataMember(Name = "Date")]
        private string FormattedDate { get; set; }

        /// <summary>
        /// Gets or sets the date the content was updated
        /// </summary>
        [IgnoreDataMember]
        public DateTime UpdateDate
        {
            get
            {
                this.FormattedUpdateDate = string.IsNullOrEmpty(this.FormattedUpdateDate) ? "1900-01-01" : this.FormattedUpdateDate;
                return DateTime.ParseExact(this.FormattedUpdateDate, "yyyy-MM-dd", CultureInfo.InvariantCulture);
            }

            set
            {
                this.FormattedUpdateDate = value.ToString("yyyy-MM-dd");
            }
        }

        /// <summary>
        /// Gets or sets the date time
        /// This can be private because it's only ever accessed by the serializer.
        /// </summary>
        [DataMember(Name = "UpdateDate")]
        private string FormattedUpdateDate { get; set; }

        /// <summary>
        /// Calculate the read time
        /// </summary>
        public void CalculateReadTime()
        {
            string text = string.Empty;

            if (this.Elements != null)
            {
                var list = (from x in this.Elements
                            where x.Type.Equals("HTML", StringComparison.OrdinalIgnoreCase)
                            select x.Value).ToList() ?? new List<string>();
                text = this.StripHtml(string.Join(string.Empty, list));
            }

            this.ReadTime = this.CalculateReadTime(text);
        }

        /// <summary>
        /// Calculate the read time
        /// </summary>
        private string CalculateReadTime(string x)
        {
            if (string.IsNullOrEmpty(x))
            {
                return "0";
            }

            var words = x.Split(' ').Count();

            // define reading time in minutes
            var readTimeInMinutes = System.Math.Round((double)((words / (270 / 60)) / 60));

            return readTimeInMinutes > 0 ? readTimeInMinutes.ToString() : "0";
        }

        /// <summary>
        /// Remove html tags 
        /// <seealso cref="http://stackoverflow.com/questions/19523913/remove-html-tags-from-string-including-nbsp-in-c-sharp"/>
        /// </summary>
        private string StripHtml(string x)
        {
            x = x ?? string.Empty;
            string noHTML = Regex.Replace(x, @"<[^>]+>|&nbsp;", "").Trim();
            string noHTMLNormalised = Regex.Replace(noHTML, @"\s{2,}", " ");
            return noHTMLNormalised;
        }
    }
}
