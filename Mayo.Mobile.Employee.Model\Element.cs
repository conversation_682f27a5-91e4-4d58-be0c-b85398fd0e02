﻿//-----------------------------------------------------------------------
// <copyright file="Element.cs" company="Mayo Foundation">
//     Copyright © Mayo Foundation. All rights reserved.
// </copyright>
//-----------------------------------------------------------------------

namespace Mayo.Mobile.Employee.Model
{
    using System;
    using System.Collections.Generic;
    using System.Linq;
    using System.Runtime.Serialization;
    using System.Text;

    /// <summary>
    /// Initializes a new instance of the Element class.
    /// </summary>
    [DataContract(Namespace = "http://www.mayo.edu/MayoMobile")]
    public class Element
    {
        /// <summary>
        /// Initializes a new instance of the Element class.
        /// </summary>
        /// <param name="type">The type</param>
        /// <param name="value">The value</param>
        public Element(string type, string value)
        {
            this.Type = type;
            this.Value = value;
        }

        /// <summary>
        /// Gets or sets the type
        /// </summary>
        [DataMember(Name = "Type")]
        public string Type { get; set; }

        /// <summary>
        /// Gets or sets the value
        /// </summary>
        [DataMember(Name = "Value")]
        public string Value { get; set; }
    }
}
