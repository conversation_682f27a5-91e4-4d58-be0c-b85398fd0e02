﻿//-----------------------------------------------------------------------
// <copyright file="IDirectoryStorageRepository.cs" company="Mayo Foundation">
//     Copyright © Mayo Foundation. All rights reserved.
// </copyright>
//-----------------------------------------------------------------------

namespace Mayo.Mobile.Employee.Directory.Model
{
    using Mayo.Mobile.Employee.Model;
    using System;
    using System.Collections.Generic;
    using System.Text;
    using System.Threading.Tasks;

    /// <summary>
    /// Initializes a new instance of the IDirectoryStorageRepository class.
    /// </summary>
    public interface IDirectoryStorageRepository
    {
        /// <summary>
        /// Get the favorited items for a person
        /// </summary>
        /// <param name="userId">The user identifier</param>
        /// <returns>The <see cref="Task{TResult}"/></returns>
        Task<List<Person>> FavoritesAsync(string userId);

        /// <summary>
        /// Save the favorites for a person
        /// </summary>
        /// <param name="userId">The user identifier</param>
        /// <param name="list">The list of <see cref="Person"/></param>
        /// <returns>The <see cref="Task{TResult}"/></returns>
        Task SaveFavoritesAsync(string userId, List<Person> list);

        /// <summary>
        /// Get the status
        /// </summary>
        /// <param name="userId">The user identifier</param>
        /// <returns>The <see cref="Task{TResult}"/></returns>
        Task<Status> StatusAsync(string userId);
    }
}
