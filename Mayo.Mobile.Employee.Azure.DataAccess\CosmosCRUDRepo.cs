﻿using Mayo.Mobile.Employee.Azure.DataAccess.Interfaces;
using Mayo.Mobile.Employee.Azure.DataAccess.Models;
using Microsoft.Azure.Cosmos;

namespace Mayo.Mobile.Employee.Azure.DataAccess
{
    public class CosmosCRUDRepo: ICosmosCRUDRepository
    {
        private CosmosClient cosmosClient;
        private Database database;
        private Container container;

        /// <summary>
        /// Constructor used for testing purposes
        /// Will create the database and container is they don't already exist
        /// </summary>
        /// <param name="options"></param>
        public CosmosCRUDRepo(CosmosCRUDRepoOptions configurationOptions)
        {
            var clientOptions = new CosmosClientOptions()
            {

                SerializerOptions = new CosmosSerializationOptions()
                {
                    IgnoreNullValues = true
                }
            };
            cosmosClient = new CosmosClient(configurationOptions.CosmosConnectionString, clientOptions);
            database = cosmosClient.CreateDatabaseIfNotExistsAsync(configurationOptions.CosmosDatabaseName).Result;
            container = database.DefineContainer(configurationOptions.CosmosContainerName, $"/{configurationOptions.CosmosPartitionKeyProperty}").CreateIfNotExistsAsync(400).Result;
        }

        /// <summary>
        /// For use with Testing ONLY!!
        /// </summary>
        /// <param name="containerName"></param>
        /// <returns></returns>
        public void DeleteContainer(string containerName)
        {
            container = database.GetContainer(containerName).DeleteContainerAsync().Result;
        }
    }
}
