﻿//-----------------------------------------------------------------------
// <copyright file="SearchRepository.cs" company="Mayo Foundation">
//     Copyright © Mayo Foundation. All rights reserved.
// </copyright>
//-----------------------------------------------------------------------

namespace Mayo.Mobile.Employee.Azure.Search.Model
{
    using System;
    using System.Collections.Generic;
    using System.Linq;
    using System.Text;
    using System.Threading.Tasks;

    /// <summary>
    /// Initializes a new instance of the SearchRepository class.
    /// </summary>
    public class SearchRepository : ISearchRepository, IDisposable
    {
        /// <summary>
        /// The <see cref="AzureRepository"/>
        /// </summary>
        private readonly AzureRepository repository = null;

        /// <summary>
        /// Detect redundant calls
        /// </summary>
        protected bool disposedValue = false;

        /// <summary>
        /// Initializes a new instance of the AzureRepository class.
        /// </summary>
        /// <param name="serviceName">The service name</param>
        /// <param name="queryApiKey">The query api key</param>
        public SearchRepository(string serviceName, string queryApiKey)
        {
            this.repository = new AzureRepository(serviceName, queryApiKey);
        }

        /// <summary>
        /// Search the content
        /// </summary>
        /// <param name="name">The name of the index to search</param>
        /// <param name="query">The text querying for</param>
        /// <param name="filter">The search filter</param>
        /// <returns>The <see cref="Task{TResult}"/></returns>
        public async Task<List<Mayo.Mobile.Employee.Model.SearchResult>> SearchAsync(string name, string query, string filter = null)
        {
            return await this.repository.SearchAsync(name, query);
        }

        /// <summary>
        /// Search the content
        /// </summary>
        /// <param name="name">The name of the index to search</param>
        /// <param name="query">The text querying for</param>
        /// <returns>The <see cref="Task{TResult}"/></returns>
        public async Task<List<Mayo.Mobile.Employee.Model.SearchResult>> SuggestAsync(string name, string query)
        {
            return await this.repository.SuggestAsync(name, query);
        }

        /// <summary>
        /// This code added to correctly implement the disposable pattern.
        /// </summary>
        /// <param name="disposing">A value indicating whether the class is being disposed</param>
        protected virtual void Dispose(bool disposing)
        {
            if (!disposedValue)
            {
                if (disposing)
                {
                    if (this.repository != null)
                    {
                        this.repository.Dispose();
                    }
                }

                disposedValue = true;
            }
        }

        /// <summary>
        /// This code added to correctly implement the disposable pattern.
        /// </summary>
        public void Dispose()
        {
            Dispose(true);
        }
    }
}
