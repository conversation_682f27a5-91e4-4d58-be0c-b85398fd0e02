﻿//-----------------------------------------------------------------------
// <copyright file="ISearchService.cs" company="Mayo Foundation">
//     Copyright © Mayo Foundation. All rights reserved.
// </copyright>
//-----------------------------------------------------------------------

namespace Mayo.Mobile.Employee.Search.Model
{
    using System.Collections.Generic;
    using System.Threading.Tasks;
    using Mayo.Mobile.Employee.Model;

    /// <summary>
    /// Initializes a new instance of the ISearchService class.
    /// </summary>
    public interface ISearchService
    {
        /// <summary>
        /// Search the content
        /// </summary>
        /// <param name="name">The name of the index to search</param>
        /// <param name="query">The text querying for</param>
        /// <param name="version">The application version</param>
        /// <returns>The <see cref="Task{TResult}"/></returns>
        Task<List<SearchResult>> SearchAsync(string name, string query, double version = 0.0);

        /// <summary>
        /// Search the content
        /// </summary>
        /// <param name="name">The name of the index to search</param>
        /// <param name="query">The text querying for</param>
        /// <returns>The <see cref="Task{TResult}"/></returns>
        Task<List<SearchResult>> SuggestAsync(string name, string query);
    }
}
