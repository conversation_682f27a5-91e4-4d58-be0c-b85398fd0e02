﻿//-----------------------------------------------------------------------
// <copyright file="ApplicationService.cs" company="Mayo Foundation">
//     Copyright © Mayo Foundation. All rights reserved.
// </copyright>
//-----------------------------------------------------------------------

namespace Mayo.Mobile.Employee.Application.Model
{
    using System;
    using System.Collections.Generic;
    using System.Linq;
    using System.Text;
    using System.Threading.Tasks;
    using Mayo.Mobile.Application.Model;
    using Mayo.Mobile.Application.Models;
    using Newtonsoft.Json.Linq;

    /// <summary>
    /// Initializes a new instance of the ApplicationService class.
    /// </summary>
    public class ApplicationService : IApplicationService
    {

        private IApplicationRepository ApplicationRepo;

        public ApplicationService(IApplicationRepository applicationRepo)
        {
            ApplicationRepo = applicationRepo;
        }

        /// <summary>
        /// Get the data from the blob
        /// </summary>
        /// <param name="name">The name of the text</param>
        /// <returns>The <see cref="Task{TResult}"/></returns>
        public async Task<string> GetDataAsync(string name)
        {
            return await this.ApplicationRepo.GetDataAsync(name);
        }

        /// <summary>
        /// Gets the <see cref="Device"/>
        /// </summary>
        /// <param name="id">The device identifier</param>
        /// <returns>The <see cref="Task{TResult}"/></returns>
        public async Task<Device> GetDeviceAsync(string id)
        {
            return await this.ApplicationRepo.GetDeviceAsync(id);
        }

        /// <summary>
        /// Update the device information
        /// </summary>
        /// <param name="device">The <see cref="Device"/></param>
        /// <returns>The updated <see cref="Task{TResult}"/></returns>
        public async Task<(Device Device, ApplicationUpdate Update)> SaveDeviceAsync(Device device)
        {
            device = await this.ApplicationRepo.SaveAsync(device);

            var j = JObject.Parse(await this.ApplicationRepo.GetDataAsync("EMPLOYEEAPPVERSIONS.json"));

            var array = new JArray((from x in (JArray)j["Versions"]
                                    where (bool)x["IsActive"] == true
                                    select x).ToList());

            var versions = (from x in array
                            where ((string)x["OS"]).Equals(device.OperatingSystem) == true
                            select (Id: (string)x["Id"], Name: (string)x["Name"], Description: (string)x["Description"], Version: (string)x["Version"], URL: (string)x["URL"], Status: (string)x["Status"], Message: (string)x["Message"])).ToList();

            var update = new ApplicationUpdate(versions, device.Application?.Version, device.OperatingSystem);
            return (device, update);
        }

        /// <summary>
        /// Save the user and device entity/>
        /// </summary>
        /// <param name="userId">The user identifier</param>
        /// <param name="deviceId">The device identifier</param>
        /// <returns>The <see cref="Task"/></returns>
        public async Task SaveAsync(string userId, string deviceId)
        {
            await this.ApplicationRepo.SaveAsync(userId, deviceId);
        }
    }
}