﻿//-----------------------------------------------------------------------
// <copyright file="UserRepository.cs" company="Mayo Foundation">
//     Copyright © Mayo Foundation. All rights reserved.
// </copyright>
//-----------------------------------------------------------------------

namespace Mayo.Mobile.Employee.Content.Model
{
    using System;
    using System.Collections.Generic;
    using System.Threading.Tasks;
    using Mayo.Mobile.Application.Azure.DataAccess.Interfaces;
    using Mayo.Mobile.Employee.Model;
    using Microsoft.Azure.Cosmos.Table;

    public class UserRepository : IUserRepository
    {
        private ITableStorageRepository TableStorageRepo;

        public UserRepository(ITableStorageRepository tableStorageRepo)
        {
            TableStorageRepo = tableStorageRepo;
        }


        /// <summary>
        /// Save the <see cref="Identity"/>
        /// </summary>
        /// <param name="i">The <see cref="Identity"/></param>
        /// <returns>The <see cref="Task"/></returns>
        public async Task SaveAsync(Identity i)
        {
            var e = new DynamicTableEntity(
                i.UserId,
                i.AccessToken,
                "*",
                new Dictionary<string, EntityProperty>
                {
                    { "Id", new EntityProperty(i.Id) },
                    { "UserId", new EntityProperty(i.UserId) },
                    { "IsActive", new EntityProperty(i.IsActive) },
                    { "IsValidated", new EntityProperty(i.IsValidated) },
                    { "LastName", new EntityProperty(i.LastName) },
                    { "RefreshToken", new EntityProperty(i.RefreshToken) },
                    { "DeviceId", new EntityProperty(i.DeviceId) },
                    { "Email", new EntityProperty(i.Email) },
                    { "ExpirationDate", new EntityProperty(i.ExpirationDate) },
                    { "FirstName", new EntityProperty(i.FirstName) },
                    { "FullName", new EntityProperty(i.FullName) }
                });

            await TableStorageRepo.SaveAsync("UserStorage", e);
        }

        /// <summary>
        /// Save the user authentication action. If they authenticated with user credentials or did a refresh
        /// </summary>
        /// <param name="userId">The user identifier</param>
        /// <param name="action">The action the user performed</param>
        /// <param name="accessToken">The access token</param>
        /// <param name="refreshToken">The refresh token</param>
        /// <returns>The <see cref="Task"/></returns>
        public async Task SaveAsync(string userId, string action, string accessToken, string refreshToken)
        {
            var e = new DynamicTableEntity(
                userId,
                action,
                "*",
                new Dictionary<string, EntityProperty>
                {
                    { "UserId", new EntityProperty(userId) },
                    { "IsActive", new EntityProperty(true) },
                    { "RefreshToken", new EntityProperty(refreshToken) },
                    { "AccessToken", new EntityProperty(accessToken) }
                });

            await TableStorageRepo.SaveAsync("UserStorage", e);
        }

        /// <summary>
        /// Get the <see cref="Identity"/>
        /// </summary>
        /// <param name="userId">The user identifier</param>
        /// <param name="accessToken">The access token</param>
        /// <param name="refreshToken">The refresh token</param>
        /// <returns>The <see cref="Task"/></returns>
        public async Task<Identity> IdentityAsync(string userId, string accessToken, string refreshToken = null)
        {
            var d = await TableStorageRepo.GetEntityAsync("UserStorage", userId, accessToken);
            if (d == null)
            {
                throw new Exception("Unable to validate token and user");
            }
            else
            {
                var i = new Identity
                {
                    Id = this.GetStringValue(d.Properties, "Id"),
                    AccessToken = d.RowKey,
                    DeviceId = this.GetStringValue(d.Properties, "DeviceId"),
                    Email = this.GetStringValue(d.Properties, "Email"),
                    ExpirationDate = this.GetStringValue(d.Properties, "ExpirationDate"),
                    FirstName = this.GetStringValue(d.Properties, "FirstName"),
                    FullName = this.GetStringValue(d.Properties, "FullName"),
                    IsActive = this.GetBoolValue(d.Properties, "IsActive"),
                    IsValidated = this.GetBoolValue(d.Properties, "IsValidated"),
                    LastName = this.GetStringValue(d.Properties, "LastName"),
                    RefreshToken = this.GetStringValue(d.Properties, "RefreshToken"),
                    UserId = this.GetStringValue(d.Properties, "UserId"),
                };

                if (i.IsActive == false)
                {
                    throw new Exception("Token is not active");
                }

                if (refreshToken != null && i.RefreshToken.Equals(refreshToken) == false)
                {
                    throw new Exception("Invalid refresh token");
                }

                return i;
            }
        }

        /// <summary>
        /// Get the last time the user authenticated with username and passwors
        /// </summary>
        /// <param name="userId">The user identifier</param>
        /// <param name="accessToken">The access token</param>
        /// <param name="refreshToken">The refresh token</param>
        /// <returns>The <see cref="Task"/></returns>
        public async Task<DateTimeOffset> LastLogonTimeAsync(string userId)
        {
            var d = await TableStorageRepo.GetEntityAsync("UserStorage", userId, "LOGON");
            if (d == null)
            {
                throw new Exception();
            }
            else
            {
                return d.Timestamp;
            }
        }

        /// <summary>
        /// Save the status
        /// </summary>
        /// <param name="userId">The user identifier</param>
        /// <param name="status">The <see cref="Status"/></param>
        /// <returns>The <see cref="Task"/></returns>
        public async Task SaveAsync(string userId, Status status)
        {
            var e = new DynamicTableEntity(
                userId,
                "STATUS",
                "*",
                new Dictionary<string, EntityProperty>
                {
                    { "Id", new EntityProperty(status.Id) },
                    { "Name", new EntityProperty(status.Name) },
                    { "Description", new EntityProperty(status.Description) },
                    { "Emoji", new EntityProperty(status.Emoji) },
                    { "StartDate", new EntityProperty(status.StartDate) },
                    { "EndDate", new EntityProperty(status.EndDate) },
                    { "IsActive", new EntityProperty(status.IsActive) }
                });

            await TableStorageRepo.SaveAsync("UserStorage", e);
        }

        /// <summary>
        /// Get the status
        /// </summary>
        /// <param name="userId">The user identifier</param>
        /// <returns>The <see cref="Task{TResult}"/></returns>
        public async Task<Status> StatusAsync(string userId)
        {
            var d = await TableStorageRepo.GetEntityAsync("UserStorage", userId, "STATUS");
            if (d == null)
            {
                return new Status();
            }
            else
            {
                var s = new Status
                {
                    PersonId = userId,
                    Description = this.GetStringValue(d.Properties, "Description"),
                    Emoji = this.GetStringValue(d.Properties, "Emoji"),
                    Id = this.GetStringValue(d.Properties, "Id"),
                    EndDate = this.GetStringValue(d.Properties, "EndDate"),
                    IsActive = this.GetBoolValue(d.Properties, "IsActive"),
                    Name = this.GetStringValue(d.Properties, "Name"),
                    StartDate = this.GetStringValue(d.Properties, "StartDate")
                };

                if (s.IsActive == false)
                {
                    s = new Status();
                }

                if (string.IsNullOrEmpty(s.StartDate) == false)
                {
                    var date = DateTimeOffset.Parse(s.StartDate);
                    s = (date.UtcDateTime.CompareTo(DateTime.UtcNow) < 0) ? s : new Status();
                }

                if (string.IsNullOrEmpty(s.EndDate) == false)
                {
                    var date = DateTimeOffset.Parse(s.EndDate);
                    s = (date.UtcDateTime.CompareTo(DateTime.UtcNow) > 0) ? s : new Status();
                }

                return s;
            }
        }

        /// <summary>
        /// Gets the string value from the dictionary given a key
        /// </summary>
        /// <param name="dictionary">The <see cref="IDictionary{TKey, TValue}"/></param>
        /// <param name="key">The key</param>
        /// <returns>The value</returns>
        private string GetStringValue(IDictionary<string, EntityProperty> dictionary, string key)
        {
            return dictionary.TryGetValue(key, out EntityProperty value) ? value.StringValue : string.Empty;
        }

        /// <summary>
        /// Gets the bool value from the dictionary given a key
        /// </summary>
        /// <param name="dictionary">The <see cref="IDictionary{TKey, TValue}"/></param>
        /// <param name="key">The key</param>
        /// <returns>The value</returns>
        private bool GetBoolValue(IDictionary<string, EntityProperty> dictionary, string key)
        {
            return dictionary.TryGetValue(key, out EntityProperty value)
                ? value.BooleanValue ?? false
                : false;
        }
    }
}