﻿using Mayo.Mobile.Employee.Azure.DataAccess.Models;
using Mayo.Mobile.Employee.Location.Model;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace Mayo.Mobile.Employee.Azure.DataAccess.Interfaces
{
    public interface IWorkOrderLoggingRepo
    {
        /// <summary>
        /// Gets or Creates the WorkOrder Document 
        /// </summary>
        /// <param name="issue"><see cref="FacilityIssue"></param>
        /// <returns></returns>
        public Task<FacilityWorkOrderDocument> GetCreateWorkOrderDocumentAsync(FacilityWorkOrderQueueMessage issue);

        /// <summary>
        /// Returns the WorkOrderDocument
        /// </summary>
        /// <param name="documentId">Id of the document in the documentDB</param>
        /// <param name="partitionKey">Key to find the document in the documentDB</param>
        /// <returns></returns>
        public Task<FacilityWorkOrderDocument> GetWorkOrderDocumentAsync(string documentId, string partitionKey);

        /// <summary>
        /// Used to update a FacilityWorkOrderDocument in our database
        /// </summary>
        /// <param name="workOrder"></param>
        /// <returns></returns>
        public Task<FacilityWorkOrderDocument> UpdateWorkOrderDocumentAsync(FacilityWorkOrderDocument workOrder);
    }
}
