﻿// -----------------------------------------------------------------------
// <copyright file="MockDirectoryService.cs" company="Mayo Clinic">
//     Copyright © Mayo Foundation. All rights reserved.
// </copyright>
// -----------------------------------------------------------------------

namespace Mayo.Mobile.Employee.Directory.Model
{
    using System;
    using System.Collections.Generic;
    using System.IO;
    using System.Linq;
    using System.Threading.Tasks;
    using Mayo.Mobile.Application.Azure.DataAccess.Interfaces;
    using Mayo.Mobile.Employee.Model;

    /// <summary>
    /// Initializes a new instance of the MockDirectoryService class.
    /// </summary>
    public class MockDirectoryService : IDirectoryService
    {
        private IBlobStorageRepository BlobStorageRepo;

        /// <summary>
        /// Initializes a new instance of the MockDirectoryService class.
        /// </summary>
        /// <param name="repository">The <see cref="StorageRepository"/></param>
        public MockDirectoryService(IBlobStorageRepository blobStorageRepo)
        {
            BlobStorageRepo = blobStorageRepo;
        }

        /// <summary>
        /// Gets or sets the <see cref="IDirectoryStorageRepository"/>
        /// </summary>
        //public IDirectoryStorageRepository Repository { get; set; }

        /// <summary>
        /// Search person or phone number
        /// </summary>
        /// <param name="searchTerm">The search term</param>
        /// <returns>The <see cref="Task{List{Employee.Model.SearchResult}}"/></returns>
        public async Task<List<SearchResult>> SearchPersonAsync(string searchTerm)
        {
            var list = await BlobStorageRepo.GetBlobAsync<List<Mayo.Mobile.Employee.Model.SearchResult>>("user", "search.json");
            return list.Where(x => x.Name.IndexOf(searchTerm, StringComparison.OrdinalIgnoreCase) > 0).ToList();
        }

        /// <summary>
        /// Get the persons information
        /// </summary>
        /// <param name="personId">The person identifier</param>
        /// <returns>The <see cref="Task{TResult}"/></returns>
        public async Task<Mayo.Mobile.Employee.Model.Employee> GetPersonAsync(string personId)
        {
            return await BlobStorageRepo.GetBlobAsync<Mayo.Mobile.Employee.Model.Employee>($"user\\{personId}", $"{personId}.json");
        }

        /// <summary>
        /// Sends page to pager number
        /// </summary>
        /// <param name="pagerNumber">The pager number</param>
        /// <param name="entity">The entity</param>
        /// <param name="message">The message</param>
        /// <param name="senderPersonId">The sender person identifier</param>
        /// <returns>The <see cref="Task"/></returns>
        public async Task PageAsync(string pagerNumber, string entity, string message, string senderPersonId)
        {
            await Task.Delay(0);
        }

        /// <summary>
        /// Sends page to pager number
        /// </summary>
        /// <param name="pagerNumber">The pager number</param>
        /// <param name="entity">The entity</param>
        /// <param name="message">The message</param>
        /// <param name="senderPersonId">The sender person identifier</param>
        /// <returns>The <see cref="Task"/></returns>
        public async Task PageAsyncUsingSP(string pagerNumber, string entity, string message, string senderPersonId)
        {
            await Task.Delay(0);
        }

        /// <summary>
        /// Gets person photo
        /// </summary>
        /// <param name="personId">The user id</param>
        /// <returns>The <see cref="Task{TResult}"/></returns>
        public async Task<Stream> GetPhotoAsync(string personId)
        {
            return await BlobStorageRepo.GetBlobAsync($"user\\{personId}", $"{personId}.jpg");
        }

        /// <summary>
        /// Gets the favorites list
        /// </summary>
        /// <param name="userId">The user identifier</param>
        /// <param name="count">The number of items</param>
        /// <returns>The <see cref="Task{TResult}"/></returns>
        public async Task<List<Person>> GetFavoritesAsync(string userId, int? count)
        {
            return await BlobStorageRepo.GetBlobAsync<List<Person>>($"user/{userId}/favorites", "people.json") ?? new List<Person>();
        }

        /// <summary>
        /// Add to the favorites list
        /// </summary>
        /// <param name="userId">The user identifier</param>
        /// <param name="p">The <see cref="Person"/></param>
        /// <returns>The <see cref="Task"/></returns>
        public async Task AddFavoriteAsync(string userId, Person p)
        {
            await Task.Delay(0);
        }

        /// <summary>
        /// Delete from the favorites list
        /// </summary>
        /// <param name="userId">The user identifier</param>
        /// <param name="p">The <see cref="Person"/></param>
        /// <returns>The <see cref="Task"/></returns>
        public async Task DeleteFavoriteAsync(string userId, Person p)
        {
            await Task.Delay(0);
        }

        /// <summary>
        /// Get the status
        /// </summary>
        /// <param name="userId">The user identifier</param>
        /// <returns>The <see cref="Task{TResult}"/></returns>
        public async Task<Status> StatusAsync(string userId)
        {
            await Task.Delay(0);
            return new Status();
        }
    }
}
