﻿//-----------------------------------------------------------------------
// <copyright file="IContentStorageRepository.cs" company="Mayo Foundation">
//     Copyright © Mayo Foundation. All rights reserved.
// </copyright>
//-----------------------------------------------------------------------

namespace Mayo.Mobile.Employee.Content.Model
{
    using System;
    using System.Collections.Generic;
    using System.IO;
    using System.Text;
    using System.Threading.Tasks;

    /// <summary>
    /// Initializes a new instance of the IContentStorageRepository class.
    /// </summary>
    public interface IContentStorageRepository
    {
        /// <summary>
        /// Get the list of blobs in a container
        /// </summary>
        /// <param name="name">The name of the container</param>
        /// <returns>The <see cref="Task{TResult}"/></returns>
        Task<List<string>> GetBlobsAsync(string container);

        /// <summary>
        /// Get the contents of a blob
        /// </summary>
        /// <typeparam name="T">The type of serialized content in the blob</typeparam>
        /// <param name="container">The name of the containter</param>
        /// <param name="name">The name of the blob</param>
        /// <returns></returns>
        Task<T> GetBlobAsync<T>(string container, string name);

        /// <summary>
        /// Get the blob contents as a string
        /// </summary>
        /// <param name="container">The blob container name</param>
        /// <param name="name">The name of the blob</param>
        /// <returns>The blob contents</returns>
        Task<string> GetBlobContentsAsync(string containerName, string blobName);

        /// <summary>
        /// Get the items for the Sections package
        /// </summary>
        /// <returns>The <see cref="Task{TResult}"/></returns>
        Task<Stream> ImageAsync(string name);

        /// <summary>
        /// The content
        /// </summary>
        /// <param name="id">The category identifier</param>
        /// <returns>The list of <see cref="PackageItem"/></returns>
        Task<PackageItem> ItemAsync(string partitionKey, string rowKey);

        /// <summary>
        /// The list of content
        /// </summary>
        /// <param name="id">The category identifier</param>
        /// <returns>The list of <see cref="PackageItem"/></returns>
        Task<List<PackageItem>> ItemsAsync(string id, int count = 100);
        /// <summary>
        /// Get the items for the Campuses package
        /// </summary>
        /// <returns>The <see cref="Task{TResult}"/></returns>
        Task<List<PackageItem>> CampusesAsync();

        /// <summary>
        /// Get the items for the Sections package
        /// </summary>
        /// <returns>The <see cref="Task{TResult}"/></returns>
        Task<List<PackageItem>> SectionsAsync();

        /// <summary>
        /// Get the favorited items for a person
        /// </summary>
        /// <param name="userId">The user identifier</param>
        /// <returns>The <see cref="Task{TResult}"/></returns>
        Task<List<PackageItem>> FavoritesAsync(string userId);

        /// <summary>
        /// Save the favorites for a person
        /// </summary>
        /// <param name="userId">The user identifier</param>
        /// <param name="list">The list of <see cref="PackageItem"/></param>
        /// <returns>The <see cref="Task{TResult}"/></returns>
        Task SaveFavoritesAsync(string userId, List<PackageItem> list);

        /// <summary>
        /// Save the blob block to the container 
        /// </summary>
        /// <param name="containerName">The blob container</param>
        /// <param name="name">The name of the blob block</param>
        /// <param name="s">The contents of the blob</param>
        Task SaveAsync(string containerName, string name, string s);

        /// <summary>
        /// Save the blob block to the container 
        /// </summary>
        /// <typeparam name="T">The type of the object</typeparam>
        /// <param name="containerName">The blob container</param>
        /// <param name="name">The name of the blob block</param>
        /// <param name="content">The contents of the blob</param>
        Task SaveAsync<T>(string containerName, string name, T content);

        /// <summary>
        /// Save the blob block to the container 
        /// </summary>
        /// <param name="containerName">The blob container</param>
        /// <param name="name">The name of the blob block</param>
        /// <param name="s">The contents of the blob</param>
        Task SaveStreamAsync(string containerName, string name, Stream stream);

        /// <summary>
        /// Delete all entities in a partition assuming a manageable count
        /// </summary>
        /// <param name="id">The partition key</param>
        /// <returns>The <see cref="Task"/></returns>
        Task DeleteEntitiesAsync(string id);
    }
}
