﻿using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Text;

namespace Mayo.Mobile.Employee.Facilities.DataAccess.Models
{
    public class FacilityCreateWOResponse
    {
        [JsonProperty("result")]
        public List<FacilityCreateWOResult> Result { get; set; }
    }

    public class FacilityWOAttachmentResponse
    {
        [JsonProperty("result")]
        public FacilityWOAttachmentResult Result { get; set; }
    }

    /// <summary>
    /// Generic Facility's Error Response
    /// </summary>
    public class FacilityErrorResponse
    {
        [JsonProperty("error")]
        public FacilityWOError Error { get; set; }

        [JsonProperty("status")]
        public string Status { get; set; }
    }

    /// <summary>
    /// The Response object used with successful Facility Create Work Order Requests
    /// </summary>
    public class FacilityCreateWOResult
    {
        [JsonProperty("sys_id")]
        public string SysId { get; set; }

        [JsonProperty("wo_number")]
        public string WoNumber { get; set; }
    }

    /// <summary>
    /// The Response object used with successful Facility Work Order Attachment Requests
    /// </summary>
    public class FacilityWOAttachmentResult
    {
        [JsonProperty("size_bytes")]
        public string SizeBytes { get; set; }

        [JsonProperty("file_name")]
        public string FileName { get; set; }

        [JsonProperty("sys_mod_count")]
        public string SysModCount { get; set; }

        [JsonProperty("sys_updated_on")]
        public string SysUpdatedOn { get; set; }

        [JsonProperty("sys_tags")]
        public string SysTags { get; set; }

        [JsonProperty("table_name")]
        public string TableName { get; set; }

        [JsonProperty("sys_id")]
        public string SysId { get; set; }

        [JsonProperty("sys_updated_by")]
        public string SysUpdatedBy { get; set; }

        [JsonProperty("download_link")]
        public string DownloadLink { get; set; }

        [JsonProperty("content_type")]
        public string ContentType { get; set; }

        [JsonProperty("sys_created_on")]
        public string SysCreatedOn { get; set; }

        [JsonProperty("size_compressed")]
        public string SizeCompressed { get; set; }

        [JsonProperty("compressed")]
        public string Compressed { get; set; }

        [JsonProperty("state")]
        public string State { get; set; }

        [JsonProperty("table_sys_id")]
        public string TableSysId { get; set; }

        [JsonProperty("chunk_size_bytes")]
        public string ChunkSizeBytes { get; set; }

        [JsonProperty("hash")]
        public string Hash { get; set; }

        [JsonProperty("sys_created_by")]
        public string SysCreatedBy { get; set; }
    }

    public class FacilityWOError
    {
        [JsonProperty("detail")]
        public string Detail { get; set; }

        [JsonProperty("message")]
        public string Message { get; set; }
    }

}
