﻿using Mayo.Mobile.Employee.Azure.DataAccess.Models;
using Mayo.Mobile.Employee.Location.Model;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace Mayo.Mobile.Employee.Azure.DataAccess.Interfaces
{
    public interface IFacilitiesLocationRepo
    {
        /// <summary>
        /// Returns the a list of facility's campus
        /// </summary>
        /// <returns></returns>
        public Task<List<Campus>> GetCampusListAsync();

        /// <summary>
        /// Returns the a list of facility's buildings associated with the campusId
        /// </summary>
        /// <param name="campusId"></param>
        /// <returns></returns>
        public Task<List<Building>> GetBuildingListAsync(string campusId);

        /// <summary>
        /// Returns the a list of facility's floors associated with the campusId and buildingId
        /// </summary>
        /// <param name="campusId"></param>
        /// <param name="buildingId"></param>
        /// <returns></returns>
        public Task<List<Floor>> GetFloorListAsync(string campusId, string buildingId);

        /// <summary>
        /// Returns the a list of facility's rooms associated with the campusId, buildingId and floorId
        /// </summary>
        /// <param name="campusId">campus identifier</param>
        /// <param name="buildingId">building identifier</param>
        /// <param name="floorId">floor identifier</param>
        /// <returns></returns>
        public Task<List<Room>> GetRoomListAsync(string campusId, string buildingId, string floorId);

        /// <summary>
        /// Returns the a list of facility's campus
        /// </summary>
        /// <returns></returns>
        public Task InsertCampusListAsync(List<Campus> campus);

        /// <summary>
        /// Returns the a list of facility's buildings associated with the campusId
        /// </summary>
        /// <param name="campusId"></param>
        /// <param name="campusName"></param>
        /// <returns></returns>
        public Task InsertBuildingListAsync(string campusId, string campusName, List<Building> buildings);

        /// <summary>
        /// Returns the a list of facility's floors associated with the campusId and buildingId
        /// </summary>
        /// <param name="campusId"></param>
        /// <param name="buildingId"></param>
        /// <param name="buildingName"></param>
        /// <param name="locationSubType"></param>
        /// <returns></returns>
        public Task InsertFloorListAsync(string campusId, string buildingId, string buildingName, string locationSubType, List<Floor> floors);

        /// <summary>
        /// Returns the a list of facility's rooms associated with the campusId, buildingId and floorId
        /// </summary>
        /// <param name="campusId">campus identifier</param>
        /// <param name="buildingId">building identifier</param>
        /// <param name="floorId">floor identifier</param>
        /// <param name="floorName">floor Name</param>
        /// <param name="locationSubType">floor subType</param>
        /// <returns></returns>
        public Task InsertRoomListAsync(string campusId, string buildingId, string floorId, string floorName, string locationSubType, List<Room> rooms);

    }
}
