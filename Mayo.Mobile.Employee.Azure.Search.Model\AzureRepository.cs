﻿//-----------------------------------------------------------------------
// <copyright file="AzureRepository.cs" company="Mayo Foundation">
//     Copyright © Mayo Foundation. All rights reserved.
// </copyright>
//-----------------------------------------------------------------------

namespace Mayo.Mobile.Employee.Azure.Search.Model
{
    using Microsoft.Azure.Search;
    using Microsoft.Azure.Search.Models;
    using System;
    using System.Collections.Generic;
    using System.Linq;
    using System.Text;
    using System.Threading.Tasks;

    /// <summary>
    /// Initializes a new instance of the AzureRepository class.
    /// </summary>
    /// <remarks>https://docs.microsoft.com/en-us/azure/search/search-howto-dotnet-sdk</remarks>
    public class AzureRepository : ISearchRepository, IDisposable
    {
        /// <summary>
        /// The connection string
        /// </summary>
        protected string serviceName = string.Empty;

        /// <summary>
        /// The Query api key
        /// </summary>
        protected string queryApiKey = string.Empty;

        /// <summary>
        /// The <see cref="ISearchIndexClient"/>
        /// </summary>
        protected ISearchIndexClient searchClient = null;

        /// <summary>
        /// Detect redundant calls
        /// </summary>
        protected bool disposedValue = false;

        /// <summary>
        /// Initializes a new instance of the AzureRepository class.
        /// </summary>
        /// <param name="serviceName">The service name</param>
        /// <param name="queryApiKey">The query api key</param>
        public AzureRepository(string serviceName, string queryApiKey)
        {
            this.serviceName = serviceName;
            this.queryApiKey = queryApiKey;
        }

        /// <summary>
        /// Search the content
        /// </summary>
        /// <param name="name">The name of the index to search</param>
        /// <param name="query">The text querying for</param>
        /// <param name="filter">The search filter</param>
        /// <returns>The <see cref="Task{TResult}"/></returns>
        public async Task<List<Mayo.Mobile.Employee.Model.SearchResult>> SearchAsync(string name, string query, string filter = null)
        {
            this.searchClient = this.searchClient ?? new SearchIndexClient(this.serviceName, name, new SearchCredentials(queryApiKey));

            var results = await searchClient.Documents.SearchAsync<Content>(
                query,
                new SearchParameters
                {
                    Filter = filter ?? string.Empty,
                    IncludeTotalResultCount = false,
                    Top = 300
                });

            return (from x in results.Results
                    select new Mayo.Mobile.Employee.Model.SearchResult
                    {
                        //// HighLights = x.Highlights,
                        Score = x.Score.ToString(),
                        Category = new Mobile.Employee.Model.Category(x.Document.CategoryId, x.Document.CategoryName),
                        Definition = x.Document.Definition,
                        Description = x.Document.Description,
                        Id = x.Document.Id,
                        ImageUri = string.IsNullOrEmpty(x.Document.ImageId) ? null : new Uri(x.Document.ImageId),
                        Name = x.Document.Name,
                        Type = x.Document.Type,
                        Date = x.Document.CreateDatetime.ToString("s")
                    }).ToList();
        }

        /// <summary>
        /// Search the content
        /// </summary>
        /// <param name="name">The name of the index to search</param>
        /// <param name="query">The text querying for</param>
        /// <returns>The <see cref="Task{TResult}"/></returns>
        public async Task<List<Mayo.Mobile.Employee.Model.SearchResult>> SuggestAsync(string name, string query)
        {
            this.searchClient = this.searchClient ?? new SearchIndexClient(this.serviceName, name, new SearchCredentials(queryApiKey));

            var results = await searchClient.Documents.SuggestAsync<Content>(query, "contentsuggester", new SuggestParameters { Top = 100 });

            return (from x in results.Results
                    select new Mayo.Mobile.Employee.Model.SearchResult
                    {
                        Category = new Mobile.Employee.Model.Category(x.Document.CategoryId, x.Document.CategoryName),
                        Definition = x.Document.Definition,
                        Description = x.Document.Description,
                        Id = x.Document.Id,
                        ImageUri = string.IsNullOrEmpty(x.Document.ImageId) ? null : new Uri(x.Document.ImageId),
                        Name = x.Document.Name,
                        Type = x.Document.Type
                    }).ToList();
        }

        /// <summary>
        /// This code added to correctly implement the disposable pattern.
        /// </summary>
        /// <param name="disposing">A value indicating whether the class is being disposed</param>
        protected virtual void Dispose(bool disposing)
        {
            if (!disposedValue)
            {
                if (disposing)
                {
                    if (this.searchClient != null)
                    {
                        this.searchClient.Dispose();
                    }
                }

                disposedValue = true;
            }
        }

        /// <summary>
        /// This code added to correctly implement the disposable pattern.
        /// </summary>
        public void Dispose()
        {
            Dispose(true);
        }
    }
}
