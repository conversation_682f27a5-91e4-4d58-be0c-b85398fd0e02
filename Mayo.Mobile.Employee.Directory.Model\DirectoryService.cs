﻿// -----------------------------------------------------------------------
// <copyright file="DirectoryService.cs" company="Mayo Clinic">
//     Copyright © Mayo Foundation. All rights reserved.
// </copyright>
// -----------------------------------------------------------------------

namespace Mayo.Mobile.Employee.Directory.Model
{
    using System;
    using System.Collections.Generic;
    using System.Data;
    using System.Diagnostics;
    using System.IO;
    using System.Linq;
    using System.Net.Http;
    using System.Net.Http.Headers;
    using System.Net.Mail;
    using System.Text;
    using System.Text.Json;
    using System.Text.RegularExpressions;
    using System.Threading.Tasks;
    using System.Web;
    using System.Xml.Linq;
    using Mayo.Mobile.Employee.Model;
    using Microsoft.Data.SqlClient;
    using Microsoft.Extensions.Options;

    /// <summary>
    /// Initializes a new instance of the DirectoryService class.
    /// </summary>
    public class DirectoryService : IDirectoryService
    {
        // Static token caching fields shared across all instances
        private static string _cachedToken;
        private static DateTime _tokenExpirationTime = DateTime.MinValue;
        private static readonly TimeSpan _tokenCacheDuration = TimeSpan.FromHours(1);
        private static readonly object _tokenLock = new object();

        /// <summary>
        /// Constants for Apigee authentication
        /// </summary>
        private string API_KEY;
        private string API_SECRET;
        private string APIGEE_TOKEN_URL;
        private string APIGEE_API_PATH;
        private string pagerSQLConnection_MN;
        private string pagerSQLConnection_FL;
        private string pagerSQLConnection_AZ;      
        private const string APIGEE_SEARCH_SUFFIX = "/search?search=";
        private const string APIGEE_PERSON_DETAILS_SUFFIX = "/personDetails/";

        // Add this field to the class
        private static TextWriterTraceListener _traceListener;

        /// <summary>
        /// Initializes logging for the DirectoryService
        /// </summary>
        /// <param name="logFilePath">Optional path to log file</param>
        public static void InitializeLogging(string logFilePath = null)
        {
            if (logFilePath == null)
                logFilePath = Path.Combine(Path.GetTempPath(), "DirectoryServiceTests.log");
            
            _traceListener = new TextWriterTraceListener(logFilePath);
            Trace.Listeners.Add(_traceListener);
            Trace.AutoFlush = true;
            
            Trace.WriteLine($"[{DateTime.Now}] DirectoryService logging initialized");
        }

        /// <summary>
        /// Common academic and medical degrees
        /// </summary>
        private static readonly HashSet<string>  COMMON_DEGREES = new HashSet<string>(StringComparer.OrdinalIgnoreCase)
        {
            // Academic Degrees
            "Ph.D.", "M.D.", "M.S.", "M.A.", "M.B.A.", "B.S.", "B.A.", "LL.M.",
            "J.D.", "D.D.S.", "D.V.M.", "Ed.D.", "Psy.D.", "D.Min.", "D.O.",
            "M.Ed.", "M.F.A.", "M.P.H.", "M.S.W.", "M.S.N.", "Dr.P.H.",
            
            // Medical and Healthcare
            "M.B.B.S.", "M.Ch.", "F.A.C.S.", "D.M.", "D.M.D.", "Pharm.D.",
            "B.Pharm.", "M.Pharm.", "O.D.", "D.P.T.", "O.T.D.", "Au.D.",
            "S.L.P.D.", "D.N.P.", "Ph.D.N.", "B.S.N.", "A.P.R.N.", "F.N.P.",
            "C.N.M.", "C.R.N.A.", "M.S.P.H.", "D.C.", "N.D.", "D.A.O.M.",
            "D.S.W.", "D.H.Sc.", "M.H.A.",
            
            // International and Specialty
            "F.R.C.S.", "F.R.C.P.", "F.A.C.O.G.", "F.A.A.F.P.", "F.A.C.C.",
            "F.A.C.P.", "M.R.C.P.", "M.R.C.S."
        };

        /// <summary>
        /// Formats the initials for a name
        /// </summary>
        /// <remarks>
        /// https://stackoverflow.com/questions/10820273/regex-to-extract-initials-from-name
        /// </remarks>
        private readonly Func<string, string> initialsRegex = (s) =>
        {
            // first remove all: punctuation, separator chars, control chars, and numbers (unicode style regexes)
            string initials = Regex.Replace(s, @"\([^()]*\)", string.Empty);

            initials = Regex.Replace(initials, @"[\p{P}\p{S}\p{C}\p{N}]+", "");

            // Replacing all possible whitespace/separator characters (unicode style), with a single, regular ascii space.
            initials = Regex.Replace(initials, @"\p{Z}+", " ");

            // Remove all Sr, Jr, I, II, III, IV, V, VI, VII, VIII, IX at the end of names
            initials = Regex.Replace(initials.Trim(), @"\s+(?:[JS]R|I{1,3}|I[VX]|VI{0,3})$", "", RegexOptions.IgnoreCase);

            // Extract up to 2 initials from the remaining cleaned name.
            initials = Regex.Replace(initials, @"^(\p{L})[^\s]*(?:\s+(?:\p{L}+\s+(?=\p{L}))?(?:(\p{L})\p{L}*)?)?$", "$1$2").Trim();

            if (initials.Length > 2)
            {
                // Worst case scenario, everything failed, just grab the first two letters of what we have left.
                initials = initials.Substring(0, 2);
            }

            return initials.ToUpperInvariant();
        };

        /// <summary>
        /// Formats the name for sorting list
        /// </summary>
        private readonly Func<string, string> sortingName = (s) =>
        {
            // order name is given First (Nick) M. Last, Degree
            // order needed is Last, First M. (Nick), Degree
            var degree = "";
            var index = s.IndexOf(",");
            var length = s.Length - index;
            if (index > 0)
            {
                degree = s.Substring(index, length).Trim();
                s = s.Substring(0, index);
            }

            var lastName = "";
            index = s.LastIndexOf(" ");
            length = s.Length - index;
            if (index > 0)
            {
                lastName = s.Substring(index, length).Trim();
                s = s.Substring(0, index);
            }

            var middleName = "";
            index = s.LastIndexOf(".") - 1;
            length = s.Length - index;
            if (index > 0)
            {
                middleName = s.Substring(index, length).Trim();
                s = s.Substring(0, index);
            }

            var nickName = "";
            index = s.IndexOf("(");
            length = s.Length - index;
            if (index > 0)
            {
                nickName = s.Substring(index, length).Trim();
                s = s.Substring(0, index);
            }

            var firstName = s.Trim();

            var builtName = $"{lastName}, {firstName}";
            if (string.IsNullOrEmpty(middleName) == false)
            {
                builtName += $" {middleName}";
            }

            if (string.IsNullOrEmpty(nickName) == false)
            {
                builtName += $" {nickName}";
            }

            if (string.IsNullOrEmpty(degree) == false)
            {
                builtName += degree;
            }

            return builtName;
        };

        private HttpClient Client;

        private IDirectoryStorageRepository DirectoryStorageRepo;
        private DirectoryServiceAPIOptions apiOptions;

        /// <summary>
        /// Initializes a new instance of the DirectoryService class.
        /// </summary>
        /// <param name="client">The http client</param>
        public DirectoryService(HttpClient client, IDirectoryStorageRepository directoryStorageRepo,IOptions<DirectoryServiceAPIOptions> directoryServiceAPIOptions,IOptions<PagerOptions> pagerOptions)
        {
            Client = client;
            DirectoryStorageRepo = directoryStorageRepo;
            API_KEY = directoryServiceAPIOptions.Value.ClientID;
            API_SECRET = directoryServiceAPIOptions.Value.ClientSecret;
            APIGEE_TOKEN_URL = directoryServiceAPIOptions.Value.TokenURL;
            APIGEE_API_PATH = directoryServiceAPIOptions.Value.OnlineDirectoryAPIURL;
            pagerSQLConnection_MN = pagerOptions.Value.PagerSQLConnection_MN;
            pagerSQLConnection_FL = pagerOptions.Value.PagerSQLConnection_FL;
            pagerSQLConnection_AZ = pagerOptions.Value.PagerSQLConnection_AZ;
        }

        /// <summary>
        /// Gets an Apigee access token, using a cached version if available and not expired
        /// </summary>
        /// <returns>The access token</returns>
        private async Task<string> GetApigeeTokenAsync()
        {
            // Check if we have a valid cached token
            lock (_tokenLock)
            {
                if (!string.IsNullOrEmpty(_cachedToken) && DateTime.UtcNow < _tokenExpirationTime)
                {
                    Trace.WriteLine($"[TEST LOG] Using cached Apigee token (expires at {_tokenExpirationTime})");
                    return _cachedToken;
                }
            }

            // No valid cached token, fetch a new one
            Trace.WriteLine($"[TEST LOG] Fetching new Apigee token");
            
            // Create a new HttpClient for token request to avoid any configuration issues
            using (var tokenClient = new HttpClient())
            {
                var payload = $"grant_type=client_credentials&client_id={API_KEY}&client_secret={API_SECRET}";
                var content = new StringContent(payload, Encoding.UTF8, "application/x-www-form-urlencoded");
                
                var response = await tokenClient.PostAsync(APIGEE_TOKEN_URL, content);
                
                if (!response.IsSuccessStatusCode)
                {
                    throw new Exception($"Failed to retrieve token. Status code: {response.StatusCode}, Message: {await response.Content.ReadAsStringAsync()}");
                }
                
                var jsonResponse = await response.Content.ReadAsStringAsync();
                string newToken;
                
                using (JsonDocument doc = JsonDocument.Parse(jsonResponse))
                {
                    newToken = doc.RootElement.GetProperty("access_token").GetString();
                }
                
                // Cache the new token
                lock (_tokenLock)
                {
                    _cachedToken = newToken;
                    _tokenExpirationTime = DateTime.UtcNow.Add(_tokenCacheDuration);
                    Trace.WriteLine($"[TEST LOG] New Apigee token cached until {_tokenExpirationTime}");
                }
                
                return newToken;
            }
        }

        /// <summary>
        /// Convert JSON person results to XML format that exactly matches the original GoogleUsers endpoint structure
        /// </summary>
        /// <param name="jsonData">JSON data from Apigee API</param>
        /// <returns>XML document compatible with existing parsing logic</returns>
        private XDocument ConvertJsonToXml(string jsonData)
        {
            var json = JsonDocument.Parse(jsonData);
            var root = new XElement("ROOT");
            
            // Check if personResults exists and has items
            if (!json.RootElement.TryGetProperty("personResults", out var personResults) || 
                personResults.ValueKind != JsonValueKind.Array)
            {
                // Return empty results if no person data found
                return new XDocument(root);
            }
            
            foreach (var person in personResults.EnumerateArray())
            {
                var moduleResult = new XElement("MODULE_RESULT");
                
                // Add Title element directly as it's accessed with e.Element("Title").Value
                string fullName = GetStringProperty(person, "fullName");
                moduleResult.Add(new XElement("Title", fullName));
                
                // Add fields that are accessed via Value(e, "attributeName")
                AddField(moduleResult, "perid", GetStringProperty(person, "perid"));

                // TODO: check if it should be the `entity` which are always null in JSON
                AddField(moduleResult, "Entity", GetStringProperty(person, "areaDescription"));
                
                // Map location fields - combining building, floor, and campus when available
                string building = GetStringProperty(person, "building");
                string floor = GetStringProperty(person, "floor");
                string campus = GetStringProperty(person, "campus");
                
                // Build the location string starting with building and floor
                string location = string.IsNullOrWhiteSpace(building) && string.IsNullOrWhiteSpace(floor) 
                    ? string.Empty 
                    : $"{building} {floor}".Trim();
                
                // Add campus to the location if it's available
                if (!string.IsNullOrWhiteSpace(campus))
                {
                    // If we already have some location info, add a separator before the campus
                    if (!string.IsNullOrWhiteSpace(location))
                    {
                        location += $", {campus}";
                    }
                    else
                    {
                        location = campus;
                    }
                }
                
                AddField(moduleResult, "PrimaryLocation", location);
                
                // Add NameOnly field for Initials generation
                AddField(moduleResult, "NameOnly", $"{GetStringProperty(person, "lastName")}, {GetStringProperty(person, "firstName")}");
                
                root.Add(moduleResult);
            }
            
            return new XDocument(root);
        }

        /// <summary>
        /// Helper to safely get a string property from JSON
        /// </summary>
        private string GetStringProperty(JsonElement element, string propertyName)
        {
            if (element.TryGetProperty(propertyName, out var property))
            {
                if (property.ValueKind == JsonValueKind.String)
                    return property.GetString() ?? string.Empty;
                else if (property.ValueKind == JsonValueKind.Null)
                    return string.Empty;
            }
            return string.Empty;
        }
        
        /// <summary>
        /// Helper to add a Field element with name attribute
        /// </summary>
        private void AddField(XElement parent, string name, string value)
        {
            var field = new XElement("Field", value ?? string.Empty);
            field.Add(new XAttribute("name", name));
            parent.Add(field);
        }

        /// <summary>
        /// Search person or phone number using the Apigee endpoint
        /// </summary>
        /// <param name="searchTerm">The search term</param>
        /// <returns>The <see cref="Task{List{Employee.Model.SearchResult}}"/></returns>
        public async Task<List<Mayo.Mobile.Employee.Model.SearchResult>> SearchPersonAsync(string searchTerm)
        {
            Trace.WriteLine($"[TEST LOG] SearchPersonAsync called with search term: {searchTerm}");
            
            try
            {
                // Get Apigee token
                var token = await GetApigeeTokenAsync();
                Trace.WriteLine($"[TEST LOG] Successfully retrieved Apigee token");
                
                // Prepare the request to the Apigee endpoint
                var request = new HttpRequestMessage(HttpMethod.Get, $"{APIGEE_API_PATH}{APIGEE_SEARCH_SUFFIX}{HttpUtility.UrlEncode(searchTerm)}");
                request.Headers.Authorization = new AuthenticationHeaderValue("Bearer", token);
                request.Headers.Accept.Add(new MediaTypeWithQualityHeaderValue("application/json"));
                
                Trace.WriteLine($"[TEST LOG] Sending request to: {APIGEE_API_PATH}{APIGEE_SEARCH_SUFFIX}{HttpUtility.UrlEncode(searchTerm)}");
                var response = await Client.SendAsync(request);
                Trace.WriteLine($"[TEST LOG] Received response with status code: {response.StatusCode}");
                
                // Log the full response content in a prettified format
                if (response.IsSuccessStatusCode)
                {
                    var responseContent = await response.Content.ReadAsStringAsync();
                    try
                    {
                        // Try to parse and prettify JSON
                        using (JsonDocument doc = JsonDocument.Parse(responseContent))
                        {
                            var options = new JsonSerializerOptions { WriteIndented = true };
                            var formattedJson = JsonSerializer.Serialize(doc, options);
                            Trace.WriteLine($"[TEST LOG] New API's Response content (prettified):\n{formattedJson}");
                        }
                    }
                    catch
                    {
                        // If not valid JSON, just log the raw content
                        Trace.WriteLine($"[TEST LOG] New API'sResponse content (raw):\n{responseContent}");
                    }
                }
                
                // Handle empty results or error responses
                if (!response.IsSuccessStatusCode)
                {
                    if (response.StatusCode == System.Net.HttpStatusCode.NotFound || 
                        response.StatusCode == System.Net.HttpStatusCode.NoContent)
                    {
                        // Return empty list for no results
                        return new List<SearchResult>();
                    }
                    
                    // Throw exception for other error responses
                    response.EnsureSuccessStatusCode();
                }
                
                var jsonBody = await response.Content.ReadAsStringAsync();
                
                // If we get an empty response or invalid JSON, return empty list
                if (string.IsNullOrWhiteSpace(jsonBody))
                {
                    return new List<SearchResult>();
                }
                
                try
                {
                    // Convert JSON to XML format for compatibility with existing code
                    var xmlDocument = ConvertJsonToXml(jsonBody);
                    
                    // Continue with the existing XML parsing logic
                    var results = (from e in xmlDocument.Root.Elements("MODULE_RESULT")
                                select new SearchResult
                                {
                                    Category = new Category
                                    {
                                        Id = "staff",
                                        Name = "Staff",
                                        Type = "staff"
                                    },
                                    Definition = Value(e, "Entity"),
                                    Description = Value(e, "PrimaryLocation"),
                                    Id = Value(e, "perid"),
                                    Name = e.Element("Title")?.Value ?? string.Empty,
                                    Abbreviation = Initials((Value(e, "NameOnly").Trim())),
                                    Score = null,
                                    Type = "BIO",
                                }).ToList();
                    
                    // Log the returned results in a prettified format
                    try
                    {
                        var options = new JsonSerializerOptions { WriteIndented = true };
                        var resultsJson = JsonSerializer.Serialize(results, options);
                        Trace.WriteLine($"[TEST LOG] Updated function SearchPersonAsync returning (prettified):\n{resultsJson}");
                    }
                    catch (Exception ex)
                    {
                        Trace.WriteLine($"[TEST LOG] Error serializing search results: {ex.Message}");
                    }
                    
                    return results;
                }
                catch (JsonException jsonEx)
                {
                    // Log JSON parsing error
                    Trace.WriteLine($"Error parsing JSON response: {jsonEx.Message}");
                    throw;
                }
            }
            catch (Exception ex)
            {
                // Log the exception
                Trace.WriteLine($"Error in SearchPersonAsync: {ex.Message}");
                throw;
            }

            string Value(XElement e, string attribute)
            {
                var field = e.Elements("Field").SingleOrDefault(x => x.Attribute("name")?.Value == attribute);
                return field?.Value ?? string.Empty;
            }

            string Initials(string s)
            {
                if (string.IsNullOrWhiteSpace(s))
                    return string.Empty;
                    
                try {
                    return string.Join(string.Empty, s.Split(',').Select(s1 => 
                        string.IsNullOrEmpty(s1.Trim()) ? string.Empty : s1.Trim().Substring(0, 1)));
                }
                catch {
                    // If there's any error (like empty parts after split), return empty string
                    return string.Empty;
                }
            }
        }

        /// <summary>
        /// Get the persons information
        /// </summary>
        /// <param name="personId">The person identifier</param>
        /// <returns>The <see cref="Task{TResult}"/></returns>
        public async Task<Mayo.Mobile.Employee.Model.Employee> GetPersonAsync(string personId)
        {
            Trace.WriteLine($"[TEST LOG] GetPersonAsync called with personId: {personId}");
            
            try
            {
                // Get Apigee token
                var token = await GetApigeeTokenAsync();
                Trace.WriteLine($"[TEST LOG] Successfully retrieved Apigee token");
                
                // Prepare the request to the Apigee endpoint
                var request = new HttpRequestMessage(HttpMethod.Get, $"{APIGEE_API_PATH}{APIGEE_PERSON_DETAILS_SUFFIX}{personId}");
                request.Headers.Authorization = new AuthenticationHeaderValue("Bearer", token);
                request.Headers.Accept.Add(new MediaTypeWithQualityHeaderValue("application/json"));
                
                Trace.WriteLine($"[TEST LOG] Sending request to: {APIGEE_API_PATH}{APIGEE_PERSON_DETAILS_SUFFIX}{personId}");
                var response = await Client.SendAsync(request);
                Trace.WriteLine($"[TEST LOG] Received response with status code: {response.StatusCode}");
                
                // Log the full response content in a prettified format
                if (response.IsSuccessStatusCode)
                {
                    var responseContent = await response.Content.ReadAsStringAsync();
                    try
                    {
                        // Try to parse and prettify JSON
                        using (JsonDocument doc = JsonDocument.Parse(responseContent))
                        {
                            var options = new JsonSerializerOptions { WriteIndented = true };
                            var formattedJson = JsonSerializer.Serialize(doc, options);
                            Trace.WriteLine($"[TEST LOG] New API's Response content (prettified):\n{formattedJson}");
                        }
                    }
                    catch
                    {
                        // If not valid JSON, just log the raw content
                        Trace.WriteLine($"[TEST LOG] New API's Response content (raw):\n{responseContent}");
                    }
                }
                
                // Handle empty results or error responses
                if (!response.IsSuccessStatusCode)
                {
                    if (response.StatusCode == System.Net.HttpStatusCode.NotFound || 
                        response.StatusCode == System.Net.HttpStatusCode.NoContent)
                    {
                        return null;
                    }
                    
                    // Throw exception for other error responses
                    response.EnsureSuccessStatusCode();
                }
                
                var jsonBody = await response.Content.ReadAsStringAsync();
                Trace.WriteLine($"[TEST LOG] Received JSON response body length: {jsonBody?.Length ?? 0}");
                
                // If we get an empty response or invalid JSON, return null
                if (string.IsNullOrWhiteSpace(jsonBody))
                {
                    return null;
                }
                
                // Parse the JSON response
                using (JsonDocument doc = JsonDocument.Parse(jsonBody))
                {
                    // Check if required properties exist
                    if (!doc.RootElement.TryGetProperty("person", out var personData))
                    {
                        Trace.WriteLine("[TEST LOG] JSON response missing 'person' property");
                        return null;
                    }
                    
                    if (!doc.RootElement.TryGetProperty("workLocations", out var workLocations))
                    {
                        Trace.WriteLine("[TEST LOG] JSON response missing 'workLocations' property");
                        workLocations = new JsonElement();
                    }

                    if (!doc.RootElement.TryGetProperty("pagers", out var pagers))
                    {
                        Trace.WriteLine("[TEST LOG] JSON response missing 'pagers' property");
                        pagers = new JsonElement();
                    }
                    
                    Trace.WriteLine($"[TEST LOG] Successfully parsed JSON document");

                    var pagersList = new List<Pager>();
                    if (pagers.ValueKind == JsonValueKind.Array)
                    {
                        foreach (var pager in pagers.EnumerateArray())
                        {
                            pagersList.Add(new Pager
                            {
                                Entity = GetStringProperty(pager, "pagerSiteCode"),
                                Length = GetStringProperty(pager, "maxDataLength"),
                                Number = GetStringProperty(pager, "subscriberNumber"),
                                Type = GetStringProperty(pager, "displayType"),
                                Url = GetStringProperty(pager, "pagerUrl")
                            });

                        }
                    }
                    
                    
                    var fullName = GetStringProperty(personData, "fullName");
                    var nameWithoutDegree = RemoveDegreeFromName(fullName);
                    
                    var email = GetStringProperty(personData, "email");
                    var initials = string.IsNullOrEmpty(email) ? this.initialsRegex(nameWithoutDegree) : InitialsEmailRegex(email);
                    
                    // Get the status for this person - handle null repository
                    var status = this.DirectoryStorageRepo != null ? 
                        await this.DirectoryStorageRepo.StatusAsync(personId) : 
                        null;
                    
                    // Create departments from workLocations
                    var departments = new List<Department>();
                    if (workLocations.ValueKind == JsonValueKind.Array)
                    {
                        foreach (var location in workLocations.EnumerateArray())
                        {
                            var department = new Department
                            {
                                Name = GetStringProperty(location, "workArea"),
                                Location = new Location
                                {
                                    Campus = GetStringProperty(location, "campus"),
                                    Name = GetStringProperty(location, "primaryLocation")
                                },
                                Supports = new List<Support>()
                            };
                            
                            // Add supports if available
                            if (location.TryGetProperty("support", out var supportsElement) && 
                                supportsElement.ValueKind == JsonValueKind.Array)
                            {
                                foreach (var support in supportsElement.EnumerateArray())
                                {
                                    department.Supports.Add(new Support
                                    {
                                        Name = GetStringProperty(support, "supportDescription"),
                                        PhoneNumbers = new Phone(
                                            GetStringProperty(support, "supportPhone"), 
                                            GetStringProperty(support, "supportPhoneExternal")
                                        ).PhoneNumbers
                                    });
                                }
                            }
                            
                            departments.Add(department);
                        }
                    }
                    
                    // Extract building information if available
                    string address = "";
                    string buildingName = "";
                    string city = "";
                    string country = "";
                    string buildingShortName = "";
                    string state = "";
                    string zipCode = "";
                    
                    if (doc.RootElement.TryGetProperty("building", out var buildingElement))
                    {
                        address = $"{GetStringProperty(buildingElement, "address1")}{Environment.NewLine}{GetStringProperty(buildingElement, "address2")}";
                        buildingName = GetStringProperty(buildingElement, "name");
                        city = GetStringProperty(buildingElement, "city");
                        country = GetStringProperty(buildingElement, "country");
                        buildingShortName = GetStringProperty(buildingElement, "shortName");
                        state = GetStringProperty(buildingElement, "state");
                        zipCode = GetStringProperty(buildingElement, "zip");
                    }

                    // Get supervisor information
                    var supervisorName = GetStringProperty(personData, "supervisorName");
                    var supervisorNameWithoutDegree = RemoveDegreeFromName(supervisorName);
                    var supervisorInitials = this.initialsRegex(supervisorNameWithoutDegree);
                    
                    // Build the full employee record
                    var employee = new Employee
                    {
                        DisplayName = fullName,
                        Initials = initials,
                        Certifications = string.IsNullOrEmpty(GetStringProperty(personData, "apptSpecialty")) 
                            ? new List<Certification>() 
                            : new List<Certification> { new Certification(GetStringProperty(personData, "apptSpecialty")) },
                        Education = ExtractDegreeFromName(fullName),
                        EmailAddress = email,
                        Id = personId,
                        // set in controller
                        ImageUri = "",
                        Location = new Location
                        {
                            Address = address,
                            BuildingName = buildingName,
                            Campus = departments.Count > 0 ? departments[0].Location.Campus : "",
                            City = city,
                            Country = country,
                            Latitude = "",
                            Longitude = "",
                            Name = buildingShortName,
                            State = state,
                            ZipCode = zipCode,
                            Mail = GetStringProperty(personData, "mailLocation"),
                        },
                        Title = GetStringProperty(personData, "title"),
                        Pagers = pagersList,
                        Departments = departments,
                        Supervisor = new Supervisor
                        {
                            Id = GetStringProperty(personData, "supervisorPerid"),
                            Relationship = "Supervisor", // Default since not provided in JSON
                            Name = supervisorName,
                            Initials = supervisorInitials
                        },
                        PhoneNumber = departments.Count > 0 ? GetStringProperty(workLocations[0], "phone") : "",
                        SiteName = departments.Count > 0 ? departments[0].Location.Campus : "",
                        PhoneNumbers = departments.Count > 0 
                            ? new Phone(GetStringProperty(workLocations[0], "phone"), GetStringProperty(workLocations[0], "externalPhone")).PhoneNumbers 
                            : new Phone("").PhoneNumbers,
                        SortingName = this.sortingName(fullName),
                        SortingInitials = initials.Length >= 2 ? $"{initials.Substring(1, 1)}{initials.Substring(0, 1)}" : initials,
                        Status = status
                    };
                    
                    // Log the returned employee object in a prettified format
                    try
                    {
                        var options = new JsonSerializerOptions { WriteIndented = true };
                        var employeeJson = JsonSerializer.Serialize(employee, options);
                        Trace.WriteLine($"[TEST LOG] Updated function GetPersonAsync returning (prettified):\n{employeeJson}");
                    }
                    catch (Exception ex)
                    {
                        Trace.WriteLine($"[TEST LOG] Error serializing employee object: {ex.Message}");
                    }
                    
                    return employee;
                }
            }
            catch (Exception ex)
            {
                // Log the exception
                Trace.WriteLine($"Error in GetPersonAsync: {ex.Message}");
                throw;
            }

            // Helper function to extract initials from email
            string InitialsEmailRegex(string s)
            {
                MailAddress address = new MailAddress(s);
                var names = address.User.Split('.');
                return names.Count() > 1
                    // FirstnameLastname format
                    ? $"{names.Last().Substring(0, 1)}{names.First().Substring(0, 1)}"
                    // FirstinitialLastname format
                    : $"{new string(names.First().Take(2).ToArray())}";
            }
            
            // Helper function to extract degree information from the full name
            List<Education> ExtractDegreeFromName(string fullName)
            {
                // Trim any trailing spaces
                fullName = fullName.TrimEnd();
                
                // Check for a degree at the end of the full name (after the last space)
                int lastSpace = fullName.LastIndexOf(' ');
                if (lastSpace >= 0 && lastSpace < fullName.Length - 1)
                {
                    string potentialDegree = fullName.Substring(lastSpace + 1).Trim();
                    if (IsDegree(potentialDegree))
                    {
                        return new List<Education> { new Education(potentialDegree) };
                    }
                }
                
                // No degree found
                return new List<Education>();
            }

            // Helper function to remove degree information from the full name
            string RemoveDegreeFromName(string fullName)
            {
                if (string.IsNullOrWhiteSpace(fullName))
                    return string.Empty;
                    
                // Trim any trailing spaces
                fullName = fullName.TrimEnd();
                
                // Check for a degree at the end of the name
                int lastSpace = fullName.LastIndexOf(' ');
                if (lastSpace >= 0 && lastSpace < fullName.Length - 1)
                {
                    string potentialDegree = fullName.Substring(lastSpace + 1).Trim();
                    if (IsDegree(potentialDegree))
                    {
                        return fullName.Substring(0, lastSpace).Trim();
                    }
                }
                
                // No degree found, return original name
                return fullName;
            }

            // Helper function to determine if a string is likely a degree
            bool IsDegree(string text)
            {
                if (string.IsNullOrWhiteSpace(text))
                    return false;
                
                if (COMMON_DEGREES.Contains(text))
                    return true;
                    
                // Check for general degree patterns with periods
                if (text.Contains('.'))
                {
                    // First check if it's a simple "X.Y." format (like M.D., M.S.)
                    if (Regex.IsMatch(text, @"^[A-Z]\.[A-Z]\.$"))
                        return true;
                        
                    // Check for longer formats with periods (like M.B.A., Ph.D.)
                    if (Regex.IsMatch(text, @"^([A-Za-z]+\.)+$"))
                        return true;
                }
                
                // Additional pattern matching for abbreviated credentials
                // Look for short uppercase sequences
                if (text.Length <= 5 && text.All(c => char.IsUpper(c) || c == '.'))
                    return true;
                
                return false;
            }
        }

        /// <summary>
        /// Sends page to pager number
        /// </summary>
        /// <param name="pagerNumber">The pager number</param>
        /// <param name="entity">The entity</param>
        /// <param name="message">The message</param>
        /// <param name="senderPersonId">The sender person identifier</param>
        /// <returns>The <see cref="Task"/></returns>
        public async Task PageAsync(string pagerNumber, string entity, string message, string senderPersonId)
        {
            // type seems optional. All tests with "" work
            // senderPersonId seems optional. Testing with "" works just won't let user know who it's from.

            var response = await this.Client.GetAsync($"/pmts/GoogleDirectoryWS/iPhoneservice.asmx/executePage?pagerNumber={pagerNumber}&type={ "" }&length={message.Length}&entity={entity}&message={message}&iPhoneID={senderPersonId}");
            response.EnsureSuccessStatusCode();

            var body = await response.Content.ReadAsStringAsync();
            var e = XDocument.Parse(body).Root.Element("Send_Page_Return_Message").Value;

            if (e.Equals("OK", StringComparison.OrdinalIgnoreCase) == false)
            {
                throw new Exception($"Failed to send page to pager {pagerNumber} at {entity} from {senderPersonId}");
            }
        }

        public Task PageAsyncUsingSP(string pagerNumber, string entity, string message, string senderPersonId)
        {
            Trace.WriteLine($"[{DateTime.Now}] PageAsyncUsingSP - Execution - Start");

            // Determine which connection string to use based on entity location
            string pagerSQLConnection = entity switch
            {
                var e when e.Equals("MCR")  => pagerSQLConnection_MN,
                var e when e.Equals("MCF") => pagerSQLConnection_FL,
                var e when e.Equals("MCA") => pagerSQLConnection_AZ,
                _ => throw new ArgumentException($"Invalid entity location: {entity}. Expected 'MCR', 'MCF', or 'MCA'")
            };    

            Console.WriteLine($"[{DateTime.Now}] PageAsyncUsingSP - connectionString=" + pagerSQLConnection);
            Trace.WriteLine($"[{DateTime.Now}] PageAsyncUsingSP - connectionString=" + pagerSQLConnection);
            Trace.WriteLine($"[{DateTime.Now}] PageAsyncUsingSP - pagerNumber=" + pagerNumber);

            //Retrieve the pager digits based on location
            var pager = ExtractPagerNumber(entity, pagerNumber);

            // Create SQLServer connection
            using (var connection = new SqlConnection(pagerSQLConnection))
            {
                try
                {
                    // Open the connection
                    connection.Open();
                    Trace.WriteLine($"[{DateTime.Now}] PageAsyncUsingSP - Connection to SQL Server - Opened");

                    // Prepare command to call the stored procedure
                    using (SqlCommand command = new SqlCommand("dbo.SendPage", connection))
                    {
                        command.CommandType = CommandType.StoredProcedure;

                        // Define parameters for the stored procedure
                        command.Parameters.AddWithValue("@sDialedNumber", pager);
                        command.Parameters.AddWithValue("@sMessage", message);
                        command.Parameters.AddWithValue("@sUserID", senderPersonId);
                        command.Parameters.AddWithValue("@sComment", "Employee Platform Mobile App");

                        // Define a parameter to get the return value
                        var returnParameter = command.Parameters.Add("@ReturnVal", SqlDbType.Int);
                        returnParameter.Direction = ParameterDirection.ReturnValue;

                        // Execute the stored procedure
                        command.ExecuteNonQuery();

                        // Get the return value
                        var result = returnParameter.Value;

                        // You can now use 'result' for whatever you need
                        Console.WriteLine("Return Value: " + result);
                        Trace.WriteLine($"[{DateTime.Now}] PageAsyncUsingSP - SQL Procedure Return value = " + result);


                        // Handle result, assuming a result of "OK" indicates success
                        if (result == null || !(result.ToString().Equals("0") || result.ToString().Equals("1")))
                        {
                            throw new Exception($"Failed to send page to pager {pagerNumber} at {entity} from {senderPersonId}");
                        }
                    }
                }
                catch (Exception ex)
                {
                    throw new Exception($"An error occurred while executing the stored procedure. - {ex.Message}", ex);
                }
            }
             Trace.WriteLine($"[{DateTime.Now}] PageAsyncUsingSP - Execution - Complete");
            return Task.CompletedTask;
        }

        /// <summary>
        /// Gets person photo
        /// </summary>
        /// <param name="personId">The user id</param>
        /// <returns>The <see cref="Task{TResult}"/></returns>
        public async Task<Stream> GetPhotoAsync(string personId)
        {
            var response = await this.Client.GetAsync($"http://quarterly.mayo.edu/qtphotos/{personId}.jpg");
            return await response.Content.ReadAsStreamAsync();
        }

        /// <summary>
        /// Gets the favorites list
        /// </summary>
        /// <param name="userId">The user identifier</param>
        /// <param name="count">The number of items</param>
        /// <returns>The <see cref="Task{TResult}"/></returns>
        public async Task<List<Person>> GetFavoritesAsync(string userId, int? count)
        {
            return await this.DirectoryStorageRepo.FavoritesAsync(userId);
        }

        /// <summary>
        /// Add to the favorites list
        /// </summary>
        /// <param name="userId">The user identifier</param>
        /// <param name="p">The <see cref="Person"/></param>
        /// <returns>The <see cref="Task"/></returns>
        public async Task AddFavoriteAsync(string userId, Person p)
        {
            var items = await this.DirectoryStorageRepo.FavoritesAsync(userId);

            var item = items.Where(x => x.Id.Equals(p.Id)).FirstOrDefault();
            if (item != null)
            {
                items.Remove(item);
            }

            items.Insert(0, p);

            await this.DirectoryStorageRepo.SaveFavoritesAsync(userId, items);
        }

        /// <summary>
        /// Delete from the favorites list
        /// </summary>
        /// <param name="userId">The user identifier</param>
        /// <param name="p">The <see cref="Person"/></param>
        /// <returns>The <see cref="Task"/></returns>
        public async Task DeleteFavoriteAsync(string userId, Person p)
        {
            var items = await this.DirectoryStorageRepo.FavoritesAsync(userId);

            var item = items.Where(x => x.Id.Equals(p.Id)).FirstOrDefault();
            if (item != null)
            {
                items.Remove(item);

                await this.DirectoryStorageRepo.SaveFavoritesAsync(userId, items);
            }
        }

        /// <summary>
        /// Get the status
        /// </summary>
        /// <param name="userId">The user identifier</param>
        /// <returns>The <see cref="Task{TResult}"/></returns>
        public async Task<Status> StatusAsync(string userId)
        {
            return await this.DirectoryStorageRepo.StatusAsync(userId);
        }

        private string ExtractPagerNumber(string location, string rawPagerNumber)
        {
            if (string.IsNullOrWhiteSpace(rawPagerNumber))
                return string.Empty;

            // Remove all non-digit characters
            string cleanNumber = Regex.Replace(rawPagerNumber, @"[^\d]", "");

            // Determine number of digits based on location
            int digitsToTake = location.ToLower() switch
            {
                var l when l.Contains("florida") => 4,
                var l when l.Contains("arizona") => 4,
                var l when l.Contains("rochester") || l.Contains("minnesota") => 5,
                _ => 4  // Default to 4 digits
            };

            // Extract the last N digits
            if (cleanNumber.Length >= digitsToTake)
            {
                string result = cleanNumber.Substring(cleanNumber.Length - digitsToTake);
                Trace.WriteLine($"[{DateTime.Now}] ExtractPagerNumber - Extracted {digitsToTake} digits: {result}");
                return result;
            }

            Trace.WriteLine($"[{DateTime.Now}] No valid pager number found in {rawPagerNumber}");
            return string.Empty;
        }
    }
}
