﻿// -----------------------------------------------------------------------
// <copyright file="Certification.cs" company="Mayo Clinic">
//     Copyright © Mayo Foundation. All rights reserved.
// </copyright>
// -----------------------------------------------------------------------

namespace Mayo.Mobile.Employee.Model
{
    using System.Runtime.Serialization;

    /// <summary>
    /// Initializes a new instance of the Certification class.
    /// </summary>
    [DataContract(Namespace = "http://www.mayo.edu/MayoMobile")]
    public class Certification
    {
        /// <summary>
        /// Initializes a new instance of the Certification class.
        /// </summary>
        public Certification()
        {
        }

        /// <summary>
        /// Initializes a new instance of the Certification class.
        /// </summary>
        /// <param name="type">The type</param>
        public Certification(string type)
        {
            this.Type = type;
            this.Institution = string.Empty;
        }

        /// <summary>
        /// Gets or sets the type
        /// </summary>
        [DataMember]
        public string Type { get; set; }

        /// <summary>
        /// Gets or sets the institution
        /// </summary>
        [DataMember]
        public string Institution { get; set; }
    }
}