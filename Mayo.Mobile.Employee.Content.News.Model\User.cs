﻿// -----------------------------------------------------------------------
// <copyright file="User.cs" company="Mayo Clinic">
//     Copyright © Mayo Foundation. All rights reserved.
// </copyright>
// -----------------------------------------------------------------------

namespace Mayo.Mobile.Employee.Content.News.Model
{
    using System;
    using System.Collections.Generic;
    using System.Text;
    using Newtonsoft.Json;

    /// <summary>
    /// Initializes a new instance of the User class
    /// </summary>
    public class User
    {
        /// <summary>
        /// Gets or sets the email
        /// </summary>
        [JsonProperty("user_email")]
        public string Email { get; set; }

        /// <summary>
        /// Gets or sets the display name
        /// </summary>
        [JsonProperty("display_name")]
        public string DisplayName { get; set; }

        /// <summary>
        /// Gets or sets the user name
        /// </summary>
        [JsonProperty("user_name")]
        public string UserName { get; set; }
    }
}
