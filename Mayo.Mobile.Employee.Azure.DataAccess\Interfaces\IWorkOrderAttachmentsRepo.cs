﻿using System.IO;
using System.Threading.Tasks;

namespace Mayo.Mobile.Employee.Azure.DataAccess.Interfaces
{
    public interface IWorkOrderAttachmentsRepo
    {
        /// <summary>
        /// Retrieves the blob in this container with the past in blobName
        /// </summary>
        /// <typeparam name="Stream"></typeparam>
        /// <param name="blobName">name of blob you want to retrieve</param>
        /// <returns></returns>
        public Task<Stream> GetImageFileAsync(string blobName);

        /// <summary>
        /// Inserts a new blob in this container or overwrites blob if it already exists
        /// </summary>
        /// <param name="photo"></param>
        /// <param name="photoId"></param>
        /// <returns></returns>
        public Task InsertUpdateFileAsync(Stream photo, string photoId);

        /// <summary>
        /// Deletes Blob if it Exists
        /// </summary>
        /// <param name="photoId">Name of blob in this container to be deleted if it Exists</param>
        /// <returns></returns>
        public Task DeleteFileAsync(string photoId);
    }
}
