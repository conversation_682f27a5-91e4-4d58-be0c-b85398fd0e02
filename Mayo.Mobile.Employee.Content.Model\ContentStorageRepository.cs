﻿//-----------------------------------------------------------------------
// <copyright file="ContentStorageRepository.cs" company="Mayo Foundation">
//     Copyright © Mayo Foundation. All rights reserved.
// </copyright>
//-----------------------------------------------------------------------

namespace Mayo.Mobile.Employee.Content.Model
{
    using System;
    using System.Collections.Generic;
    using System.IO;
    using System.Linq;
    using System.Runtime.Serialization.Json;
    using System.Threading.Tasks;
    using Mayo.Mobile.Application.Azure.DataAccess.Interfaces;
    using Mayo.Mobile.Employee.Model;
    using Microsoft.Azure.Cosmos.Table;

    /// <summary>
    /// Initializes a new instance of the ContentStorageRepository class.
    /// </summary>
    public class ContentStorageRepository : IContentStorageRepository
    {
        private ITableStorageRepository TableStorageRepo;
        private IBlobStorageRepository BlobStorageRepo;
        /// <summary>
        /// Initializes a new instance of the ContentStorageRepository class.
        /// </summary>
        /// <param name="connectionString">The connection string</param>
        public ContentStorageRepository(ITableStorageRepository tableStorageRepo, IBlobStorageRepository blobStorageRepo)
        {
            TableStorageRepo = tableStorageRepo;
            BlobStorageRepo = blobStorageRepo;
        }

        public async Task<List<string>> GetBlobsAsync(string container)
        {
            return await BlobStorageRepo.GetBlobsAsync(container);
        }

        public async Task<T> GetBlobAsync<T>(string container, string name)
        {
            return await BlobStorageRepo.GetBlobAsync<T>(container, name);
        }

        public async Task<string> GetBlobContentsAsync(string containerName, string blobName)
        {
            return await BlobStorageRepo.GetBlobContentsAsync(containerName, blobName);
        }

        /// <summary>
        /// The list of content
        /// </summary>
        /// <param name="id">The category identifier</param>
        /// <returns>The list of <see cref="PackageItem"/></returns>
        public async Task<List<PackageItem>> ItemsAsync(string id, int count = 100)
        {
            List<PackageItem> list = new List<PackageItem>();

            //// TODO: optimize 
            var entities = await TableStorageRepo.GetEntitiesAsync("ContentStorage", id) ?? new List<DynamicTableEntity>();
            entities.ForEach(x =>
            {
                list.Add(new PackageItem
                {
                    Category = TableStorageRepo.Deserialize<Category>(GetStringValue(x.Properties, "Category")),
                    Description = GetStringValue(x.Properties, "Description"),
                    Id = x.RowKey,
                    Images = TableStorageRepo.Deserialize<List<Element>>(GetStringValue(x.Properties, "Images")),
                    Name = GetStringValue(x.Properties, "Name"),
                    Type = GetStringValue(x.Properties, "Type").ToUpper(),
                    IsScheduled = false,
                    Author = TableStorageRepo.Deserialize<Person>(GetStringValue(x.Properties, "Author")),
                    ReadTime = GetStringValue(x.Properties, "ReadTime"),
                    Date = GetStringValue(x.Properties, "Date"),
                    SortDate = GetStringValue(x.Properties, "SortDate"),
                });
            });

            return list
                .Where(x => x.Category.Name.Equals("Photo of the Week", StringComparison.OrdinalIgnoreCase) == false)
                //// sortable date string
                .OrderByDescending(x => x.SortDate)
                .ThenByDescending(x => x.Id).ToList();
        }

        /// <summary>
        /// The content
        /// </summary>
        /// <param name="id">The category identifier</param>
        /// <returns>The list of <see cref="PackageItem"/></returns>
        public async Task<PackageItem> ItemAsync(string partitionKey, string rowKey)
        {
            var x = await TableStorageRepo.GetEntityAsync("ContentStorage", partitionKey, rowKey) ?? new DynamicTableEntity();
            return new PackageItem
                {
                    Category = TableStorageRepo.Deserialize<Category>(GetStringValue(x.Properties, "Category")),
                    Description = GetStringValue(x.Properties, "Description"),
                    Id = x.RowKey,
                    Images = TableStorageRepo.Deserialize<List<Element>>(GetStringValue(x.Properties, "Images")),
                    Name = GetStringValue(x.Properties, "Name"),
                    Type = GetStringValue(x.Properties, "Type").ToUpper(),
                    IsScheduled = false,
                    Author = TableStorageRepo.Deserialize<Person>(GetStringValue(x.Properties, "Author")),
                    ReadTime = GetStringValue(x.Properties, "ReadTime"),
                    Date = GetStringValue(x.Properties, "Date"),
                    SortDate = GetStringValue(x.Properties, "SortDate")
                };
        }

        /// <summary>
        /// Get the items for the Campuses package
        /// </summary>
        /// <returns>The <see cref="Task{TResult}"/></returns>
        public async Task<List<PackageItem>> CampusesAsync()
        {
             return await BlobStorageRepo.GetBlobAsync<List<PackageItem>>("content", "campuses.json");
        }

        /// <summary>
        /// Get the items for the Sections package
        /// </summary>
        /// <returns>The <see cref="Task{TResult}"/></returns>
        public async Task<List<PackageItem>> SectionsAsync()
        {
            return await BlobStorageRepo.GetBlobAsync<List<PackageItem>>("content", "sections.json");
        }

        /// <summary>
        /// Get the favorited items for a person
        /// </summary>
        /// <param name="userId">The user identifier</param>
        /// <returns>The <see cref="Task{TResult}"/></returns>
        public async Task<List<PackageItem>> FavoritesAsync(string userId)
        {
            return await BlobStorageRepo.GetBlobAsync<List<PackageItem>>("user", $"{userId}/favorites/content.json") ?? new List<PackageItem>();
        }

        /// <summary>
        /// Save the favorites for a person
        /// </summary>
        /// <param name="userId">The user identifier</param>
        /// <param name="list">The list of <see cref="PackageItem"/></param>
        /// <returns>The <see cref="Task{TResult}"/></returns>
        public async Task SaveFavoritesAsync(string userId, List<PackageItem> list)
        {
            await SaveAsync<List<PackageItem>>("user", $"{userId}/favorites/content.json", list);
        }

        /// <summary>
        /// Get the items for the Sections package
        /// </summary>
        /// <returns>The <see cref="Task{TResult}"/></returns>
        public async Task<Stream> ImageAsync(string name)
        {
            name = name.ToLower();
            return await BlobStorageRepo.GetBlobAsync("images", name);
        }

        /// <summary>
        /// Delete all entities in a partition assuming a manageable count
        /// </summary>
        /// <param name="id">The partition key</param>
        /// <returns>The <see cref="Task"/></returns>
        public async Task DeleteEntitiesAsync(string id)
        {
            var entities = await TableStorageRepo.GetEntitiesAsync("ContentStorage", id);

            if (entities.Count > 0)
            {
                await TableStorageRepo.DeleteAsync("ContentStorage", entities);
            }
        }

        /// <summary>
        /// Save the blob block to the container 
        /// </summary>
        /// <typeparam name="T">The type of the object</typeparam>
        /// <param name="containerName">The blob container</param>
        /// <param name="name">The name of the blob block</param>
        /// <param name="content">The contents of the blob</param>
        public async Task SaveAsync<T>(string containerName, string name, T content)
        {
            MemoryStream stream = null;
            try
            {
                stream = this.Serialize<T>(content);
                await BlobStorageRepo.SaveAsync(containerName, name, stream);
                stream = null;
            }
            finally
            {
                if (stream != null)
                {
                    stream.Dispose();
                }
            }
        }

        /// <summary>
        /// Save the blob block to the container 
        /// </summary>
        /// <param name="containerName">The blob container</param>
        /// <param name="name">The name of the blob block</param>
        /// <param name="s">The contents of the blob</param>
        public async Task SaveStreamAsync(string containerName, string name, Stream stream)
        {
            await BlobStorageRepo.SaveAsync(containerName, name, stream);
        }

        /// <summary>
        /// Save the blob block to the container 
        /// </summary>
        /// <param name="containerName">The blob container</param>
        /// <param name="name">The name of the blob block</param>
        /// <param name="s">The contents of the blob</param>
        public async Task SaveAsync(string containerName, string name, string s)
        {
            await BlobStorageRepo.SaveAsync(containerName, name, s);
        }

        /// <summary>
        /// Serialize the object to JSON
        /// </summary>
        /// <typeparam name="T">The type of the object</typeparam>
        /// <param name="model">The object</param>
        /// <returns>The JSON string</returns>
        protected MemoryStream Serialize<T>(T model)
        {
            MemoryStream stream = new MemoryStream();
            DataContractJsonSerializer ser = new DataContractJsonSerializer(typeof(T));
            ser.WriteObject(stream, model);
            stream.Position = 0;
            return stream;
        }

        /// <summary>
        /// Gets the string value from the dictionary given a key
        /// </summary>
        /// <param name="dictionary">The <see cref="IDictionary{TKey, TValue}"/></param>
        /// <param name="key">The key</param>
        /// <returns>The value</returns>
        private string GetStringValue(IDictionary<string, EntityProperty> dictionary, string key)
        {
            return dictionary.TryGetValue(key, out EntityProperty value) ? value.StringValue : string.Empty;
        }
    }
}