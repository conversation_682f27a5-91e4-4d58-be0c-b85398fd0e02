using Mayo.Mobile.Application.Azure.DataAccess.Interfaces;
using Mayo.Mobile.Application.Azure.DataAccess.Models;
using Mayo.Mobile.Application.Azure.DataAccess.Repositories;
using Mayo.Mobile.Application.Model;
using Mayo.Mobile.Application.Models;
using Mayo.Mobile.Employee.Application.Model;
using Mayo.Mobile.Employee.Web.Api.Controllers;
using Mayo.Mobile.Logging.Interfaces;
using Mayo.Mobile.Logging.Model;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Options;
using Microsoft.VisualStudio.TestTools.UnitTesting;
using System.Web.Http;

namespace Mayo.Mobile.Employee.Api.Int.Tests
{ 
    [TestClass]
    public class ApplicationTests
    {
        private IMayoMobileLogger logger = null;

        private IApplicationService service = null;

        private readonly string accessToken = "Bearer eyJhbGciOiJodHRwOi8vd3d3LnczLm9yZy8yMDAxLzA0L3htbGRzaWctbW9yZSNobWFjLXNoYTI1NiIsInR5cCI6IkpXVCJ9.****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.s7HnCxd67qx5RaL9O9Jl42GcnNNoHYi89vbPYary7_c";
        private readonly string userId = "**********";

        [TestInitialize]
        public void Init()
        {
            var config = new ConfigurationBuilder().AddJsonFile("appsettings.json").Build();
            var saRepoOptions = Options.Create<StorageAccountRepoOptions>(new StorageAccountRepoOptions() { ConnectionString = config.GetConnectionString("StorageAccount") });
            ITableStorageRepository tableStorageRepo = new TableStorageRepository(saRepoOptions);
            IBlobStorageRepository blobStorageRepo = new BlobStorageRepository(saRepoOptions);
            IQueueStorageRepository queueStorageRepo = new QueueStorageRepository(saRepoOptions);
            IApplicationRepository applicationRepository = new ApplicationRepository(tableStorageRepo, blobStorageRepo);
            service = new ApplicationService(applicationRepository);
            logger = new MayoMobileLogger(tableStorageRepo, queueStorageRepo);
        }

        [TestMethod]
        public void Device_Update_Test()
        {
            var controller = new ApplicationController(service, logger)
            {
                ControllerContext = new ControllerContext()
            };
            controller.ControllerContext.HttpContext = new DefaultHttpContext();
            controller.ControllerContext.HttpContext.Request.Path = @"/api/application/Device/Update";

            var device = new Device
            {
                Application = new Mobile.Application.Models.Application
                {
                    Id = "Employee",
                    Name = "Mayo Clinic Mobile Employee Application",
                    Version = "1.0"
                },
                Manufacturer = "Apple",
                Model = "",
                OperatingSystem = "IOS",
                Version = "12.0",
                Id = "00f879f5-4863-467b-aa85-079b2a468e7f"
            };

            var result = controller.UpdateDevice(device).GetAwaiter().GetResult() as OkObjectResult;
            Assert.IsNotNull(result);
            Assert.AreEqual(200, result.StatusCode);
        }

        [TestMethod]
        public void Feedback_Test()
        {
            var controller = new ApplicationController(service, logger) { ControllerContext = new ControllerContext().SetContext(accessToken, this.userId) };
            controller.ControllerContext.HttpContext.Request.Path = @"/api/application/Feedback/Send";

            var f = new FeedbackRequest
            {
                ApplicationId = "Employee",
                Email = "<EMAIL>",
                DeviceId = "00f879f5-4863-467b-aa85-079b2a468e7f",
                Type = "FEEDBACK",
                Message = "This is a feedback message"
            };

            var result = controller.Feedback(accessToken, f).GetAwaiter().GetResult() as OkResult;
            Assert.IsNotNull(result);
            Assert.AreEqual(200, result.StatusCode);
        }
    }
}
