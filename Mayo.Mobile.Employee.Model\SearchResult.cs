﻿//-----------------------------------------------------------------------
// <copyright file="SearchRepository.cs" company="Mayo Foundation">
//     Copyright © Mayo Foundation. All rights reserved.
// </copyright>
//-----------------------------------------------------------------------

namespace Mayo.Mobile.Employee.Model
{
    using System;
    using System.Collections.Generic;
    using System.Runtime.Serialization;

    /// <summary>
    /// Initializes a new instance of the SearchResult class.
    /// </summary>
    [DataContract(Namespace = "http://www.mayo.edu/MayoMobile")]
    public class SearchResult : Item
    {
        /// <summary>
        /// Gets or sets the definition
        /// </summary>
        [DataMember(Name = "Definition")]
        public string Definition { get; set; }

        /// Gets or sets an image for the content
        /// </summary>
        [DataMember(Name = "ImageUri")]
        public Uri ImageUri { get; set; }

        /// <summary>
        /// Gets or sets the search highlights
        /// </summary>
        [DataMember(Name = "HighLights")]
        public Dictionary<string, IList<string>> HighLights { get; set; }

        /// <summary>
        /// Gets or sets the search score
        /// </summary>
        [DataMember(Name = "Score")]
        public string Score { get; set; }
    }
}
