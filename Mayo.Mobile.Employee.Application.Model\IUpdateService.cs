﻿//-----------------------------------------------------------------------
// <copyright file="IUpdateService.cs" company="Mayo Foundation">
//     Copyright © Mayo Foundation. All rights reserved.
// </copyright>
//-----------------------------------------------------------------------

namespace Mayo.Mobile.Employee.Application.Model
{

    /// <summary>
    /// Initializes a new instance of the IUpdateService class.
    /// </summary>
    public interface IUpdateService
    {
        /// <summary>
        /// Gets the app store url for apple devices
        /// </summary>
        string AppStoreUrl { get; }

        /// <summary>
        /// Gets the google play store url for android devices
        /// </summary>
        string PlayStoreUrl { get; }

        /// <summary>
        /// Gets the default url
        /// </summary>
        string DefaultUrl { get; }

        /// <summary>
        /// Set the app store url in the view
        /// </summary>
        /// <param name="os">The os the device runs</param>
        /// <param name="appStores">The list of app stores</param>
        string GetAppStoreUrl(string os, string userAgent);
    }
}
