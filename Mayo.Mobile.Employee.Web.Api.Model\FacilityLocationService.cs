﻿using Mayo.Mobile.Employee.Location.Model;
using System.Collections.Generic;
using System.Threading.Tasks;
using System.IO;
using Mayo.Mobile.Employee.Azure.DataAccess.Interfaces;
using Mayo.Mobile.Employee.Facilities.DataAccess.Interfaces;
using Mayo.Mobile.Employee.Azure.DataAccess.Models;
using System;
using Mayo.Mobile.Employee.Facilities.DataAccess.Models;

namespace Mayo.Mobile.Employee.Web.Api.Model
{    /// <summary>
    /// Initializes a new instance of the LocationService class.
    /// </summary>
    public class FacilityLocationService: ILocationService
    {

        private IFacilitiesLocationRepo locationsRepo;
        private IWorkOrderQueueRepo workOrderQueueRepo;
        private IWorkOrderAttachmentsRepo workOrderAttachmentsRepo;
        private IWorkOrderLoggingRepo workOrderLoggingRepo;
        private IServiceNowRepo serviceNowRepo;

        public FacilityLocationService(
            IFacilitiesLocationRepo locationsRepository,
            IWorkOrderQueueRepo workOrderQueueRepository, 
            IWorkOrderAttachmentsRepo workOrderAttachmentsRepository,
            IWorkOrderLoggingRepo workOrderLoggingRepository,
            IServiceNowRepo serviceNowRepository)
        {
            locationsRepo = locationsRepository;
            workOrderQueueRepo = workOrderQueueRepository;
            workOrderAttachmentsRepo = workOrderAttachmentsRepository;
            workOrderLoggingRepo = workOrderLoggingRepository;
            serviceNowRepo = serviceNowRepository;
        }

        /// <summary>
        /// Get the list of campuses
        /// </summary>
        /// <returns>The <see cref="Task{TResult}"/></returns>
        public async Task<List<Campus>> CampusListAsync()
        {
            var campusList = await locationsRepo.GetCampusListAsync();
            campusList.Sort(new CampusComparer());
            return campusList;
        }

        /// <summary>
        /// Get the list of buildings
        /// </summary>
        /// <param name="campusId">The campus identifier</param>
        /// <returns>The <see cref="Task{TResult}"/></returns>
        public async Task<List<Building>> BuildingListAsync(string campusId)
        {
            var buildingList = await locationsRepo.GetBuildingListAsync(campusId);
            buildingList.Sort(new BuildingComparer());
            return buildingList;
        }

        /// <summary>
        /// Get the list of floors
        /// </summary>
        /// <param name="campusId">The campus identifier</param>
        /// <param name="buildingId">The building identifier</param>
        /// <returns>The <see cref="Task{TResult}"/></returns>
        public async Task<List<Floor>> FloorListAsync(string campusId, string buildingId)
        {
            var floorList = await locationsRepo.GetFloorListAsync(campusId, buildingId);
            floorList.Sort(new FloorComparer());
            return floorList;
        }

        /// <summary>
        /// Get the list of roomss
        /// </summary>
        /// <param name="campusId">The campus identifier</param>
        /// <param name="buildingId">The building identifier</param>
        /// <param name="floorId">The floor identifier</param>
        /// <returns>The <see cref="Task{TResult}"/></returns>
        public async Task<List<Room>> RoomListAsync(string campusId, string buildingId, string floorId)
        {
            var roomList = await locationsRepo.GetRoomListAsync(campusId, buildingId, floorId);
            roomList.Sort(new RoomComparer());
            return roomList;
        }

        /// <summary>
        /// Save the reported issue
        /// </summary>
        /// <param name="facilityIssue">all the info of the facilityIssue<see cref="FacilityIssue"/></param>
        /// <param name="photos">The list of image identifiers</param>
        /// <returns>string representing the workorderId</returns>
        public async Task<string> SaveReportAsync(FacilityIssue facilityIssue, List<(string Id, Stream Data)> photos)
        {
            photos = photos ?? new List<(string Id, Stream Data)>();

            FacilityWorkOrderDocument workOrder = null;
            //Check if we already have tried to process this
            var message = new FacilityWorkOrderQueueMessage()
            {
                Id = facilityIssue.Id,
                ReportedBy = facilityIssue.UserId,
                CampusId = facilityIssue.CampusId,
                BuildingId = facilityIssue.BuildingId,
                FloorId = facilityIssue.FloorId,
                RoomId = facilityIssue.RoomId,
                Comments = facilityIssue.Comments,
                Latitude = facilityIssue.ReportedLocation.Latitude,
                Longitude = facilityIssue.ReportedLocation.Longitude,
                PhotoIds = facilityIssue.PhotoIds
            };
            workOrder = await workOrderLoggingRepo.GetCreateWorkOrderDocumentAsync(message);

            var locationLink = string.Empty;
            if (!string.IsNullOrWhiteSpace(message.Latitude) && !string.IsNullOrWhiteSpace(message.Latitude))
            {
                locationLink = $"https://www.google.com/maps/search/?api=1&query={message.Latitude},{message.Longitude}";
                locationLink = $"{Environment.NewLine}{Environment.NewLine}Google Maps Link: {locationLink}";
            }

            try
            {
                if (string.IsNullOrWhiteSpace(workOrder.WorkOrderId))
                {
                    var requestBody = new FacilitiesCreateWORequestBody()
                    {
                        CampusId = workOrder.CampusId,
                        BuildingId = workOrder.BuildingId,
                        FloorId = workOrder.FloorId,
                        RoomId = workOrder.RoomId,
                        Description = $"{workOrder.Comment}{locationLink}",
                        ReportedBy = workOrder.ReportedBy,
                        LocationUrl = locationLink,
                        Latitude = message.Latitude,
                        Longitude = message.Longitude
                    };

                    var workOrderResponse = await serviceNowRepo.CreateFacilityWorkOrderAsync(requestBody);

                    workOrder.FacilitiesSysId = workOrderResponse.SysId;
                    workOrder.WorkOrderId = workOrderResponse.WoNumber;

                    // should not fail this call since we successfully retrieved/created doc in previous code
                    // if this happens then we need to put in its own try catch
                    workOrder = await workOrderLoggingRepo.UpdateWorkOrderDocumentAsync(workOrder);
                }
            }
            catch (Exception e)
            {
                /// still want to save images to blob storage to process later in webjob/functionApp since we are queueing serviceNow creation
                SaveImagesToBlobAsync(photos);
                // Add exception to the Cosmos Document for Logging
                workOrder.LoggingLevel = "Error";
                workOrder.ExceptionMessage = $"{e.Message}{Environment.NewLine}{e.StackTrace}";// Append the Exception to the WorkOrder Doc in the Mobile repo
                workOrder = await workOrderLoggingRepo.UpdateWorkOrderDocumentAsync(workOrder);

                // didn't create sn ticket, insert in queue for webjob/function app to process it and try again
                await workOrderQueueRepo.EnqueueWOAsync(facilityIssue);
                throw e;
            }

            /// saving images to blob storage to process later in webjob/functionApp
            SaveImagesToBlobAsync(photos);

            // We loop here to put the images on the queue 
            workOrder.PhotoIds.ForEach(async id => {
                var attachment = new FacilityWorkOrderAttachment()
                {
                    PartitionKey = workOrder.PartitionKey,
                    MobileDocumentId = workOrder.DocumentId,
                    PhotoId = id,
                    SystemId = workOrder.FacilitiesSysId,
                    WorkOrderId = workOrder.WorkOrderId
                };
                await workOrderQueueRepo.EnqueueWOAttachmentAsync(attachment);
            });

            return workOrder.WorkOrderId;
        }

        private void SaveImagesToBlobAsync(List<(string Id, Stream Data)> photos)
        {
            photos.ForEach(async photo =>
            {
                await workOrderAttachmentsRepo.InsertUpdateFileAsync(photo.Data, photo.Id);

                if (photo.Data != null)
                {
                    photo.Data.Dispose();
                }
            });
        }
    }
}