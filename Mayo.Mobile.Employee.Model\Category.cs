﻿//-----------------------------------------------------------------------
// <copyright file="Category.cs" company="Mayo Foundation">
//     Copyright © Mayo Foundation. All rights reserved.
// </copyright>
//-----------------------------------------------------------------------

namespace Mayo.Mobile.Employee.Model
{
    using System;
    using System.Collections.Generic;
    using System.Linq;
    using System.Runtime.Serialization;
    using System.Text;

    /// <summary>
    /// Initializes a new instance of the Category class.
    /// </summary>
    [DataContract(Namespace = "http://www.mayo.edu/MayoMobile")]
    public class Category
    {
        /// <summary>
        /// Initializes a new instance of the Category class.
        /// </summary>
        public Category()
        {
        }

        /// <summary>
        /// Initializes a new instance of the Category class.
        /// </summary>
        /// <param name="id">The identifier</param>
        /// <param name="name">The name</param>
        public Category(string id, string name)
        {
            this.Id = id;
            this.Type = id;
            this.Name = name;
        }

        /// <summary>
        /// Initializes a new instance of the Category class.
        /// </summary>
        /// <param name="id">The identifier</param>
        /// <param name="type">The type</param>
        /// <param name="name">The name</param>
        public Category(string id, string type, string name)
        {
            this.Id = id;
            this.Type = type;
            this.Name = name;
        }

        /// <summary>
        /// Gets or sets the name of the category
        /// </summary>
        [DataMember(Name = "Id")]
        public string Id { get; set; }

        /// <summary>
        /// Gets or sets the category type
        /// </summary>
        [DataMember(Name = "Type")]
        public string Type { get; set; }

        /// <summary>
        /// Gets or sets the category name
        /// </summary>
        [DataMember(Name = "Name")]
        public string Name { get; set; }

        /// <summary>
        /// Gets or sets the color in hex
        /// </summary>
        [IgnoreDataMember]
        public string HexColor { get; set; }

        /// <summary>
        /// Gets or sets the marketing message
        /// </summary>
        [IgnoreDataMember]
        public string MarketingTitle { get; set; }

        /// <summary>
        /// Gets or sets the marketing description
        /// </summary>
        [IgnoreDataMember]
        public string MarketingDescription { get; set; }
    }
}