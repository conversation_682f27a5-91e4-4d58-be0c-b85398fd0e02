﻿//-----------------------------------------------------------------------
// <copyright file="ContentController.cs" company="Mayo Foundation">
//     Copyright © Mayo Foundation. All rights reserved.
// </copyright>
//-----------------------------------------------------------------------

namespace Mayo.Mobile.Employee.Web.Api.Controllers
{
    using System;
    using System.Collections.Generic;
    using System.Linq;
    using System.Security.Principal;
    using System.Threading.Tasks;
    using Mayo.Mobile.Employee.Content.Model;
    using Mayo.Mobile.Employee.Search.Model;
    using Mayo.Mobile.Employee.Web.Api.Model;
    using Mayo.Mobile.Logging.Model;
    using Microsoft.AspNetCore.Authorization;
    using Microsoft.AspNetCore.Mvc;
    using Newtonsoft.Json.Linq;

    /// <summary>
    /// Initializes a new instance of the ContentController class
    /// </summary>
    [Authorize]
    [ApiVersion("1.0")]
    [ApiVersion("2.0")]
    [ApiController]
    [Route("api/[controller]")]
    [Route("api/v{version:apiVersion}/[controller]")]
    public class ContentController : ControllerBase
    {

        private IContentService ContentService;

        private MockContentService MockService;

        public ContentController(IContentService contentService, MockContentService mockService)
        {
            ContentService = contentService;
            MockService = mockService;
        }

        /// <summary>
        /// Gets the list of packages
        /// </summary>
        /// <param name="authorization">The Authorization header</param>
        /// <param name="id">The category identifier</param>
        /// <returns>The <see cref="Task{TResult}"/></returns>
        [HttpGet, Route("list/{id?}"), MapToApiVersion("1.0")]
        [ProducesResponseType (typeof (List<Package>), 200)]
        [ProducesResponseType (404)]
        public async Task<IActionResult> GetContentListV1Async([FromHeader] string authorization, string id, [FromQuery] int? count)
        {
            var userId = User.Identity.GetUserId();

            var package = userId.Equals("00000001")
                ? await MockService.GetListAsync(id, count)
                : await ContentService.GetListAsync(id, count);

            return Ok (package);
        }

        /// <summary>
        /// Gets the list of packages
        /// </summary>
        /// <param name="authorization">The Authorization header</param>
        /// <param name="id">The category identifier</param>
        /// <returns>The <see cref="Task{TResult}"/></returns>
        [HttpGet, Route("list/{id?}")]
        [ProducesResponseType(typeof(List<Package>), 200)]
        [ProducesResponseType(404)]
        public async Task<IActionResult> GetContentListAsync([FromHeader] string authorization, string id, [FromQuery] int? count)
        {
            var userId = User.Identity.GetUserId();

            var packages = userId.Equals("00000001")
                ? await MockService.GetListAsync(id, count)
                : await ContentService.GetListAsync(id, count);

            if (string.IsNullOrEmpty(id))
            {
                var alerts = userId.Equals("00000001")
                    ? await MockService.GetAlertsAsync()
                    : await ContentService.GetAlertsAsync();
                packages.Insert(0, alerts);
            }

            return Ok(packages);
        }

        /// <summary>
        /// Gets the list of favorites
        /// </summary>
        /// <param name="authorization">The Authorization header</param>
        /// <returns>The <see cref="Task{TResult}"/></returns>
        [HttpGet, Route("favorites/list")]
        [ProducesResponseType(typeof(List<Package>), 200)]
        [ProducesResponseType(404)]
        public async Task<IActionResult> FavoritesListAsync([FromHeader] string authorization)
        {
            var userId = User.Identity.GetUserId();

            var favorites = await ContentService.GetFavoritesAsync(userId, null);

            return Ok(favorites);
        }

        /// <summary>
        /// Gets the content
        /// </summary>
        /// <param name="authorization">The Authorization header</param>
        /// <param name="id">The content identifier</param>
        /// <returns>The <see cref="Task{TResult}"/></returns>
        [HttpGet, Route("detail/{id}")]
        [ProducesResponseType (typeof (Content), 200)]
        [ProducesResponseType (404)]
        public async Task<IActionResult> GetContentAsync([FromHeader] string authorization, string id)
        {
            var userId = User.Identity.GetUserId();
            var content = await ContentService.GetContentAsync(id);

            return Ok(content);
        }

        /// <summary>
        /// Gets the content
        /// </summary>
        /// <param name="authorization">The Authorization header</param>
        /// <param name="formatted">A value indicating wheter the html content needs formatted</param>
        /// <param name="id">The content identifier</param>
        /// <returns>The <see cref="Task{TResult}"/></returns>
        [HttpGet, Route("detail/html/{id}")]
        [ProducesResponseType(404)]
        public async Task<ContentResult> GetContentHtmlAsync([FromHeader] string authorization, string id, bool? formatted = true)
        {
            var userId = User.Identity.GetUserId();
            var content = await ContentService.GetContentAsync(id, formatted ?? true);

            return new ContentResult
            {
                ContentType = "text/html",
                StatusCode = (int)System.Net.HttpStatusCode.OK,
                Content = content.Page.Elements.Where(x => x.Type.Equals("HTML")).Select(x => x.Value).First()
            };
        }

        /// <summary>
        /// Add Favorited Content
        /// </summary>
        /// <param name="authorization">The Authorization header</param>
        /// <returns>The <see cref="Task{TResult}"/></returns>
        [HttpPost, Route("favorite/add")]
        [ProducesResponseType(200)]
        [ProducesResponseType(400)]
        public async Task<IActionResult> AddFavoriteAsync([FromHeader] string authorization, [FromBody] Model.FavoriteRequest r)
        {
            var userId = User.Identity.GetUserId();

            await ContentService.AddFavoriteAsync(userId, r.CategoryId, r.Id);

            return Ok();
        }

        /// <summary>
        /// Delete Favorited Content
        /// </summary>
        /// <param name="authorization">The Authorization header</param>
        /// <returns>The <see cref="Task{TResult}"/></returns>
        [HttpPost, Route("favorite/delete")]
        [ProducesResponseType(200)]
        [ProducesResponseType(404)]
        public async Task<IActionResult> DeleteFavoriteAsync([FromHeader] string authorization, [FromBody] Model.FavoriteRequest r)
        {
            var userId = User.Identity.GetUserId();

            await ContentService.DeleteFavoriteAsync(userId, r.CategoryId, r.Id);

            return Ok();
        }

        /// <summary>
        /// Get the image for Mayo Clinic Daily content
        /// </summary>
        /// <param name="imageId">The image identifier</param>
        /// <returns>The image</returns>
        [AllowAnonymous]
        [HttpGet, Route("image/{imageId}")]
        [ProducesResponseType(200)]
        public IActionResult ImageAsync(string imageId)
        {
            var image = ContentService.GetImageAsync(imageId).GetAwaiter().GetResult();
            return File(image, "image/jpeg");
        }

        /// <summary>
        /// Returns comments to article
        /// </summary>
        /// <param name="id">The article identifier</param>
        /// <param name="pageNumber">The page number to get
        [HttpGet, Route("comments/{id}")]
        public async Task<IActionResult> GetCommentsAsync(string id, string pageNumber = null)
        {
            var userId = User.Identity.GetUserId();

            var comments = await ContentService.CommentsAsync(id, pageNumber);

            comments.ForEach(x =>
            {
                x.Commenter.ImageUrl = $"{Request.Scheme}://{Request.Host.Value}/api/directory/photo/{x.Commenter.Id}";

                x.Comments.ForEach(y =>
                {
                    y.Commenter.ImageUrl = $"{Request.Scheme}://{Request.Host.Value}/api/directory/photo/{y.Commenter.Id}";
                });
            });

            return Ok(new PagingResponse<Comment>(comments));
        }

        /// <summary>
        /// Used for Posting Comments to news articles
        /// </summary>
        /// <param name="comment">The <see cref="Comment"/></param>
        /// <returns>The <see cref="Task{TResult}"/></returns>
        [HttpPost, Route("comments/add")]
        public async Task<IActionResult> AddCommentAsync([FromBody] Comment comment)
        {
            var userId = User.Identity.GetUserId();
            var email = User.Identity.GetEmail();

            await ContentService.PostCommentAsync(comment.ContentId, comment.ThreadId, email, comment.Text, comment.Id);

            return Ok();
        }

        /// <summary>
        /// Search the content
        /// </summary>
        /// <param name="searchService">The <see cref="ISearchService"/></param>
        /// <param name="r">The <see cref="System.Text.Json.JsonElement"/></param>
        [HttpPost, Route("search")]
        public async Task<IActionResult> SearchAsync([FromServices] ISearchService searchService, [FromBody] JObject r)
        {
            var userId = User.Identity.GetUserId();

            var query = (string)r["Query"];

            var results = await searchService.SearchAsync("newscentercontent", query);

            return Ok(new PagingResponse<Employee.Model.SearchResult>(results));
        }
    }
}