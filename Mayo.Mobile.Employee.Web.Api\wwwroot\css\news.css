﻿blockquote {
    border: none;
    border-top: 1px solid #E6E6E6;
    border-bottom: 1px solid #E6E6E6;
    text-transform: none;
    line-height: 33px;
    padding: 29px 20px 15px 55px;
    margin: 30px 0px 30px 0px;
    font-weight: normal;
    font-size: 30px;
    font-family: HelveticaLTStd-Roman;
    color: #575656;
    background-image: url('https://ccs-iter-ema.mayo.edu/content/blockquote.png');
    background-repeat: no-repeat;
    background-size: 25px 23px;
    background-position: 23px 23px;
}
blockquote p {
    text-transform: none;
    line-height: 33px !important;
    font-weight: normal;
    font-size: 30px !important;
    color: #575656;
    font-family: HelveticaLTStd-Roman;
}
blockquote p:first-of-type {
    margin-bottom: 0px;
    margin-top: 20px;
}
blockquote p:last-of-type {
    position: relative;
    font-size: 14px !important;
    line-height: 15.4px;
    text-transform: none;
    font-style: normal;
    display: block;
    margin-top: 10px;
    margin-bottom: 10px;
}
blockquote cite {
    position: relative;
    font-size: 14px !important;
    line-height: 15.4px;
    text-transform: none;
    font-style: normal;
    display: block;
    margin-top: 10px;
    margin-bottom: 10px;
}


.wp-block-pullquote blockquote {
    border: none;
    border-top: 1px solid #E6E6E6;
    border-bottom: 1px solid #E6E6E6;
    text-transform: none;
    line-height: 33px;
    padding: 29px 20px 15px 55px;
    margin: 30px 0px 30px 0px;
    font-weight: normal;
    font-size: 30px;
    font-family: HelveticaLTStd-Roman;
    color: #575656;
    background-image: url('https://ccs-iter-ema.mayo.edu/content/blockquote.png');
    background-repeat: no-repeat;
    background-size: 25px 23px;
    background-position: 23px 23px;
}

.wp-block-pullquote blockquote p {
    text-transform: none;
    line-height: 33px !important;
    font-weight: normal;
    font-size: 30px !important;
    color: #575656;
    font-family: HelveticaLTStd-Roman;
}

.wp-block-pullquote blockquote p:first-of-type {
    margin-bottom: 0px;
    margin-top: 20px;
}

.wp-block-pullquote blockquote cite {
    position: relative;
    font-size: 14px !important;
    line-height: 15.4px;
    text-transform: none;
    font-style: normal;
    display: block;
    margin-top: 10px;
    margin-bottom: 10px;
}

.alignright {
    margin: 5px 0 20px 40px;
}
.alignleft {
    margin: 5px 40px 20px 0;
}
.wp-block-image {
    max-width: 100%;
    margin-bottom: 1em;
    margin-left: 0;
    margin-right: 0;
}
.wp-block-image img {
    max-width: 100%;
}
.wp-block-image figcaption {
    margin-top: .5em;
    margin-bottom: 1em;
    color: #555d66;
    text-align: center;
    font-size: 13px;
}
.wp-block-image .aligncenter, .wp-block-image .alignleft, .wp-block-image .alignright, .wp-block-image.is-resized {
    display: table;
    margin-left: 0;
    margin-right: 0;
}
.wp-block-image .alignright {
    float: right;
    margin-left: 1em;
}
.wp-block-image .alignleft {
    float: left;
    margin-right: 1em;
}

.wp-block-pullquote {
    margin: 0;
    padding: 0px;
    text-align: left;
    background: none;
}

