﻿using Mayo.Mobile.Employee.Azure.DataAccess.Interfaces;
using Mayo.Mobile.Employee.Azure.DataAccess.Models;
using Mayo.Mobile.Employee.Facilities.DataAccess.Interfaces;
using Mayo.Mobile.Employee.Facilities.DataAccess.Models;
using Mayo.Mobile.Employee.Functions.Interfaces;
using Mayo.Mobile.Employee.Functions.Models;
using Mayo.Mobile.Employee.Location.DataAccess.Interfaces;
using Mayo.Mobile.Employee.Location.Model;
using Microsoft.Extensions.Options;
using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using System.Threading.Tasks.Dataflow;

namespace Mayo.Mobile.Employee.Functions.Services
{
    public class FacilityFunctionsService : IFacilityFunctionsService
    {
        private IFacilitiesLocationRepo facilitiesLocationRepo;
        private IWorkOrderLoggingRepo workOrderLoggingRepo;
        private IWorkOrderQueueRepo workOrderQueueRepo;
        private IWorkOrderAttachmentsRepo workOrderAttachmentsRepo;
        private IGoogleMapRepo googleMapRepo;
        private IUDPLocationRepo udpLocationRepo;
        private IServiceNowRepo serviceNowRepo;

        // List for filtering the locations loaded by the loading function
        private List<string> campusTypes;
        private List<string> buildingTypes;
        private List<string> floorTypes;
        private List<string> roomTypes;

        public FacilityFunctionsService(
            IFacilitiesLocationRepo facilitiesLocationRepository,
            IWorkOrderLoggingRepo workOrderLoggingRepository, 
            IWorkOrderQueueRepo workOrderQueueRepository, 
            IWorkOrderAttachmentsRepo workOrderAttachmentsRepository, 
            IGoogleMapRepo googleMapRepository, 
            IUDPLocationRepo udpLocationRepository, 
            IServiceNowRepo serviceNowRepository,
            IOptions<FacilityFunctionsServiceOptions> configurationOptions)
        {
            facilitiesLocationRepo = facilitiesLocationRepository;
            workOrderLoggingRepo = workOrderLoggingRepository;
            workOrderQueueRepo = workOrderQueueRepository;
            workOrderAttachmentsRepo = workOrderAttachmentsRepository;
            googleMapRepo = googleMapRepository;
            udpLocationRepo = udpLocationRepository;
            serviceNowRepo = serviceNowRepository;
            campusTypes = configurationOptions.Value.CampusTypes;
            buildingTypes = configurationOptions.Value.BuildingTypes;
            floorTypes = configurationOptions.Value.FloorTypes;
            roomTypes = configurationOptions.Value.RoomTypes;
        }

        /// <summary>
        /// For Use with Testing
        /// </summary>
        /// <param name="workOrderLoggingRepository"></param>
        /// <param name="workOrderQueueRepository"></param>
        /// <param name="workOrderAttachmentsRepository"></param>
        /// <param name="googleMapRepository"></param>
        /// <param name="udpLocationRepository"></param>
        /// <param name="serviceNowRepository"></param>
        /// <param name="configurationOptions"></param>
        public FacilityFunctionsService(
            IFacilitiesLocationRepo facilitiesLocationRepository,
            IWorkOrderLoggingRepo workOrderLoggingRepository,
            IWorkOrderQueueRepo workOrderQueueRepository,
            IWorkOrderAttachmentsRepo workOrderAttachmentsRepository,
            IGoogleMapRepo googleMapRepository,
            IUDPLocationRepo udpLocationRepository,
            IServiceNowRepo serviceNowRepository,
            FacilityFunctionsServiceOptions configurationOptions)
        {
            facilitiesLocationRepo = facilitiesLocationRepository;
            workOrderLoggingRepo = workOrderLoggingRepository;
            workOrderQueueRepo = workOrderQueueRepository;
            workOrderAttachmentsRepo = workOrderAttachmentsRepository;
            googleMapRepo = googleMapRepository;
            udpLocationRepo = udpLocationRepository;
            serviceNowRepo = serviceNowRepository;
            campusTypes = configurationOptions.CampusTypes;
            buildingTypes = configurationOptions.BuildingTypes;
            floorTypes = configurationOptions.FloorTypes;
            roomTypes = configurationOptions.RoomTypes;
        }

        /// <summary>
        /// Used to Process a WorkOrder Message and create a new work order in ServiceNow
        /// </summary>
        /// <param name="issue"></param>
        /// <returns></returns>
        public async Task ProcessFacilityIssueAsync(FacilityWorkOrderQueueMessage issue)
        {
            FacilityWorkOrderDocument workOrder = null;
            //Check if we already have tried to process this

            workOrder = await workOrderLoggingRepo.GetCreateWorkOrderDocumentAsync(issue);

            var locationLink = "None supplied.";
            if (!string.IsNullOrWhiteSpace(issue.Latitude) && !string.IsNullOrWhiteSpace(issue.Latitude))
            {
                locationLink = $"https://www.google.com/maps/search/?api=1&query={issue.Latitude},{issue.Longitude}";
            }

            try
            {
                if (string.IsNullOrWhiteSpace(workOrder.WorkOrderId))
                {
                    var requestBody = new FacilitiesCreateWORequestBody()
                    {
                        CampusId = workOrder.CampusId,
                        BuildingId = workOrder.BuildingId,
                        FloorId = workOrder.FloorId,
                        RoomId = workOrder.RoomId,
                        Description = $"{workOrder.Comment}{Environment.NewLine}{Environment.NewLine}Google Maps Link: {locationLink}",
                        ReportedBy = workOrder.ReportedBy,
                        LocationUrl = locationLink,
                        Latitude = issue.Latitude,
                        Longitude = issue.Longitude
                    };

                    var workOrderResponse = await serviceNowRepo.CreateFacilityWorkOrderAsync(requestBody);

                    workOrder.FacilitiesSysId = workOrderResponse.SysId;
                    workOrder.WorkOrderId = workOrderResponse.WoNumber;

                    workOrder = await workOrderLoggingRepo.UpdateWorkOrderDocumentAsync(workOrder);
                }
            }
            catch (Exception e)
            {
                // Add exception to the Cosmos Document for Logging
                workOrder.LoggingLevel = "Error";
                workOrder.ExceptionMessage = $"{e.Message}{Environment.NewLine}{Environment.NewLine}{e.StackTrace}";// Append the Exception to the WorkOrder Doc in the Mobile repo
                workOrder = await workOrderLoggingRepo.UpdateWorkOrderDocumentAsync(workOrder);

                // Azure Queues remove messages when they finish successfully and remove messages when they throw an exception
                // we want to throw this exception so that the message stays on the queue to be tried again
                throw e;
            }

            // We loop here to put the images on the queue if it has an exception we want to try leave this message on the queue to try again
            // Only Caveat is that if the Exception happens after a few have been queue successfully, this may lead to multiples of the same
            // image added to the WorkOrder, likely hood of that happening is low 
            workOrder.PhotoIds.ForEach(async id => {
                var attachment = new FacilityWorkOrderAttachment()
                {
                    PartitionKey = workOrder.PartitionKey,
                    MobileDocumentId = workOrder.DocumentId,
                    PhotoId = id,
                    SystemId = workOrder.FacilitiesSysId,
                    WorkOrderId = workOrder.WorkOrderId
                };
                await workOrderQueueRepo.EnqueueWOAttachmentAsync(attachment);
            });
        }

        /// <summary>
        /// Use to Process a WorkOrderAttachment Queue Message and attach it to a Service now WorkOrder
        /// </summary>
        /// <param name="attachment"></param>
        /// <returns></returns>
        public async Task ProcessWorkOrderAttachmentAsync(FacilityWorkOrderAttachment attachment)
        {
            var stream = await workOrderAttachmentsRepo.GetImageFileAsync(attachment.PhotoId);
            try
            {
                await serviceNowRepo.AttachImageToFacilityWorkOrder(stream, attachment.SystemId, attachment.PhotoId);
            }catch(Exception e)
            {
                var workOrder = await workOrderLoggingRepo.GetWorkOrderDocumentAsync(attachment.MobileDocumentId, attachment.PartitionKey);
                workOrder.LoggingLevel = "Error";
                workOrder.ExceptionMessage = $"{e.Message}{Environment.NewLine}{Environment.NewLine}{e.StackTrace}";// Append the Exception to the WorkOrder Doc in the Mobile repo
                await workOrderLoggingRepo.UpdateWorkOrderDocumentAsync(workOrder);
            }
            
            try
            {
                await workOrderAttachmentsRepo.DeleteFileAsync(attachment.PhotoId);
            }
            catch
            {
                // swallow the delete error and return successful so that the message is removed from the queue
                // we will just be left with a file in the blob storage that doesn't need to be there.
            }
        }

        /// <summary>
        /// Used to Load location Data from UDP into our cosmos repo
        /// </summary>
        /// <returns></returns>
        public async Task ProcessFacilityLocationLoadingAsync()
        {
            var locations = await this.udpLocationRepo.GetCampusDataAsync();

            var dictionary = new ConcurrentDictionary<string, (string, string)>();

            var block = new ActionBlock<(string BuildingId, string BuildingName, string StreetAddress, string City, string State, string Zipcode, string County)>(
                async y =>
                {
                    var address = new Address
                    {
                        StreetAddress = y.StreetAddress,
                        City = y.City,
                        PostalCode = y.Zipcode,
                        State = y.State
                    };

                    if (dictionary.TryGetValue(address.FormattedAddress, out (string Latitude, string Longitude) coordinates) == false)
                    {
                        coordinates = await googleMapRepo.GetLatidueAndLongitudeAsync(address.FormattedAddress);
                        dictionary.TryAdd(y.BuildingId, coordinates);
                    }
                },
                new ExecutionDataflowBlockOptions { MaxDegreeOfParallelism = 3 });

            var buildings = (from x in locations
                             group x by x.BuildingCode into g
                             select (g.Key, g.First().BuildingCodeDescription, g.First().GetFormattedAddress(), g.First().CityCode, g.First().StateCode, g.First().PostalCode, g.First().County)).ToList();
            buildings.ForEach(async z =>
            {
                await block.SendAsync(z);
            });

            block.Complete();
            await block.Completion;

            var buildingsList = (from building in locations
                                where buildingTypes.Contains(building.SpaceTypeDescription.Trim())
                                select (building));
            var floorsList = (from floor in locations
                                  where floorTypes.Contains(floor.SpaceTypeDescription.Trim())
                                  select (floor));
            var roomsList = (from room in locations
                                 where roomTypes.Contains(room.SpaceTypeDescription.Trim())
                                 select (room));

            var mappedLocations = (from campus in locations
                                   where campusTypes.Contains(campus.SpaceTypeDescription.Trim())
                                   select (new Campus
                                   {
                                       Id = campus.RoomCode,
                                       Name = campus.LocationDescription,
                                       LocationType = "Campus",
                                       LocationSubType = campus.SpaceTypeDescription.Trim(),
                                       Buildings = (from building in buildingsList
                                                    where buildingTypes.Contains(building.SpaceTypeDescription.Trim())
                                                    && building.CampusCode == campus.CampusCode
                                                    select (new Building
                                                    {
                                                        Id = building.RoomCode,
                                                        Name = building.LocationDescription,
                                                        Address = new Address
                                                        {
                                                            StreetAddress = building.GetFormattedAddress(),
                                                            City = building.CityCode,
                                                            PostalCode = building.PostalCode,
                                                            State = building.StateCode
                                                        },
                                                        Latitude = GetLatitude(dictionary, building.BuildingCode),
                                                        Longitude = GetLongitude(dictionary, building.BuildingCode),
                                                        LocationType = "Building",
                                                        LocationSubType = building.SpaceTypeDescription.Trim(),
                                                        Floors = (from floor in floorsList
                                                                  where floorTypes.Contains(floor.SpaceTypeDescription.Trim())
                                                                  && floor.CampusCode == campus.CampusCode
                                                                  && floor.BuildingCode == building.BuildingCode
                                                                  select (new Floor
                                                                  {
                                                                      Id = floor.RoomCode,
                                                                      Name = floor.LocationDescription,
                                                                      LocationType = "Floor",
                                                                      LocationSubType = floor.SpaceTypeDescription.Trim(),
                                                                      Rooms = (from room in roomsList
                                                                               where roomTypes.Contains(room.SpaceTypeDescription.Trim())
                                                                               && room.CampusCode == campus.CampusCode
                                                                               && room.BuildingCode == building.BuildingCode
                                                                               && room.FloorCode == floor.FloorCode
                                                                               select (new Room
                                                                               {
                                                                                   Id = room.RoomCode,
                                                                                   Name = room.LocationDescription,
                                                                                   LocationType = "Room",
                                                                                   LocationSubType = room.SpaceTypeDescription.Trim(),
                                                                               })).ToList()
                                                                  })).ToList()
                                                    })).ToList()
                                   })).ToList();
                                   

            //Insert Lists into Repo
            // had to use non async foreach loops here do to overwhelming the cosmos DB which is set at 400 transactions/ms
            await facilitiesLocationRepo.InsertCampusListAsync(mappedLocations);
            foreach (var campus in mappedLocations)
            {
                foreach (var building in campus.Buildings)
                {
                    foreach (var floor in building.Floors)
                    {
                        await facilitiesLocationRepo.InsertRoomListAsync(campus.Id, building.Id, floor.Id, floor.Name, floor.LocationSubType, floor.Rooms);
                    }
                    await facilitiesLocationRepo.InsertFloorListAsync(campus.Id, building.Id, building.Name, building.LocationSubType, building.Floors);
                }
                await facilitiesLocationRepo.InsertBuildingListAsync(campus.Id, campus.Name, campus.Buildings);
            }

            static string GetLatitude(ConcurrentDictionary<string, (string, string)> dictionary, string id)
            {
                try
                {
                    return dictionary.TryGetValue(id, out (string, string) coordinates) ? coordinates.Item1 : string.Empty;
                }
                catch
                {
                    return string.Empty;
                }
            }

            static string GetLongitude(ConcurrentDictionary<string, (string, string)> dictionary, string id)
            {
                try
                {
                    return dictionary.TryGetValue(id, out (string, string) coordinates) ? coordinates.Item2 : string.Empty;
                }
                catch
                {
                    return string.Empty;
                }
            }
        }

    }
}
