# Introduction
This is the api gateway and web job for the Employee app.  The web job grabs content and pulls into storage.  The api calls the platform identity api.

# Getting Started
All packages should be .net core so you can use Visual Studio, Visual Studio for Mac or Visual Studio Code to make changes.

# Build and Test
Deploy to hybrid azure using the publish profiles from visual studio.

- Mayo.Mobile.Employee.Web.Api - ccs-<env>-ema
- Mayo.Mobile.Employee.Functions - ccs-<env>-ema-fn
     - NewsTimerFunctionAsync 
          - The timer triggered function to pull news content
     - NewsWrapperTimerFunctionAsync
          - The timer triggered function to pull the news content wrapper
     - ProcessNewsContentSearchAsync
          - Handle the content blob being saved
     - ProcessAuditQueueMessage
          - This function will get triggered/executed when a new message is written on an Azure Queue
     - ProcessErrorQueueMessage
          - This function will get triggered/executed when a new message is written on an Azure Queue
     - ProcessFacilityWorkOrderQueueMessage
          - Queue Trigger for processing WorkOrder Messages that need to be created in ServiceNow
     - ProcessFacilityWorkOrderAttachmentQueueMessage
          - Queue Trigger for processing Attachment Messages to be added to ServiceNow WorkOrders
     - ProcessLocationsAsync
          - This function gets the location data from UDP and inserts it into our team repository
