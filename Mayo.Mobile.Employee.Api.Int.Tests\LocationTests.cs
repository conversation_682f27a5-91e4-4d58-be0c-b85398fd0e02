using Mayo.Mobile.Employee.Location.Model;
using Microsoft.Extensions.Configuration;
using Microsoft.VisualStudio.TestTools.UnitTesting;
using Mayo.Mobile.Employee.Azure.DataAccess;
using Mayo.Mobile.Employee.Facilities.DataAccess.Interfaces;
using Mayo.Mobile.Employee.Facilities.DataAccess;
using Mayo.Mobile.Employee.Azure.DataAccess.Models;
using Mayo.Mobile.Employee.Functions.Services;
using Newtonsoft.Json;
using System.Collections.Generic;
using Mayo.Mobile.Logging.Interfaces;
using Mayo.Mobile.Employee.Location.DataAccess;
using Mayo.Mobile.Employee.Functions.Models;
using Mayo.Mobile.Employee.Location.DataAccess.Interfaces;
using Mayo.Mobile.Employee.Web.Api.Controllers;
using Microsoft.AspNetCore.Mvc;
using System.Web.Http;
using Mayo.Mobile.Application.Azure.DataAccess.Models;
using Microsoft.Extensions.Options;

namespace Mayo.Mobile.Employee.Api.Int.Tests
{
    [TestClass]
    public class LocationTests
    {

        private ILocationService service = null;

        private readonly string accessToken = "Bearer eyJhbGciOiJodHRwOi8vd3d3LnczLm9yZy8yMDAxLzA0L3htbGRzaWctbW9yZSNobWFjLXNoYTI1NiIsInR5cCI6IkpXVCJ9.****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.s7HnCxd67qx5RaL9O9Jl42GcnNNoHYi89vbPYary7_c";
        private readonly string userId = "1234567890";
        private WorkOrderLoggingCosmosRepo workOrderLoggingRepo { get; set; }
        private FacilitiesCosmosRepo facilitiesCosmosRepo { get; set; }
        private FacilitiesQueueRepo workOrderQueueRepo { get; set; }
        private FacilitiesBlobRepo workOrderAttachmentsRepo { get; set; }
        private GoogleRepository googleMapRepo { get; set; }
        private UDPLocationRepository udpLocationRepo { get; set; }
        private ServiceNowRepository serviceNowRepo { get; set; }
        private FacilityFunctionsService facilityFunctionsService { get; set; }
        private CosmosCRUDRepo cosmosCRUDRepo { get; set; }

        [TestInitialize]
        public void Init()
        {
            var config = new ConfigurationBuilder().AddJsonFile("appsettings.json").Build();
            var saRepoOptions = Options.Create<StorageAccountRepoOptions>(new StorageAccountRepoOptions() { ConnectionString = config.GetConnectionString("StorageAccount") });
            var woRepoOptions = Options.Create<FacilityQueueOptions>(new FacilityQueueOptions() { QueueName = config.GetConnectionString("FacilityQueueOptions:QueueName") });
            var blobRepoOptions = Options.Create<FacilityBlobOptions>(new FacilityBlobOptions() { BlobContainerName = config.GetConnectionString("FacilityBlobOptions:BlobContainerName") });
            var cosmosRepoOptions = Options.Create<CosmosRepoOptions>(new CosmosRepoOptions() { ConnectionString = config.GetConnectionString("CosmosAccount") });
            var facilityCosmosOptions = Options.Create<FacilityCosmosOptions>(new FacilityCosmosOptions()
            {
                CosmosDatabaseName = config["FacilityCosmosOptions:CosmosDatabaseName"],
                CosmosContainerName = config["FacilityCosmosOptions:CosmosContainerName"],
                CosmosPartitionKeyProperty = config["FacilityCosmosOptions:CosmosPartitionKeyProperty"]
            });
            var woCosmosOptions = Options.Create<WorkOrderCosmosOptions>(new WorkOrderCosmosOptions()
            {
                CosmosDatabaseName = config["WorkOrderCosmosOptions:CosmosDatabaseName"],
                CosmosContainerName = config["WorkOrderCosmosOptions:CosmosContainerName"],
                CosmosPartitionKeyProperty = config["WorkOrderCosmosOptions:CosmosPartitionKeyProperty"]
            });
            //this.logger = new Logger(config["ConnectionStrings:StorageAccount"]);
            udpLocationRepo = new UDPLocationRepository(new UDPOptions() { UPDConnectionString = config["UDPLocationOptions:UDPConnectionString"] });
            googleMapRepo = new GoogleRepository(new GoogleRepoOptions()
            {
                GoogleBaseUrl = config["GoogleRepoOptions:GoogleBaseUrl"],
                GoogleApiKey = config["GoogleRepoOptions:GoogleApiKey"]
            });
            serviceNowRepo = new ServiceNowRepository(new ServiceNowRepoOptions()
            {
                ServicenowWorkOrderAttachmentTable = config["ServiceNowOptions:ServicenowWorkOrderAttachmentTable"],
                ServiceNowWorkOrderBaseUrl = config["ServiceNowOptions:ServiceNowWorkOrderBaseUrl"],
                ServiceNowUser = config["ServiceNowOptions:ServiceNowUser"],
                ServiceNowPass = config["ServiceNowOptions:ServiceNowPass"]
            });
            workOrderQueueRepo = new FacilitiesQueueRepo(saRepoOptions, woRepoOptions);
            workOrderAttachmentsRepo = new FacilitiesBlobRepo(saRepoOptions, blobRepoOptions);
            facilitiesCosmosRepo = new FacilitiesCosmosRepo(cosmosRepoOptions, facilityCosmosOptions);
            workOrderLoggingRepo = new WorkOrderLoggingCosmosRepo(cosmosRepoOptions, woCosmosOptions);
            var campusTypes = config["FacilityFunctionServiceOptions:CampusTypes"];
            var buildingTypes = config["FacilityFunctionServiceOptions:BuildingTypes"];
            var floorTypes = config["FacilityFunctionServiceOptions:FloorTypes"];
            var roomTypes = config["FacilityFunctionServiceOptions:RoomTypes"];

            var functionOptions = new FacilityFunctionsServiceOptions() 
            {
                CampusTypes = JsonConvert.DeserializeObject<List<string>>(campusTypes),
                BuildingTypes = JsonConvert.DeserializeObject<List<string>>(buildingTypes),
                FloorTypes = JsonConvert.DeserializeObject<List<string>>(floorTypes),
                RoomTypes = JsonConvert.DeserializeObject<List<string>>(roomTypes),
            };

            facilityFunctionsService = new FacilityFunctionsService(facilitiesCosmosRepo, workOrderLoggingRepo, workOrderQueueRepo, workOrderAttachmentsRepo, googleMapRepo, udpLocationRepo, serviceNowRepo, functionOptions);

            //uncomment to run delete or create Test
            //cosmosCRUDRepo = new CosmosCRUDRepo(new CosmosCRUDRepoOptions()
            //{
            //    CosmosConnectionString = config["CosmosCRUDRepoOptions:CosmosConnectionString"],
            //    CosmosDatabaseName = config["CosmosCRUDRepoOptions:CosmosDatabaseName"],
            //    CosmosContainerName = config["CosmosCRUDRepoOptions:CosmosContainerName"],
            //    CosmosPartitionKeyProperty = config["CosmosCRUDRepoOptions:CosmosPartitionKeyProperty"]
            //});

        }

        [TestMethod]
        public void GetCampusList_Test()
        {
            var controller = new LocationController(service) { ControllerContext = new ControllerContext().SetContext(accessToken, userId) };

            var result = controller.GetAsync().GetAwaiter().GetResult() as OkObjectResult;
            Assert.IsNotNull(result);
            Assert.IsNotNull(result.Value);
        }

        [TestMethod]
        public void GetBuildingList_Test()
        {
            var controller = new LocationController(service) { ControllerContext = new ControllerContext().SetContext(accessToken, userId) };

            var result = controller.GetAsync("RO").GetAwaiter().GetResult() as OkObjectResult;
            Assert.IsNotNull(result);
            Assert.IsNotNull(result.Value);
        }

        [TestMethod]
        public void GetFloorList_Test()
        {
            var controller = new LocationController(service) { ControllerContext = new ControllerContext().SetContext(accessToken, userId) };

            var result = controller.GetAsync("PX", "SS").GetAwaiter().GetResult() as OkObjectResult;
            Assert.IsNotNull(result);
            Assert.IsNotNull(result.Value);
        }

        [TestMethod]
        public void GetRoomList_Test()
        {
            var controller = new LocationController(service) { ControllerContext = new ControllerContext().SetContext(accessToken, userId) };

            var result = controller.GetAsync("PX", "SS").GetAwaiter().GetResult() as OkObjectResult;
            Assert.IsNotNull(result);
            Assert.IsNotNull(result.Value);
        }

        [TestMethod]
        public void GetLocationsUDP_Test()
        {
            var locations = udpLocationRepo.GetCampusDataAsync().Result;
            Assert.IsNotNull(locations);
        }

        /// <summary>
        /// Used for Loading data in the Repos manually
        /// </summary>
        /// <returns></returns>
        //[TestMethod]
        //public async Task LoadFacilityLocations()
        //{
        //    await facilityFunctionsService.ProcessFacilityLocationLoadingAsync();
        //}

        /// <summary>
        /// Used to Read from the attachment queue and manually process it
        /// </summary>
        /// <returns></returns>
        //[TestMethod]
        //public async Task ProcessAttachementQueue()
        //{
        //    FacilityFunctionsService service = new FacilityFunctionsService(facilityWorkOrderAndLocationRepo, workOrderQueueRepo, workOrderAttachmentsRepo, googleMapRepo, udpLocationRepo, serviceNowRepo);
        //    var attachment = await workOrderQueueRepo.DequeueAttachmentAsync();
        //    await service.ProcessWorkOrderAttachmentAsync(attachment);
        //}

        //[TestMethod]
        //public void CreateEmployeeAppLoggingContainer()
        //{
        //    var workorder = new FacilityWorkOrderQueueMessage()
        //    {
        //        BuildingId = "SI",
        //        CampusId = "RO",
        //         Comments = "Testing",
        //          ReportedBy = "123456"


        //    };
        //     workOrderLoggingRepo.GetCreateWorkOrderDocumentAsync(workorder).Wait();
        //}

        /// <summary>
        /// Used for Deleting Containers that need to be rebuild, since we don't have access personally to do this anymore
        /// </summary>
        /// <returns></returns>
        //[TestMethod]
        //public void DeleteFacilityContainer()
        //{
        //    cosmosCRUDRepo.DeleteContainer("Facilities");
        //}
    }
}
