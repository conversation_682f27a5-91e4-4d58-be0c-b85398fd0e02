﻿// -----------------------------------------------------------------------
// <copyright file="CommentsListItem.cs" company="Mayo Clinic">
//     Copyright © Mayo Foundation. All rights reserved.
// </copyright>
// -----------------------------------------------------------------------

namespace Mayo.Mobile.Employee.Content.News.Model
{
    using System;
    using System.Collections.Generic;
    using System.Text;
    using Newtonsoft.Json;

    /// <summary>
    /// Initializes a new instance of the CommentsListItem
    /// </summary>
    public class CommentsListItem
    {
        /// <summary>
        /// Gets or sets the identifier
        /// </summary>
        [JsonProperty("id")]
        public string Id { get; set; }

        /// <summary>
        /// Gets or sets the thread identifier
        /// </summary>
        [JsonProperty("thread_id")]
        public string ThreadId { get; set; }

        /// <summary>
        /// Gets or sets the status
        /// </summary>
        [JsonProperty("status")]
        public string Status { get; set; }

        /// <summary>
        /// Gets or sets the content
        /// </summary>
        [JsonProperty("content")]
        public string Content { get; set; }

        /// <summary>
        /// Gets or sets the hidden value
        /// </summary>
        [JsonProperty("hidden")]
        public string Hidden { get; set; }

        /// <summary>
        /// Gets or sets the date the comment was created
        /// </summary>
        [JsonProperty("date_created")]
        public string DateCreated { get; set; }

        /// <summary>
        /// Gets or sets the time the comment was created
        /// </summary>
        [JsonProperty("time_created")]
        public string TimeCreated { get; set; }

        /// <summary>
        /// Gets or sets the date the comment was updated
        /// </summary>
        [JsonProperty("date_updated")]
        public string DateUpdated { get; set; }

        /// <summary>
        /// Gets or sets the order of the comments in the thread
        /// </summary>
        [JsonProperty("nested_order")]
        public string NestedOrder { get; set; }

        /// <summary>
        /// Gets or sets the <see cref="User"/>
        /// </summary>
        [JsonProperty("user")]
        public User User { get; set; }
    }
}
