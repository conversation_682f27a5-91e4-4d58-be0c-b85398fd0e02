﻿//-----------------------------------------------------------------------
// <copyright file="Employee.cs" company="Mayo Foundation">
//     Copyright © Mayo Foundation. All rights reserved.
// </copyright>
//-----------------------------------------------------------------------

namespace Mayo.Mobile.Employee.Model
{
    using System;
    using System.Collections.Generic;
    using System.Linq;
    using System.Runtime.Serialization;
    using System.Text;
    using System.Threading.Tasks;

    /// <summary>
    /// Initializes a new instance of the Person class.
    /// </summary>
    [DataContract(Namespace = "http://www.mayo.edu/MayoMobile")]
    public class Employee : Mayo.Mobile.Staff.Model.Person
    {
        /// <summary>
        /// The initials
        /// </summary>
        private string initials = string.Empty;

        /// <summary>
        /// The initials for sorting
        /// </summary>
        private string sortingInitials = string.Empty;

        /// <summary>
        /// Initializes a new instance of the Employee class.
        /// </summary>
        public Employee()
        {
        }

        /// <summary>
        /// Gets or sets the list of <see cref="Certification"/>
        /// </summary>
        [DataMember]
        public List<Certification> Certifications { get; set; }

        /// <summary>
        /// Gets or sets the list of <see cref="Department"/>
        /// </summary>
        [DataMember]
        public List<Department> Departments { get; set; }

        /// <summary>
        /// Gets or sets the list of <see cref="Education"/>
        /// </summary>
        [DataMember]
        public List<Education> Education { get; set; }

        /// <summary>
        /// Gets or sets the <see cref="Mayo.Mobile.Employee.Model.Location"/>
        /// </summary>
        [DataMember]
        public Location Location { get; set; }

        /// <summary>
        /// Gets or sets the <see cref="Supervisor"/>
        /// </summary>
        [DataMember]
        public Supervisor Supervisor { get; set; }

        /// <summary>
        /// Gets or sets the pager
        /// </summary>
        [DataMember]
        public List<Pager> Pagers { get; set; }

        /// <summary>
        /// Gets or sets the phone numbers
        /// </summary>
        [DataMember]
        public Dictionary<string, string> PhoneNumbers { get; set; }

        /// <summary>
        /// Gets or sets the initials
        /// </summary>
        [DataMember]
        public string Initials 
        {
            get
            {
                return this.initials;
            }

            set
            {
                this.initials = string.IsNullOrEmpty(value) ? string.Empty : value.ToUpper();
            }
        }

        /// <summary>
        /// Gets or sets the sorting name
        /// </summary>
        [DataMember]
        public string SortingName { get; set; }

        /// <summary>
        /// Gets or sets the sorting initals
        /// </summary>
        [DataMember]
        public string SortingInitials
        {
            get
            {
                return this.sortingInitials;
            }

            set
            {
                this.sortingInitials = string.IsNullOrEmpty(value) ? string.Empty : value.ToUpper();
            }
        }

        /// <summary>
        /// Gets or sets the <see cref="Status"/>
        /// </summary>
        [DataMember]
        public Status Status { get; set; }
    }
}
