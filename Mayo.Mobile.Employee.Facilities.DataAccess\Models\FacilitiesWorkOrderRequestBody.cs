﻿using Newtonsoft.Json;

namespace Mayo.Mobile.Employee.Facilities.DataAccess.Models
{
    /// <summary>
    /// Facilities Request Body for the Create WorkOrder call
    /// </summary>
    public class FacilitiesCreateWORequestBody
    {
        [JsonProperty("campusid")]
        public string CampusId { get; set; }

        [Json<PERSON>roperty("buildingid")]
        public string BuildingId { get; set; }

        [JsonProperty("floorid")]
        public string FloorId { get; set; }

        [JsonProperty("roomid")]
        public string RoomId { get; set; }

        /// <summary>
        /// Comment the user made regarding the Facility Issue
        /// </summary>
        [JsonProperty("description")]
        public string Description { get; set; }

        /// <summary>
        /// Person who reported, This is the Employee Id not the LanId
        /// </summary>
        [JsonProperty("reported_by")]
        public string ReportedBy { get; set; }
        [JsonIgnore]
        public string LocationUrl { get; set; }
        [JsonProperty("latitude")]
        public string Latitude { get; set; }
        [JsonProperty("longitude")]
        public string Longitude { get; set; }
    }
}
