﻿//-----------------------------------------------------------------------
// <copyright file="ISearchAdminRepository.cs" company="Mayo Foundation">
//     Copyright © Mayo Foundation. All rights reserved.
// </copyright>
//-----------------------------------------------------------------------

namespace Mayo.Mobile.Employee.Azure.Search.Model
{
    using System;
    using System.Collections.Generic;
    using System.Linq;
    using System.Text;
    using System.Threading.Tasks;

    /// <summary>
    /// Initializes a new instance of the ISearchAdminRepository class.
    /// </summary>
    public interface ISearchAdminRepository : IDisposable, ISearchRepository
    {
        /// <summary>
        /// Create the index
        /// </summary>
        /// <typeparam name="T">The type of object to build the index for</typeparam>
        /// <param name="name">The name of the index</param>
        /// <param name="deleteIndexIfExists">Optional value to indicate whether the index should be deleted and re-added</param>
        /// <param name="suggesterName">The name of the <see cref="Suggester"/></param>
        /// <param name="suggesters">The fields for the <see cref="Suggester"/></param>
        /// <param name="scoringProfiles">The <see cref="ScoringProfile"/> names</param>
        /// <param name="scoringWeights">The <see cref="TextWeights"/> of the <see cref="ScoringProfile"/>s</param>
        /// <returns>The <see cref="Task{SearchIndex}"/></returns>
        Task<SearchIndex> CreateIndexAsync<T>(
            string name,
            bool deleteIndexIfExists = false,
            string suggesterName = null,
            string[] suggesters = null,
            string[] scoringProfiles = null,
            Dictionary<string, double>[] scoringWeights = null);

        /// <summary>
        /// Upload content to the index for removing from the search
        /// </summary>
        /// <typeparam name="T">The type of content to updload</typeparam>
        /// <param name="name">The name of the index</param>
        /// <param name="item">The item to upload to the index</param>
        /// <returns>The <see cref="Task"/></returns>
        Task RemoveAsync<T>(string name, T item) where T : class;

        /// <summary>
        /// Upload content to the index for searching
        /// </summary>
        /// <typeparam name="T">The type of content to updload</typeparam>
        /// <param name="name">The name of the index</param>
        /// <param name="list">The list to upload to the index</param>
        /// <returns>The <see cref="Task"/></returns>
        Task UploadAsync<T>(string name, IEnumerable<T> list) where T : class;
    }
}
