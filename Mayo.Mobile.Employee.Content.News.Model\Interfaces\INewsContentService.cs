﻿using Mayo.Mobile.Employee.Content.Model;
using Mayo.Mobile.Employee.Model;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.IO;
using System.Net.Http;
using System.Text;
using System.Threading.Tasks;

namespace Mayo.Mobile.Employee.Content.News.Model.Interfaces
{
    public interface INewsContentService
    {

        /// <summary>
        /// Save the content
        /// </summary>
        /// <returns>The <see cref="Task"/></returns>
        Task SaveAsync();

        /// <summary>
        /// Save the html wrapper
        /// </summary>
        /// <returns>The <see cref="Task"/></returns>
        Task SaveWrapperAsync();

        /// <summary>
        /// Save the html wrapper
        /// </summary>
        /// <param name="list">The list of ids to refresh</param>
        /// <returns>The <see cref="Task"/></returns>
        Task RefeshBlobsAsync(List<string> list = null);

        /// <summary>
        /// Save the content
        /// </summary>
        /// <param name="source">The source list</param>
        /// <param name="categoryId">The category identifier</param>
        /// <param name="id">The content identifier</param>
        /// <returns>The <see cref="Task"/></returns>
        Task SaveAsync(string source, string categoryId, string id);

        /// <summary>
        /// Get content and save it
        /// </summary>
        /// <param name="list">The <see cref="List{T}"/></param>
        /// <returns>The <see cref="Task"/></returns>
        Task RetrieveSaveAsync(List<PackageItem> list);

        /// <summary>
        /// Get the sort order for featured and more
        /// </summary>
        /// <param name="id">The category identifier</param>
        /// <returns>The <see cref="Task{TResult}"/></returns>
        Task<List<string>> SortOrderAsync(string id);

        /// <summary>
        /// Get the list of content fro mthe api
        /// </summary>
        /// <param name="id">The category identifier</param>
        /// <param name="count">The number of items to get</param>
        /// <returns>The <see cref="Task{TResult}"/></returns>
        Task<List<Editorial>> EditorialsAsync(string id, int count = 10);

        /// <summary>
        /// Get the list of content fro mthe api
        /// </summary>
        /// <param name="id">The category identifier</param>
        /// <param name="page">The page number</param>
        /// <returns>The <see cref="Task{TResult}"/></returns>
        Task<List<Editorial>> EditorialsPageAsync(string id, string page);

        /// <summary>
        /// Get the list of content fro mthe api
        /// </summary>
        /// <param name="id">The category identifier</param>
        /// <returns>The <see cref="Task{TResult}"/></returns>
        Task<List<Editorial>> EditorialsPageAllAsync(string id);

        /// <summary>
        /// Get the list of content fro mthe api
        /// </summary>
        /// <param name="id">The category identifier</param>
        /// <param name="postId">The post identifier</param>
        /// <returns>The <see cref="Task{TResult}"/></returns>
        Task<List<Editorial>> EditorialsAllAsync(string id, string postId);

        /// <summary>
        /// Process the content
        /// </summary>
        /// <param name="editorials">The <see cref="List{T}"/></param>
        /// <param name="category">The <see cref="Category"/></param>
        /// <returns>The <see cref="Task{TResult}"/></returns>
        Task<List<PackageItem>> ProcessAsync(List<Editorial> editorials, Category category);

        /// <summary>
        /// Save the content to azure
        /// </summary>
        /// <param name="list">The <see cref="List{T}"/></param>
        /// <returns>The <see cref="Task"/></returns>
        Task SaveAsync(List<PackageItem> list);

        /// <summary>
        /// Save a content article
        /// </summary>
        /// <param name="item">The <see cref="PackageItem"/></param>
        /// <returns>The <see cref="Task"/></returns>
        Task SaveContentAsync(PackageItem item);

        /// <summary>
        /// Save the image to blob
        /// </summary>
        /// <param name="name">The name of the image</param>
        /// <param name="url">The url to the image</param>
        /// <returns>The <see cref="Task"/></returns>
        Task SaveImageAsync(string name, string url);

        /// <summary>
        /// Save the package to table storage
        /// </summary>
        /// <param name="partitionKey">The partition key</param>
        /// <param name="x">The <see cref="PackageItem"/></param>
        /// <returns>The <see cref="Task{TResult}"/></returns>
        Task SaveEntityAsync(string partitionKey, PackageItem x);

        /// <summary>
        /// Get the image
        /// </summary>
        /// <param name="name">The image name</param>
        /// <returns>The <see cref="Task{TResult}"/></returns>
        Task<Stream> GetImageAsync(string name);

        /// <summary>
        /// Gets the favorites list
        /// </summary>
        /// <param name="userId">The user identifier</param>
        /// <param name="count">The number of items</param>
        /// <returns>The <see cref="Task{TResult}"/></returns>
        Task<List<Package>> GetFavoritesAsync(string userId, int? count);

        /// <summary>
        /// Add to the favorites list
        /// </summary>
        /// <param name="userId">The user identifier</param>
        /// <param name="categoryId">The category identifier</param>
        /// <param name="id">The content identifier</param>
        /// <returns>The <see cref="Task"/></returns>
        Task AddFavoriteAsync(string userId, string categoryId, string id);

        /// <summary>
        /// Delete from the favorites list
        /// </summary>
        /// <param name="userId">The user identifier</param>
        /// <param name="categoryId">The category identifier</param>
        /// <param name="id">The content identifier</param>
        /// <returns>The <see cref="Task"/></returns>
        Task DeleteFavoriteAsync(string userId, string categoryId, string id);

        /// <summary>
        /// Get the content
        /// </summary>
        /// <param name="id">The content identifier</param>
        /// <param name="formatted">A value indicating whether the content should be formatted</param>
        /// <returns>The <see cref="Task{TResult}"/></returns>
        Task<Content.Model.Content> GetContentAsync(string id, bool formatted = true);

        /// <summary>
        /// Get the content metadata
        /// </summary>
        /// <param name="id"></param>
        /// <returns>The <see cref="Task{TResult}"/></returns>
        Task<(string Likes, string Reads)> GetMetaDataAsync(string id);

        /// <summary>
        /// Get the front page content
        /// </summary>
        /// <returns>The <see cref="Task{TResult}"/></returns>
        Task<List<Package>> GetListAsync(string id, int? count);


        /// <summary>
        /// Gets the list of comments
        /// </summary>
        /// <param name="id">The content identifier</param>
        /// <returns>The <see cref="Task{TResult}"/></returns>
        Task<List<Comment>> CommentsAsync(string id, string pageNumber = null);

        /// <summary>
        /// Add a comment to the thread
        /// </summary>
        /// <param name="contentId">The content identifier</param>
        /// <param name="threadId">The thread identifier</param>
        /// <param name="userId">The user identifier</param>
        /// <param name="comment">The plain text comment</param>
        /// <param name="replyId">The identifier of the comment to reply to</param>
        /// <returns>The <see cref="Task"/></returns>
        Task PostCommentAsync(string contentId, string threadId, string userId, string comment, string replyId = null);

        /// <summary>
        /// Get the user from the news center
        /// </summary>
        /// <param name="email">The email address</param>
        /// <returns>The <see cref="Task{TResult}"/></returns>
        Task<(string UserName, string Name, string ProfileUrl)> UserAsync(string email);

        /// <summary>
        /// Add the user to the news center
        /// </summary>
        /// <param name="email">The email address</param>
        /// <param name="name">The display name</param>
        /// <returns>The <see cref="Task{TResult}"/></returns>
        Task<(string UserName, string Name, string ProfileUrl)> AddUserAsync(string email, string name = null);

        /// <summary>
        /// Get the content from the api using the http client
        /// </summary>
        /// <param name="url">The api url</param>
        /// <returns>The <see cref="Task{TResult}"/></returns>
        Task<JObject> GetRequestClientAsync(string url);

        /// <summary>
        /// Post the content to the api using the http client
        /// </summary>
        /// <param name="url">The api url</param>
        /// <param name="form">The request body</param>
        /// <returns>The <see cref="Task{TResult}"/></returns>
        Task<JObject> PostRequestClientAsync(string url, Dictionary<string, string> form);

        /// <summary>
        /// Get the content from the api using the http client
        /// </summary>
        /// <param name="url">The api url</param>
        /// <returns>The <see cref="Task{TResult}"/></returns>
        HttpRequestMessage GetRequestMessage(string url);

        /// <summary>
        /// Get the content from the api using the http client
        /// </summary>
        /// <param name="url">The api url</param>
        /// <returns>The <see cref="Task{TResult}"/></returns>
        Task<JObject> GetResponseAsync(HttpRequestMessage requestMessage);

        /// <summary>
        /// Get the list of alerts
        /// </summary>
        /// <returns>The <see cref="Task{TResult}"/></returns>
        Task<Package> GetAlertsAsync();
}
}
