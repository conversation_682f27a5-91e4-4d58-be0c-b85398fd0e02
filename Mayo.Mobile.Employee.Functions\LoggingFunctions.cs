using System;
using System.IO;
using System.Runtime.Serialization.Json;
using System.Text;
using System.Threading.Tasks;
using Azure.Storage.Queues.Models;
using Mayo.Mobile.Logging.Interfaces;
using Mayo.Mobile.Logging.Models;
using Microsoft.Azure.WebJobs;
using Microsoft.Azure.WebJobs.Host;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;

namespace Mayo.Mobile.Employee.Functions
{
    public class LoggingFunctions
    {
        private IAuditService SplunkService;
        private ICosmosLogger<EventWrapper> CosmosLogger;
        private IMayoMobileLogger StorageLogger;

        /// <summary>
        /// Initializes a new instance of the Functions class.
        /// </summary>
        /// <param name="splunkService">The <see cref="IAuditService"/></param>
        /// <param name="cosmosLogger">The <see cref="ICosmosLogger<EventWrapper>"/></param>
        /// <param name="storageLogger">The <see cref="IMayoMobileLogger"/></param>
        public LoggingFunctions(IAuditService splunkService, ICosmosLogger<EventWrapper> cosmosLogger, IMayoMobileLogger storageLogger)
        {
            SplunkService = splunkService;
            CosmosLogger = cosmosLogger;
            StorageLogger = storageLogger;
        }

        /// <summary>
        /// Handle Audit Log events
        /// </summary>
        /// <param name="message">The serialized message from the queue</param>
        /// <param name="cosmosMessage">The message to put on the cosmos processing queue</param>
        /// <param name="splunkMessage">The message to put on the splunk processing queue</param>
        /// <param name="tableMessage">The message to put on the table storage processing queue</param>
        [FunctionName("AuditQueueFunction")]
        public void AuditQueueFunction([QueueTrigger("auditqueue")] string message,
            [Queue("cosmos-logging-queue")] ICollector<string> cosmosMessage,
            [Queue("splunk-logging-queue")] ICollector<string> splunkMessage,
            [Queue("table-logging-queue")] ICollector<string> tableMessage)
        {
            var logEvent = JsonConvert.DeserializeObject<Event>(message);
            cosmosMessage.Add(message);
            tableMessage.Add(message);

            if (logEvent.IsInitialLogEvent)
            {
                splunkMessage.Add(message);
            }
        }

        /// <summary>
        /// Handle Error Log events
        /// </summary>
        /// <param name="message">The serialized message from the queue</param>
        /// <param name="cosmosMessage">The message to put on the cosmos processing queue</param>
        /// <param name="splunkMessage">The message to put on the splunk processing queue</param>
        /// <param name="tableMessage">The message to put on the table storage processing queue</param>
        [FunctionName("ErrorQueueFunction")]
        public void ErrorQueueFunction([QueueTrigger("errorqueue")] string message,
            [Queue("cosmos-logging-queue")] ICollector<string> cosmosMessage,
            [Queue("splunk-logging-queue")] ICollector<string> splunkMessage,
            [Queue("table-logging-queue")] ICollector<string> tableMessage)
        {
            var logEvent = JsonConvert.DeserializeObject<Event>(message);
            cosmosMessage.Add(message);
            tableMessage.Add(message);

            if (logEvent.IsInitialLogEvent)
            {
                splunkMessage.Add(message);
            }
        }

        /// <summary>
        /// Process logging events to Splunk
        /// </summary>
        /// <param name="message">The message on the queue</param>
        /// <returns>The <see cref="Task{TResult}"/></returns>
        [FunctionName("SplunkQueueFunction")]
        public async Task SplunkQueueFunctionAsync([QueueTrigger("splunk-logging-queue")] string message)
        {
            var logEvent = JsonConvert.DeserializeObject<Event>(message);
            var audit = new SplunkAudit(logEvent.UserId, logEvent.PatientId, logEvent.Action, logEvent.Description, logEvent.Level, logEvent.Message, logEvent.StackTrace);

            await SplunkService.AuditAsync(audit);

        }

        /// <summary>
        /// Process logging events to Cosmos
        /// </summary>
        /// <param name="message">The message on the queue</param>
        /// <returns>The <see cref="Task{TResult}"/></returns>
        [FunctionName("CosmosQueueFunction")]
        public async Task CosmosQueueFunctionAsync([QueueTrigger("cosmos-logging-queue")] string message)
        {
            var logEvent = JsonConvert.DeserializeObject<Event>(message);
            await CosmosLogger.ProcessEventAsync(logEvent);
        }

        /// <summary>
        /// Process logging events to table storage
        /// </summary>
        /// <param name="message">The message on the queue</param>
        /// <returns>The <see cref="Task{TResult}"/></returns>
        [FunctionName("TableStorageQueueFunction")]
        public async Task TableStorageQueueFunctionAsync([QueueTrigger("table-logging-queue")] string message)
        {
            var logEvent = JsonConvert.DeserializeObject<Event>(message);
            string sessionId = "";
            string responseStr = "";

            if (logEvent.Properties.TryGetValue("SessionId", out dynamic sessionObj))
            {
                sessionId = sessionObj as string;
            }

            if (logEvent.Properties.TryGetValue("Response", out dynamic responseObj))
            {
                responseStr = JsonConvert.SerializeObject(responseObj);
            }

            if (logEvent.Level.Equals("ERROR", StringComparison.OrdinalIgnoreCase) || logEvent.Level.Equals("WARN", StringComparison.OrdinalIgnoreCase) || logEvent.Level.Equals("FATAL", StringComparison.OrdinalIgnoreCase))
            {

                await StorageLogger.LogErrorAsync(logEvent.UserId, logEvent.DeviceId, logEvent.PatientId, sessionId, logEvent.RequestPath, logEvent.Action, DateTime.Parse(logEvent.Timestamp), logEvent.Description, logEvent.Message, logEvent.StackTrace);
            }
            else
            {
                await StorageLogger.AuditAsync(logEvent.UserId, logEvent.DeviceId, logEvent.PatientId, sessionId, logEvent.RequestPath, logEvent.Action, logEvent.Description, "", responseStr);
            }
        }
    }
}
