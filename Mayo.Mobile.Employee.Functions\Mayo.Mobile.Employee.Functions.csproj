﻿<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup>
    <TargetFramework>netcoreapp3.1</TargetFramework>
    <AzureFunctionsVersion>v3</AzureFunctionsVersion>
  </PropertyGroup>
  <ItemGroup>
    <PackageReference Include="Azure.Storage.Queues" Version="12.6.1" />
    <PackageReference Include="Mayo.Mobile.Logging.Model" Version="3.1.28" />
    <PackageReference Include="Microsoft.Azure.Functions.Extensions" Version="1.1.0" />
    <PackageReference Include="Microsoft.Azure.WebJobs.Extensions.Storage" Version="4.0.4" />
    <PackageReference Include="Microsoft.Extensions.Http" Version="3.1.17" />
    <PackageReference Include="Microsoft.NET.Sdk.Functions" Version="3.0.7" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\Mayo.Mobile.Employee.Azure.DataAccess\Mayo.Mobile.Employee.Azure.DataAccess.csproj" />
    <ProjectReference Include="..\Mayo.Mobile.Employee.Content.Model\Mayo.Mobile.Employee.Content.Model.csproj" />
    <ProjectReference Include="..\Mayo.Mobile.Employee.Content.News.Model\Mayo.Mobile.Employee.Content.News.Model.csproj" />
    <ProjectReference Include="..\Mayo.Mobile.Employee.Facilities.DataAccess\Mayo.Mobile.Employee.Facilities.DataAccess.csproj" />
    <ProjectReference Include="..\Mayo.Mobile.Employee.Location.DataAccess\Mayo.Mobile.Employee.Location.DataAccess.csproj" />
    <ProjectReference Include="..\Mayo.Mobile.Employee.Search.Model\Mayo.Mobile.Employee.Search.Model.csproj" />
  </ItemGroup>
  <ItemGroup>
    <None Update="host.json">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="local.settings.json">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <CopyToPublishDirectory>Never</CopyToPublishDirectory>
    </None>
  </ItemGroup>
</Project>
