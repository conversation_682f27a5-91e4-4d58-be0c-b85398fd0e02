﻿using Novell.Directory.Ldap;
using System;
using System.Collections.Generic;
using System.Text;

namespace Mayo.Mobile.Employee.Codes.Loader
{
    /// <summary>
    /// 
    /// </summary>
    /// <remarks>
    /// https://long2know.com/2017/06/net-core-ldap/
    /// </remarks>
    public class ActiveDirectoryRepository
    {
        private LdapConnection connection = null;

        public ActiveDirectoryRepository(string username, string password)
        {
            // Creating an LdapConnection instance
            this.connection = new LdapConnection
            {
                SecureSocketLayer = true
            };
            
            // Connect function will create a socket connection to the server - Port 389 for insecure and 3269 for secure    
            this.connection.Connect("MFADLDAP.mayo.edu", 3269);
            
            // Bind function with null user dn and password value will perform anonymous bind to LDAP server 
            this.connection.Bind($"MFAD\\{username}", password);
        }

        public string GetEmail(string personId)
        {
            try
            {
                var filter = $"(&(objectClass=user)(employeeID={personId}))";
                var search = this.connection.Search(string.Empty, LdapConnection.SCOPE_SUB, filter, null, false);

                string email = search.HasMore()
                    ? search.Next()
                        .getAttributeSet()
                        .getAttribute("mail").StringValue
                    : string.Empty;

                return email;
            }
            catch
            {
                return personId;
            }
        }
    }
}
