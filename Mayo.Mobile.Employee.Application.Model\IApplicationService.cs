﻿//-----------------------------------------------------------------------
// <copyright file="IApplicationService.cs" company="Mayo Foundation">
//     Copyright © Mayo Foundation. All rights reserved.
// </copyright>
//-----------------------------------------------------------------------

namespace Mayo.Mobile.Employee.Application.Model
{
    using System.Threading.Tasks;
    using Mayo.Mobile.Application.Models;

    /// <summary>
    /// Initializes a new instance of the IApplicationService class.
    /// </summary>
    public interface IApplicationService
    {
        /// <summary>
        /// Get the data from the blob
        /// </summary>
        /// <param name="name">The name of the text</param>
        /// <returns>The <see cref="Task{TResult}"/></returns>
        Task<string> GetDataAsync(string name);

        /// <summary>
        /// Gets the <see cref="Device"/>
        /// </summary>
        /// <param name="id">The device identifier</param>
        /// <returns>The <see cref="Task{TResult}"/></returns>
        Task<Device> GetDeviceAsync(string id);

        /// <summary>
        /// Update the device information
        /// </summary>
        /// <param name="device">The <see cref="Device"/></param>
        /// <returns>The updated <see cref="Task{TResult}"/></returns>
        Task<(Device Device, ApplicationUpdate Update)> SaveDeviceAsync(Device device);

        /// <summary>
        /// Save the user and device entity/>
        /// </summary>
        /// <param name="userId">The user identifier</param>
        /// <param name="deviceId">The device identifier</param>
        /// <returns>The <see cref="Task"/></returns>
        Task SaveAsync(string userId, string deviceId);
    }
}
