﻿//-----------------------------------------------------------------------
// <copyright file="ILocationService.cs" company="Mayo Foundation">
//     Copyright © Mayo Foundation. All rights reserved.
// </copyright>
//-----------------------------------------------------------------------

namespace Mayo.Mobile.Employee.Location.Model
{
    using System;
    using System.Collections.Generic;
    using System.IO;
    using System.Threading.Tasks;

    /// <summary>
    /// Initializes a new instance of the ILocationService class.
    /// </summary>
    public interface ILocationService
    {
        /// <summary>
        /// Get the list of campuses
        /// </summary>
        /// <returns>The <see cref="Task{TResult}"/></returns>
        Task<List<Campus>> CampusListAsync();

        /// <summary>
        /// Get the list of buildings on a campus
        /// </summary>
        /// <param name="campusId">The campus identifier</param>
        /// <returns>The <see cref="Task{TResult}"/></returns>
        Task<List<Building>> BuildingListAsync(string campusId);

        /// <summary>
        /// Get the list of floors in a building
        /// </summary>
        /// <param name="campusId">The campus identifier</param>
        /// <param name="buildingId">The building identifier</param>
        /// <returns>The <see cref="Task{TResult}"/></returns>
        Task<List<Floor>> FloorListAsync(string campusId, string buildingId);

        /// <summary>
        /// Get the list of rooms on a floor
        /// </summary>
        /// <param name="campusId">The campus identifier</param>
        /// <param name="buildingId">The building identifier</param>
        /// <param name="floorId">The floor identifier</param>
        /// <returns>The <see cref="Task{TResult}"/></returns>
        Task<List<Room>> RoomListAsync(string campusId, string buildingId, string floorId);

        /// <summary>
        /// Save the reported issue
        /// </summary>
        ///<param name="facilityIssue">all the info of the facilityIssue<see cref="FacilityIssue"/></param>
        /// <param name="photos">The list of image identifiers</param>
        /// <returns><see cref="Task"/></returns>
        Task<string> SaveReportAsync(FacilityIssue facilityIssue, List<(string Id, Stream Data)> photos);
    }
}
