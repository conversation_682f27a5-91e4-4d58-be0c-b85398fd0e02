﻿//-----------------------------------------------------------------------
// <copyright file="TestExtensions.cs" company="Mayo Foundation">
//     Copyright © Mayo Foundation. All rights reserved.
// </copyright>
//-----------------------------------------------------------------------

namespace System.Web.Http
{
    using Newtonsoft.Json;
    using System.Collections.Generic;
    using System.Linq;
    using System.Net.Http;
    using System.Net.Http.Headers;
    using System.Security.Claims;
    using Microsoft.VisualStudio.TestTools.UnitTesting;
    using System.Security.Principal;
    using Microsoft.AspNetCore.Http;
    using Microsoft.AspNetCore.Mvc;

    /// <summary>
    /// Extends the TestExtensions collection
    /// </summary>
    public static class TestExtensions
    {
        public static ControllerContext SetContext(this ControllerContext c, string accessToken, string userIdentifier, string email = null)
        {
            c = new ControllerContext
            {
                HttpContext = new DefaultHttpContext()
            };
            c.HttpContext.Request.Headers["Authorization"] = $"Bearer {accessToken}";
            c.HttpContext.User = new ClaimsPrincipal().SetIdentity(userIdentifier, email ?? string.Empty);

            c.HttpContext.Request.Scheme = "https";
            c.HttpContext.Request.Host = new Microsoft.AspNetCore.Http.HostString("localhost");
            return c;
        }

        public static ClaimsPrincipal SetIdentity(this ClaimsPrincipal p, string id, string email)
        {
            p.AddIdentity(new ClaimsIdentity(new List<Claim> 
                {
                    new Claim(ClaimTypes.NameIdentifier, id),
                    new Claim("http://schemas.xmlsoap.org/ws/2005/05/identity/claims/emailaddress", email)
                }));
            return p;
        }
    }
}