﻿//-----------------------------------------------------------------------
// <copyright file="IdentityRequest.cs" company="Mayo Foundation">
//     Copyright © Mayo Foundation. All rights reserved.
// </copyright>
//-----------------------------------------------------------------------

namespace Mayo.Mobile.Employee.Web.Api.Model
{
    using System;
    using System.Collections.Generic;
    using System.Net.Http;
    using System.Runtime.Serialization;
    using System.Text;

    /// <summary>
    /// Initializes a new instance of the IdentityRequest class.
    /// </summary>
    [DataContract(Namespace = "http://www.mayo.edu/MayoMobile")]
    public class IdentityRequest : Request
    {
        /// <summary>
        /// Gets or sets the session identifier
        /// </summary>
        [DataMember]
        public string SessionId { get; set; }

        /// <summary>
        /// Gets or sets the device type
        /// </summary>
        [DataMember]
        public string DeviceType { get; set; }

        /// <summary>
        /// Gets or sets the refresh token
        /// </summary>
        [DataMember]
        public string RefreshToken { get; set; }
    }
}
