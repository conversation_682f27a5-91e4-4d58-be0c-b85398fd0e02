﻿// -----------------------------------------------------------------------
// <copyright file="CommentsListContent.cs" company="Mayo Clinic">
//     Copyright © Mayo Foundation. All rights reserved.
// </copyright>
// -----------------------------------------------------------------------

namespace Mayo.Mobile.Employee.Content.News.Model
{
    using System;
    using System.Collections.Generic;
    using System.Text;
    using Newtonsoft.Json;

    /// <summary>
    /// Initializes a new instance of the CommentsListContent
    /// </summary>
    public class CommentsListContent
    {
        /// <summary>
        /// Gets or sets the total number of rows
        /// </summary>
        [JsonProperty("total_rows")]
        public int TotalRows { get; set; }

        /// <summary>
        /// Gets or sets the maximum number of pages
        /// </summary>
        [JsonProperty("max_pages")]
        public int MaxPages { get; set; }

        /// <summary>
        /// Gets or sets the current page
        /// </summary>
        [JsonProperty("current_page")]
        public int CurrentPage { get; set; }

        /// <summary>
        /// Gets or sets the parameters
        /// </summary>
        [JsonProperty("parameters_passed")]
        public string ParametersPassed { get; set; }

        /// <summary>
        /// Gets or sets the comments list
        /// </summary>
        [JsonProperty("data")]
        public List<CommentsListItem> Comments { get; set; }
    }
}
