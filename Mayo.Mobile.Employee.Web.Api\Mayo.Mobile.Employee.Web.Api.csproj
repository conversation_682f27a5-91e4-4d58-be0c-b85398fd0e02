﻿<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>netcoreapp3.1</TargetFramework>
    <IncludeOpenAPIAnalyzers>true</IncludeOpenAPIAnalyzers>
    <UserSecretsId>54aec79c-31b5-42cf-b147-88bfff92241f</UserSecretsId>
    <AddRazorSupportForMvc>true</AddRazorSupportForMvc>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Azure.Extensions.AspNetCore.Configuration.Secrets" Version="1.0.2" />
    <PackageReference Include="Azure.Identity" Version="1.3.0" />
    <PackageReference Include="Azure.Security.KeyVault.Secrets" Version="4.1.0" />
    <PackageReference Include="Mayo.Mobile.Application.Middleware.Model" Version="3.1.28" />
    <PackageReference Include="Microsoft.ApplicationInsights.AspNetCore" Version="2.17.0" />
    <PackageReference Include="Microsoft.AspNetCore.Authentication.AzureAD.UI" Version="3.1.17" />
    <PackageReference Include="Microsoft.AspNetCore.Authentication.JwtBearer" Version="3.1.17" />
    <PackageReference Include="Microsoft.AspNetCore.Mvc.Abstractions" Version="2.2.0" />
    <PackageReference Include="Microsoft.AspNetCore.Mvc.NewtonsoftJson" Version="3.1.17" />
    <PackageReference Include="Microsoft.AspNetCore.Mvc.Versioning" Version="4.2.0" />
    <PackageReference Include="Microsoft.Extensions.Http" Version="3.1.17" />
    <PackageReference Include="Microsoft.Extensions.Http.Polly" Version="3.1.17" />
    <PackageReference Include="Microsoft.Graph" Version="3.30.0" />
    <PackageReference Include="Microsoft.Graph.Auth" Version="1.0.0-preview.3" />
    <PackageReference Include="Microsoft.Graph.Core" Version="1.25.1" />
    <PackageReference Include="Microsoft.Identity.Client" Version="4.29.0" />
    <PackageReference Include="Microsoft.IdentityModel.JsonWebTokens" Version="6.8.0" />
    <PackageReference Include="Microsoft.VisualStudio.Web.BrowserLink" Version="2.2.0" />
    <PackageReference Include="Microsoft.VisualStudio.Web.CodeGeneration.Design" Version="3.1.5" />
    <PackageReference Include="Newtonsoft.Json" Version="13.0.1" />
    <PackageReference Include="Polly" Version="7.2.2" />
    <PackageReference Include="Microsoft.Azure.Services.AppAuthentication" Version="1.6.1" />
    <PackageReference Include="Mayo.Mobile.Notifications.Azure.Storage.Model" Version="3.1.1" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\Mayo.Mobile.Employee.Content.Model\Mayo.Mobile.Employee.Content.Model.csproj" />
    <ProjectReference Include="..\Mayo.Mobile.Employee.Content.News.Model\Mayo.Mobile.Employee.Content.News.Model.csproj" />
    <ProjectReference Include="..\Mayo.Mobile.Employee.Directory.Model\Mayo.Mobile.Employee.Directory.Model.csproj" />
    <ProjectReference Include="..\Mayo.Mobile.Employee.Identity.Model\Mayo.Mobile.Employee.Identity.Model.csproj" />
    <ProjectReference Include="..\Mayo.Mobile.Employee.Location.Model\Mayo.Mobile.Employee.Location.Model.csproj" />
    <ProjectReference Include="..\Mayo.Mobile.Employee.Person.Model\Mayo.Mobile.Employee.Person.Model.csproj" />
    <ProjectReference Include="..\Mayo.Mobile.Employee.RedemptionCodes.Model\Mayo.Mobile.Employee.RedemptionCodes.Model.csproj" />
    <ProjectReference Include="..\Mayo.Mobile.Employee.Search.Model\Mayo.Mobile.Employee.Search.Model.csproj" />
    <ProjectReference Include="..\Mayo.Mobile.Employee.Web.Api.Model\Mayo.Mobile.Employee.Web.Api.Model.csproj" />
    <ProjectReference Include="..\Mayo.Mobile.Employee.Application.Model\Mayo.Mobile.Employee.Application.Model.csproj" />
  </ItemGroup>

  <ItemGroup>
    <Content Remove="Properties\ServiceDependencies\ccs-iter-ema - Web Deploy\profile.arm.json" />
    <Content Remove="wwwroot\css\bootstrap-grid.css" />
    <Content Remove="wwwroot\css\bootstrap-grid.css.map" />
    <Content Remove="wwwroot\css\bootstrap-grid.min.css" />
    <Content Remove="wwwroot\css\bootstrap-grid.min.css.map" />
    <Content Remove="wwwroot\css\bootstrap-reboot.css" />
    <Content Remove="wwwroot\css\bootstrap-reboot.css.map" />
    <Content Remove="wwwroot\css\bootstrap-reboot.min.css" />
    <Content Remove="wwwroot\css\bootstrap-reboot.min.css.map" />
    <Content Remove="wwwroot\css\bootstrap.css" />
    <Content Remove="wwwroot\css\bootstrap.css.map" />
    <Content Remove="wwwroot\css\bootstrap.min.css" />
    <Content Remove="wwwroot\css\bootstrap.min.css.map" />
    <Content Remove="wwwroot\employee.css" />
    <Content Remove="wwwroot\faveicon.ico" />
    <Content Remove="wwwroot\js\bootstrap.bundle.js" />
    <Content Remove="wwwroot\js\bootstrap.bundle.js.map" />
    <Content Remove="wwwroot\js\bootstrap.bundle.min.js" />
    <Content Remove="wwwroot\js\bootstrap.bundle.min.js.map" />
    <Content Remove="wwwroot\js\bootstrap.js" />
    <Content Remove="wwwroot\js\bootstrap.js.map" />
    <Content Remove="wwwroot\js\bootstrap.min.js" />
    <Content Remove="wwwroot\js\bootstrap.min.js.map" />
    <Content Remove="wwwroot\js\employee.css" />
    <Content Remove="wwwroot\js\faveicon.ico" />
  </ItemGroup>
  <ItemGroup>
    <Folder Include="Properties\ServiceDependencies\ccs-iter-ema - Web Deploy\" />
    <Folder Include="Views\Update\" />
  </ItemGroup>
  <ItemGroup>
    <None Include="wwwroot\css\bootstrap-grid.css">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Include="wwwroot\css\bootstrap-grid.css.map" />
    <None Include="wwwroot\css\bootstrap-grid.min.css" />
    <None Include="wwwroot\css\bootstrap-grid.min.css.map" />
    <None Include="wwwroot\css\bootstrap-reboot.css">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Include="wwwroot\css\bootstrap-reboot.css.map" />
    <None Include="wwwroot\css\bootstrap-reboot.min.css" />
    <None Include="wwwroot\css\bootstrap-reboot.min.css.map" />
    <None Include="wwwroot\css\bootstrap.css">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Include="wwwroot\css\bootstrap.css.map" />
    <None Include="wwwroot\css\bootstrap.min.css" />
    <None Include="wwwroot\css\bootstrap.min.css.map" />
    <None Include="wwwroot\employee.css" />
    <None Include="wwwroot\faveicon.ico">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Include="wwwroot\js\bootstrap.bundle.js" />
    <None Include="wwwroot\js\bootstrap.bundle.js.map" />
    <None Include="wwwroot\js\bootstrap.bundle.min.js" />
    <None Include="wwwroot\js\bootstrap.bundle.min.js.map" />
    <None Include="wwwroot\js\bootstrap.js">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Include="wwwroot\js\bootstrap.js.map" />
    <None Include="wwwroot\js\bootstrap.min.js" />
    <None Include="wwwroot\js\bootstrap.min.js.map" />
    <None Include="wwwroot\js\employee.css" />
    <None Include="wwwroot\js\faveicon.ico">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
  </ItemGroup>
  <ItemGroup>
    <Content Update="wwwroot\android-logo.png">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Update="wwwroot\apple-logo.png">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Update="wwwroot\blockquote.png">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Update="wwwroot\css\inlay.css">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Update="wwwroot\css\m-button.css">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Update="wwwroot\css\mayo.css">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Update="wwwroot\css\news.css">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Update="wwwroot\EMP-app-icon-1024.png">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Update="wwwroot\EMPAPPHero.png">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Update="wwwroot\interstitial.css">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Update="wwwroot\js\android-logo.png">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Update="wwwroot\js\apple-logo.png">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Update="wwwroot\js\blockquote.png">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Update="wwwroot\js\EMP-app-icon-1024.png">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Update="wwwroot\js\EMPAPPHero.png">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Update="wwwroot\js\interstitial.css">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Update="wwwroot\js\ogimage.jpg">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Update="wwwroot\js\Site.css">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Update="wwwroot\js\style.css">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Update="wwwroot\ogimage.jpg">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Update="wwwroot\Site.css">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Update="wwwroot\style.css">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
  </ItemGroup>
</Project>
