﻿//-----------------------------------------------------------------------
// <copyright file="IdentityExtensions.cs" company="Mayo Foundation">
//     Copyright © Mayo Foundation. All rights reserved.
// </copyright>
//-----------------------------------------------------------------------

namespace System.Security.Principal
{
	using System.Collections.Generic;
	using System.Linq;
	using System.Net.Http;
	using System.Net.Http.Headers;
	using System.Security.Claims;

    /// <summary>
    /// Extends the Identity collection
    /// </summary>
    public static class IdentityExtensions
	{
		/// <summary>
		/// Get the user identifier from the authenticated user claims
		/// </summary>
		/// <returns>The <see cref="ClaimsIdentity"/></returns>
		public static string GetUserId(this IIdentity identity)
		{
			var id = (from c in ((ClaimsIdentity)identity).Claims
					  where c.Type == "http://schemas.xmlsoap.org/ws/2005/05/identity/claims/nameidentifier"
					  select c.Value).FirstOrDefault();

			return id;
		}

		/// <summary>
		/// Get the name from the authenticated user claims
		/// </summary>
		/// <returns>The <see cref="ClaimsIdentity"/></returns>
		public static string GetName(this System.Security.Principal.IIdentity identity)
		{
			var first = (from c in ((ClaimsIdentity)identity).Claims
						 where c.Type == "http://schemas.xmlsoap.org/ws/2005/05/identity/claims/givenname"
					  select c.Value).FirstOrDefault();

			var last = (from c in ((ClaimsIdentity)identity).Claims
						where c.Type == "http://schemas.xmlsoap.org/ws/2005/05/identity/claims/surname"
						select c.Value).FirstOrDefault();

			return $"{first} {last}";
		}

		/// <summary>
		/// Get the email from the authenticated user claims
		/// </summary>
		/// <returns>The <see cref="ClaimsIdentity"/></returns>
		public static string GetEmailIDMClaim(this System.Security.Principal.IIdentity identity)
		{
			var email = (from c in ((ClaimsIdentity)identity).Claims
						 where c.Type == "http://schemas.xmlsoap.org/ws/2005/05/identity/claims/name"
						 select c.Value).FirstOrDefault();

			return email;
		}

		/// <summary>
		/// Get the email from the authenticated user claims
		/// </summary>
		/// <returns>The <see cref="ClaimsIdentity"/></returns>
		public static string GetEmail(this System.Security.Principal.IIdentity identity)
		{
			var email = (from c in ((ClaimsIdentity)identity).Claims
						 where c.Type == "http://schemas.xmlsoap.org/ws/2005/05/identity/claims/emailaddress"
						 select c.Value).FirstOrDefault();

			return email;
		}

		/// <summary>
		/// Get when the user last changed their password
		/// </summary>
		/// <returns>The <see cref="ClaimsPrincipal"/></returns>
		public static DateTime GetPasswordUpdateDate(this ClaimsPrincipal p)
        {
            var value = (from c in p.Claims
                         where c.Type == "PasswordUpdateDate"
                         select c.Value).FirstOrDefault();

            value = string.IsNullOrEmpty(value) ? DateTime.UtcNow.ToString("o") : value;
            DateTime dt = DateTime.Parse(value);
            return dt;
        }
    }
}