// -----------------------------------------------------------------------
// <copyright file="DirectoryServiceLiveTests.cs" company="Mayo Clinic">
//     Copyright © Mayo Foundation. All rights reserved.
// </copyright>
// -----------------------------------------------------------------------

namespace Mayo.Mobile.Employee.Api.Int.Tests
{
    using System;
    using System.Net.Http;
    using System.Threading.Tasks;
    using Mayo.Mobile.Employee.Directory.Model;
    using Microsoft.VisualStudio.TestTools.UnitTesting;
    using Microsoft.Extensions.Configuration;
    using Microsoft.Extensions.Options;
    using Mayo.Mobile.Employee.Functions;

    /// <summary>
    /// Tests for the real DirectoryService implementation
    /// </summary>
    [TestClass]
    public class DirectoryServiceLiveTests
    {
        private IDirectoryService directoryService = null;

         private string testPersonId = "30257400"; // Use a known valid person ID
        private string rch_testPersonId = "30257400"; //"12502499";  // Use a known valid person ID
         private string az_testPersonId = "15709582"; //"14510251"; // Use a known valid person ID
        private string fl_testPersonId = "11914599"; //"12815004"; // Use a known valid person ID
        private string testPagerNumber = "02550";
        private string testEntity = "Rochester";
        private string testMessage = "Example page from Employee Platform folks";

        [TestInitialize]
        public void Init()
        {
            var config = new ConfigurationBuilder().AddJsonFile("appsettings.json").Build();
            var directoryServiceOptions = Options.Create(new DirectoryServiceAPIOptions
            {
                ClientID = config["DirectoryServiceAPIOptions:ClientID"],
                ClientSecret = config["DirectoryServiceAPIOptions:ClientSecret"],
                TokenURL = config["DirectoryServiceAPIOptions:TokenURL"],
                OnlineDirectoryAPIURL = config["DirectoryServiceAPIOptions:OnlineDirectoryAPIURL"]
            });
            var pagerOptions = Options.Create(new PagerOptions
            {
                PagerSQLConnection_MN = config["PagerOptions:PagerSQLConnection_MN"],
                PagerSQLConnection_FL = config["PagerOptions:PagerSQLConnection_FL"],
                PagerSQLConnection_AZ = config["PagerOptions:PagerSQLConnection_AZ"]
            });            
            try
            {
                // Initialize logging
                DirectoryService.InitializeLogging();
                Console.WriteLine("Logging initialized successfully");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error initializing logging: {ex.Message}");
            }
            
            var client = new HttpClient
            {
                BaseAddress = new Uri("https://dotnetprod.mayo.edu")
            };
            
            // Create the real DirectoryService without storage repository
            // This will only test the API calls, not the storage functionality
            directoryService = new DirectoryService(client, null,directoryServiceOptions,pagerOptions);
        }

        [TestMethod]
        public async Task SearchPersonAsync_WithValidTerm_ReturnsResults()
        {
            // Arrange
            string searchTerm = "hiess"; // Use a known search term that should return results

            // Act
            var results = await directoryService.SearchPersonAsync(searchTerm);

            // Assert
            Assert.IsNotNull(results, "Search results should not be null");
            Assert.IsTrue(results.Count > 0, "Search should return at least one result");
            
            // Log some details about the first result
            if (results.Count > 0)
            {
                Console.WriteLine($"Found person: {results[0].Name}, ID: {results[0].Id}");
            }
        }

        [TestMethod]
        public async Task GetPersonAsync_WithValidId_ReturnsPersonDetails()
        {
            // Act
            testPersonId = rch_testPersonId;
            var person = await directoryService.GetPersonAsync(testPersonId);
            Console.WriteLine($"Person: {person}");
            testPagerNumber = person.Pagers[0].Number;
            var start = testPagerNumber.IndexOf("(");
            var end = testPagerNumber.IndexOf(")");
            testPagerNumber = testPagerNumber.Substring(start + 1, end - start - 1);
            Console.WriteLine($"testPagerNumber: {testPagerNumber}");
            testEntity = person.Pagers[0].Entity;

            // Assert
            Assert.IsNotNull(person, "Person details should not be null");
            Assert.AreEqual(testPersonId, person.Id, "Person ID should match the requested ID");
            
            // Log some details about the person
            Console.WriteLine($"Person details: {person.DisplayName}, Email: {person.EmailAddress}");
            Console.WriteLine($"Title: {person.Title}");
            
            if (person.Departments.Count > 0)
            {
                Console.WriteLine($"Department: {person.Departments[0].Name}");
                Console.WriteLine($"Location: {person.Departments[0].Location.Campus}");
            }
        }

        [TestMethod]
        public async Task Page_Test()
        {
            await directoryService.PageAsyncUsingSP(testPagerNumber, testEntity, testMessage, testPersonId);

        }

        [TestMethod]
        public async Task RunTestsInOrder()
        {
            testPersonId = rch_testPersonId;
            var person = await directoryService.GetPersonAsync(testPersonId);
            Console.WriteLine($"Person: {person}");
            testPagerNumber = person.Pagers[0].Number;
            Console.WriteLine($"testPagerNumber: {testPagerNumber}");
            testEntity = person.Pagers[0].Entity;
            await directoryService.PageAsyncUsingSP(testPagerNumber, testEntity, testMessage, testPersonId);

        }        
    }
}