# ASP.NET
# Build and test ASP.NET projects.
# Add steps that publish symbols, save build artifacts, deploy, and more:
# https://docs.microsoft.com/azure/devops/pipelines/apps/aspnet/build-aspnet-4

# Starter pipeline
# Start with a minimal pipeline that you can customize to build and deploy your code.
# Add steps that build, run tests, deploy, and more:
# https://aka.ms/yaml

trigger:
- development

pool:
  vmImage: windows-latest

variables:
  RestoreBuildProjects: '**/Mayo.Mobile.Employee.Functions.csproj'
  BuildPlatform: 'Any CPU'
  BuildConfiguration: 'Release'

#pool:
#  vmImage: 'windows-latest'

steps:
- task: UseDotNet@2
  displayName: 'Install .NET Core SDK'
  inputs:
    packageType: 'sdk'
    version: '3.1.x' # Specify the required .NET SDK version
    installationPath: $(Agent.ToolsDirectory)/dotnet

- task: DotNetCoreCLI@2
  displayName: Restore
  inputs:
    command: 'custom'
    custom: 'restore'
    projects: '$(RestoreBuildProjects)'

- task: DotNetCoreCLI@2
  displayName: Publish
  inputs:
    command: 'custom'
    custom: 'publish'
    publishWebProjects: false
    projects: '$(RestoreBuildProjects)'
    arguments: '-o $(Build.ArtifactStagingDirectory)'
    zipAfterPublish: true
    modifyOutputPath: true

#- task: NuGetToolInstaller@1
#  displayName: 'Use NuGet 5.8+'
#  inputs:
#    versionSpec: '>=5.8'
#
#- task: NuGetCommand@2
#  displayName: 'NuGet Restore'
#  inputs:
#    command: 'restore'
#    restoreSolution: '$(RestoreBuildProjects)'
#    feedsToUse: 'select'
#    vstsFeed: '35486eb6-55e5-4079-b29c-15c70ea0903b' # Add any additional feeds as needed

#- task: VSBuild@1
#  displayName: 'Build Solution'
#  inputs:
#    solution: '$(RestoreBuildProjects)'
#    msbuildArgs: '/t:Restore /p:Configuration=$(BuildConfiguration)'
#    platform: 'Any CPU'
#    configuration: '$(BuildConfiguration)'

#- task: VSBuild@1
#  displayName: 'Publish Solution'
#  inputs:
#    solution: '$(RestoreBuildProjects)'
#    msbuildArgs: '/t:Restore /p:Configuration=$(BuildConfiguration) /p:OutputPath=$(build.artifactstagingdirectory)'
#    platform: 'Any CPU'
#    configuration: '$(BuildConfiguration)'
#    clean: true

#- task: ArchiveFiles@2
#  displayName: 'Archive Files'
#  inputs:
#    rootFolderOrFile: '$(build.artifactstagingdirectory)'
#    includeRootFolder: false
#    archiveType: 'zip'
#    archiveFile: '$(build.artifactstagingdirectory)/mobileemployeefunctions.zip'
#    replaceExistingArchive: true

- task: PublishBuildArtifacts@1
  displayName: 'Publish Artifact'
  inputs:
    PathtoPublish: '$(build.artifactstagingdirectory)'
  condition: succeededOrFailed()
  
# - task: Veracode@3
#   inputs:
#     ConnectionDetailsSelection: 'Service Connection' # access Veracode with service connection or Veracode API credentials
#     AnalysisService: '2022_Veracode_connection' # service connection name for accessing Veracode
#     veracodeAppProfile: 'POS-MobileFunctionAPI' # Veracode application profile to scan
#     version: '$(build.buildNumber)' # name of the scan to run
#     filepath: '$(build.artifactstagingdirectory)/$(Build.BuildId).zip' # filepath or folderpath of files to upload to Veracode
#     maximumWaitTime: '360' 
