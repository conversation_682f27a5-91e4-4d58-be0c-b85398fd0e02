﻿//-----------------------------------------------------------------------
// <copyright file="Photo.cs" company="Mayo Foundation">
//     Copyright © Mayo Foundation. All rights reserved.
// </copyright>
//-----------------------------------------------------------------------

namespace Mayo.Mobile.Employee.Model
{
    using System.Runtime.Serialization;

    /// <summary>
    /// Initializes a new instance of the Photo class.
    /// </summary>
    [DataContract(Namespace = "http://www.mayo.edu/MayoMobile")]
    public class Photo
    {
        /// <summary>
        /// Gets or sets the photo file name 
        /// </summary>
        [DataMember(Name = "Id")]
        public string FileName { get; set; }

        /// <summary>
        /// Gets or sets the timestamp of the photo
        /// </summary>
        [DataMember]
        public string Timestamp { get; set; }

        /// <summary>
        /// Type of Data being sent
        /// </summary>
        [DataMember]
        public string MimeType { get; set; }
    }
}
