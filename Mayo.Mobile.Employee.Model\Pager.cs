﻿//-----------------------------------------------------------------------
// <copyright file="Pager.cs" company="Mayo Foundation">
//     Copyright © Mayo Foundation. All rights reserved.
// </copyright>
//-----------------------------------------------------------------------

namespace Mayo.Mobile.Employee.Model
{
    using System.Runtime.Serialization;

    /// <summary>
    /// Initializes a new instance of the Pager class.
    /// </summary>
    [DataContract(Namespace = "http://www.mayo.edu/MayoMobile")]
    public class Pager
    {
        /// <summary>
        /// The pager number
        /// </summary>
        [DataMember(Name = "Number")]
        public string Number { get; set; }

        /// <summary>
        /// The entity
        /// </summary>
        [DataMember(Name = "Entity")]
        public string Entity { get; set; }

        /// <summary>
        /// The url
        /// </summary>
        [IgnoreDataMember]
        public string Url { get; set; }

        /// <summary>
        /// The length
        /// </summary>
        [DataMember(Name = "Length")]
        public string Length { get; set; }

        /// <summary>
        /// The type
        /// </summary>
        [DataMember(Name = "Type")]
        public string Type { get; set; }
    }
}
