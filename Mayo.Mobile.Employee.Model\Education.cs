﻿// -----------------------------------------------------------------------
// <copyright file="Education.cs" company="Mayo Clinic">
//     Copyright © Mayo Foundation. All rights reserved.
// </copyright>
// -----------------------------------------------------------------------

namespace Mayo.Mobile.Employee.Model
{
    using System;
    using System.Collections.Generic;
    using System.Linq;
    using System.Runtime.Serialization;
    using System.Text;

    /// <summary>
    /// Initializes a new instance of the Education class.
    /// </summary>
    [DataContract(Namespace = "http://www.mayo.edu/MayoMobile")]
    public class Education : Mayo.Mobile.Staff.Model.Education
    {
        /// <summary>
        /// Initializes a new instance of the Education class.
        /// </summary>
        public Education()
        {
        }

        /// <summary>
        /// Initializes a new instance of the Education class.
        /// </summary>
        /// <param name="degree">The degree</param>
        public Education(string degree)
        {
            this.Degree = degree;
            this.Emphasis = string.Empty;
            this.StartDate = string.Empty;
            this.EndDate = string.Empty;
        }
    }
}