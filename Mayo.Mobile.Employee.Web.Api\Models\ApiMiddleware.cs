﻿using Mayo.Mobile.Application.Middleware.Model;
using Mayo.Mobile.Logging.Interfaces;
using Mayo.Mobile.Logging.Models;
using Microsoft.ApplicationInsights.AspNetCore.Extensions;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Http.Features;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace Mayo.Mobile.Employee.Web.Api.Models
{
    /// <summary>
    /// Initializes a new instance of the ApiMiddleware class.
    /// </summary>
    public class ApiMiddleware : MobileMiddleware
    {
        /// <summary>
        /// Initializes a new instance of the ApiMiddleware class.
        /// </summary>
        /// <param name="next">The <see cref="RequestDelegate"/></param>
        /// <param name="logger"><see cref="ILogger<MobileMiddleware>"/></param>
        /// <param name="mobileLogger"><see cref="IMayoMobileLogger"/></param>
        /// <param name="exceptionHandlingConfigurations"><see cref="IDictionary<Type, ExceptionHandlingConfiguration>"/></param>
        public ApiMiddleware(RequestDelegate next, ILogger<MobileMiddleware> logger, IMayoMobileLogger mobileLogger, IDictionary<Type, ExceptionHandlingConfiguration> exceptionHandlingConfigurations, IOptions<MiddlewareOptions> configurationOptions)
            : base(next, logger, mobileLogger, exceptionHandlingConfigurations, configurationOptions)
        {
        }

        /// <summary>
        /// Invoke the middleware function to log the request
        /// </summary>
        /// <param name="httpContext">The <see cref="HttpContext"/></param>
        /// <returns>The <see cref="Task"/></returns>
        public override async Task InvokeAsync(HttpContext httpContext)
        {

            var actionDiscription = AuditDecorator.GetActionDiscription(httpContext.Request.GetUri());
            httpContext.Items.Add("LoggingAction", actionDiscription);

            await base.InvokeAsync(httpContext);
        }

    }
}
