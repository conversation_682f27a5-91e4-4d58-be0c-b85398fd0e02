﻿using Mayo.Mobile.Employee.Facilities.DataAccess.Interfaces;
using Mayo.Mobile.Employee.Facilities.DataAccess.Models;
using Microsoft.Extensions.Options;
using Newtonsoft.Json;
using System;
using System.IO;
using System.Linq;
using System.Net.Http;
using System.Net.Http.Headers;
using System.Text;
using System.Threading.Tasks;

namespace Mayo.Mobile.Employee.Facilities.DataAccess
{
    public class ServiceNowRepository: IServiceNowRepo
    {
        private HttpClient httpClient;
        private string facilityAttachmentsTableName;

        public ServiceNowRepository(HttpClient client, IOptions<ServiceNowRepoOptions> configurationOptions)
        {
            httpClient = client;
            httpClient.BaseAddress = new Uri(configurationOptions.Value.ServiceNowWorkOrderBaseUrl);
            facilityAttachmentsTableName = configurationOptions.Value.ServicenowWorkOrderAttachmentTable;
            httpClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Basic", CreateAuthorizationHeader(configurationOptions.Value.ServiceNowUser, configurationOptions.Value.ServiceNowPass));
        }

        /// <summary>
        /// For Use with Test Cases only
        /// </summary>
        /// <param name="baseUrl"></param>
        /// <param name="attachmentsTableName"></param>
        /// <param name="authuser"></param>
        /// <param name="authpass"></param>
        public ServiceNowRepository(ServiceNowRepoOptions configurationOptions)
        {
            httpClient = new HttpClient();
            httpClient.BaseAddress = new Uri(configurationOptions.ServiceNowWorkOrderBaseUrl);
            facilityAttachmentsTableName = configurationOptions.ServicenowWorkOrderAttachmentTable;
            httpClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Basic", CreateAuthorizationHeader(configurationOptions.ServiceNowUser, configurationOptions.ServiceNowPass));
        }

        /// <summary>
        /// Use to create a Facility WorkOrder 
        /// </summary>
        /// <param name="info">The request body</param>
        /// <returns></returns>
        public async Task<FacilityCreateWOResult> CreateFacilityWorkOrderAsync(FacilitiesCreateWORequestBody info)
        {
            var json = JsonConvert.SerializeObject(info);
            var httpContent = new StringContent(json, Encoding.UTF8, "application/json");
            var response = await httpClient.PostAsync("api/x_nuvo_eam/fac_wo_util/post_wo", httpContent);
            response.EnsureSuccessStatusCode();
            var workOrderResponse = JsonConvert.DeserializeObject<FacilityCreateWOResponse>(await response.Content.ReadAsStringAsync());
            return workOrderResponse.Result.FirstOrDefault();
        }

        /// <summary>
        /// Use to Attach an Image to a Created WorkOrder
        /// </summary>
        /// <param name="imageStream">the image</param>
        /// <param name="sysId">the id for the workorder</param>
        /// <param name="imageName"> the images file name</param>
        /// <returns></returns>
        public async Task<FacilityWOAttachmentResult> AttachImageToFacilityWorkOrder(Stream imageStream, string sysId, string imageName)
        {
            var content = new StreamContent(imageStream);
            content.Headers.ContentType = new MediaTypeHeaderValue("image/jpeg");
            var url = $"api/now/attachment/file?table_name={facilityAttachmentsTableName}&table_sys_id={sysId}&file_name={imageName}";
            var response = await httpClient.PostAsync(url, content);
            response.EnsureSuccessStatusCode();
            var workOrderResponse = JsonConvert.DeserializeObject<FacilityWOAttachmentResponse>(await response.Content.ReadAsStringAsync());
            return workOrderResponse.Result;
        }

        private string CreateAuthorizationHeader(string username, string password)
        {
            var byteArray = Encoding.ASCII.GetBytes($"{username}:{password}");
            return Convert.ToBase64String(byteArray);
        }
    }
}
