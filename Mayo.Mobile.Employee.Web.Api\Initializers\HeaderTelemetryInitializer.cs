﻿//-----------------------------------------------------------------------
// <copyright file="HeaderTelemetryInitializer.cs" company="Mayo Foundation">
//     Copyright © Mayo Foundation. All rights reserved.
// </copyright>
//-----------------------------------------------------------------------

namespace Mayo.Mobile.Employee.Web.Api
{
    using Microsoft.ApplicationInsights.Channel;
    using Microsoft.ApplicationInsights.Extensibility;
    using Microsoft.AspNetCore.Http;
    using System.Collections.Generic;

    public class HeaderTelemetryInitializer: ITelemetryInitializer
    {
        private readonly IHttpContextAccessor httpContextAccessor;

        private readonly List<string> customRequestHeaders = new List<string>
        {
            "DeviceOS",
            "DeviceType"
        };

        /// <summary>
        /// Initializer for access to the http context
        /// </summary>
        /// <param name="httpContextAccessor"></param>
        public HeaderTelemetryInitializer(IHttpContextAccessor httpContextAccessor)
        {
            this.httpContextAccessor = httpContextAccessor ?? throw new System.ArgumentNullException(nameof(httpContextAccessor));
        }

        /// <summary>
        /// Initializer for the telemtry context
        /// </summary>
        /// <param name="telemetry"></param>
        public void Initialize(ITelemetry telemetry)
        {
            var context = httpContextAccessor.HttpContext;
            if (context == null) return;

            foreach (var customRequestHeader in customRequestHeaders)
            {
                if (context.Request.Headers.TryGetValue(customRequestHeader, out var value))
                {
                    telemetry.Context.GlobalProperties[customRequestHeader] = value.ToString();
                }
            }
        }

    }
}