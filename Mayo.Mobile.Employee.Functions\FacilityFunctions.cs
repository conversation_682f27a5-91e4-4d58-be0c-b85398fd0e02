using System;
using System.Threading;
using System.Threading.Tasks;
using Mayo.Mobile.Employee.Azure.DataAccess.Models;
using Mayo.Mobile.Employee.Functions.Interfaces;
using Microsoft.Azure.WebJobs;
using Newtonsoft.Json;

namespace Mayo.Mobile.Employee.Functions
{
    public class FacilityFunctions
    {
        private IFacilityFunctionsService FacilityFunctionsSvc;

        public FacilityFunctions(IFacilityFunctionsService facilityFunctionsSvc)
        {
            FacilityFunctionsSvc = facilityFunctionsSvc;
        }
        /// <summary>
        /// Queue Trigger for processing WorkOrder Messages that need to be created in ServiceNow
        /// </summary>
        /// <param name="message">The message read from the queue</param>
        /// <returns>The <see cref="Task"/></returns>
        [FunctionName("ProcessFacilityWorkOrderQueueMessage")]
        public async Task ProcessFacilityWorkOrderQueueMessageAsync([QueueTrigger("facilityworkorderqueue")] string message)
        {
            var facilityIssue = JsonConvert.DeserializeObject<FacilityWorkOrderQueueMessage>(message);
            await FacilityFunctionsSvc.ProcessFacilityIssueAsync(facilityIssue);
        }

        /// <summary>
        /// Queue Trigger for processing Attachment Messages to be added to ServiceNow WorkOrders
        /// </summary>
        /// <param name="message">The message read from the queue</param>
        /// <returns>The <see cref="Task"/></returns>
        [FunctionName("ProcessFacilityWorkOrderAttachmentQueueMessage")]
        public async Task ProcessFacilityWorkOrderAttachmentQueueMessageAsync([QueueTrigger("facilityworkorderqueueattachments")] string message)
        {
            var attachment = JsonConvert.DeserializeObject<FacilityWorkOrderAttachment>(message);
            await FacilityFunctionsSvc.ProcessWorkOrderAttachmentAsync(attachment);
        }

        // <summary>
        // This function gets the location data from UDP
        // </summary>
        // <param name="timerInfo">The <see cref="TimerInfo"/></param>
        // <param name="token">The <see cref="CancellationToken"/></param>
        // <returns>The <see cref="Task"/></returns>
        [FunctionName("ProcessLocationsAsync")]
        [Singleton]
        public async Task ProcessLocationsAsync([TimerTrigger("0 0 8 * * SUN", RunOnStartup = false)] TimerInfo timerInfo, CancellationToken token)
        {
            await FacilityFunctionsSvc.ProcessFacilityLocationLoadingAsync();
        }
    }
}
