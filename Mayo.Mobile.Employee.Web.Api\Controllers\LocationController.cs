﻿//-----------------------------------------------------------------------
// <copyright file="LocationController.cs" company="Mayo Foundation">
//     Copyright © Mayo Foundation. All rights reserved.
// </copyright>
//-----------------------------------------------------------------------

namespace Mayo.Mobile.Employee.Web.Api.Controllers
{
    using System;
    using System.Collections.Concurrent;
    using System.Collections.Generic;
    using System.IO;
    using System.Linq;
    using System.Security.Principal;
    using System.Threading;
    using System.Threading.Tasks;
    using Mayo.Mobile.Employee.Location.Model;
    using Mayo.Mobile.Employee.Web.Api.Model;
    using Mayo.Mobile.Logging.Model;
    using Microsoft.AspNetCore.Authorization;
    using Microsoft.AspNetCore.Http;
    using Microsoft.AspNetCore.Mvc;
    using Newtonsoft.Json;
    using Newtonsoft.Json.Linq;

    /// <summary>
    /// Initializes a new instance of the LocationController class.
    /// </summary>
    [Authorize]
    [ApiVersion("1.0")]
    [ApiController]
    [Route("api/[controller]")]
    [Route("api/v{version:apiVersion}/[controller]")]
    public class LocationController : ControllerBase
    {
        private ILocationService locationService;

        /// <summary>
        /// Initializes a new instance of the LocationController class.
        /// </summary>
        /// <param name="service">The <see cref="IContentService"/></param>
        /// <param name="logger">The <see cref="ILogger"/></param>
        public LocationController(ILocationService service)
        {
            locationService = service;
        }

        /// <summary>
        /// Gets the list of location data
        /// </summary>
        /// <param name="campusId">The campus identifier</param>
        /// <param name="buildingId">The building identifier</param>
        /// <param name="floorId">The floor identifier</param>
        /// <returns>The <see cref="Task{TResult}"/></returns>
        [HttpGet, Route("{campusId?}/{buildingId?}/{floorId?}")]
        [ProducesResponseType(typeof(List<Campus>), 200)]
        [ProducesResponseType(typeof(List<Building>), 200)]
        [ProducesResponseType(typeof(List<Floor>), 200)]
        [ProducesResponseType(typeof(List<Room>), 200)]
        [ProducesResponseType(400)]
        [ProducesResponseType(404)]
        public async Task<IActionResult> GetAsync(string campusId = null, string buildingId = null, string floorId = null)
        {
            var userId = User.Identity.GetUserId();

            switch (true)
            {

                case bool _ when campusId != null && buildingId != null && floorId != null:
                    var rooms = await locationService.RoomListAsync(campusId, buildingId, floorId);
                    return Ok(rooms);
                case bool _ when campusId != null && buildingId != null && floorId == null:
                    var floors = await locationService.FloorListAsync(campusId, buildingId);
                    return Ok(floors);
                case bool _ when campusId != null && buildingId == null && floorId == null:
                    var buildings = await locationService.BuildingListAsync(campusId);
                    return Ok(buildings);
                case bool _ when campusId == null && buildingId == null && floorId == null:
                    var campuses = await locationService.CampusListAsync();
                    return Ok(campuses);
                default:
                    return BadRequest();
            }
        }

        /// <summary>
        /// Send the reported issue
        /// </summary>
        /// <param name="json">The json form data</param>
        /// <param name="images">The list of images</param>
        /// <returns>The <see cref="Task{TResult}"/></returns>
        [HttpPost, Route("report/issue")]
        public async Task<IActionResult> SendReportAsync([FromForm] string json, List<IFormFile> images)
        {
            var userId = User.Identity.GetUserId();

            images ??= new List<IFormFile>();

            var issue = JsonConvert.DeserializeObject<IssueRequest>(json);
            issue.Validate(images.Count);

            var facilityIssue = new FacilityIssue(userId, issue.CampusId, issue.BuildingId, issue.FloorId, issue.RoomId, issue.Location, issue.Comments);

            ConcurrentBag<(string Id, Stream Data)> list = new ConcurrentBag<(string Id, Stream Data)>();

            //// limit the number of tasks using the semaphore instead of ActionBlock
            SemaphoreSlim throttler = new SemaphoreSlim(Environment.ProcessorCount, Environment.ProcessorCount);
            
            await Task.WhenAll(images.Select(async (image, index) =>
            {
                await throttler.WaitAsync();
                var photoInfo = issue.Photos.ElementAt(index); // links photos list with streamed image
                try
                {
                    var stream = new MemoryStream();
                    await image.CopyToAsync(stream);
                    stream.Position = 0; //rolls stream cursor back to start

                    var fileName = photoInfo.FileName;
                    if (fileName.EndsWith(".png") == false && fileName.EndsWith(".jpg") == false)
                    {
                        switch (photoInfo.MimeType)
                        {
                            case "image/png":
                                fileName = $"{fileName}.png";
                                break;
                            case "image/jpeg":
                                fileName = $"{fileName}.jpg";
                                break;
                            default:
                                throw new FileFormatException($"{photoInfo.MimeType} is not a valid MimeType for this call.");
                        }
                    }
                    
                    list.Add(($"{facilityIssue.Id}_{fileName}", stream));
                }
                finally
                {
                    throttler.Release();
                }
            }));

            facilityIssue.PhotoIds = (from photo in list select photo.Id).ToList();

            var workOrderId = await locationService.SaveReportAsync(facilityIssue, list.ToList());
            return Ok();
            //return Ok(workOrderId); use when we are ready for apps to test workOrderId
        }
    }
}