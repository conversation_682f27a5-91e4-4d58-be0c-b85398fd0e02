﻿//-----------------------------------------------------------------------
// <copyright file="IUserRepository.cs" company="Mayo Foundation">
//     Copyright © Mayo Foundation. All rights reserved.
// </copyright>
//-----------------------------------------------------------------------
namespace Mayo.Mobile.Employee.Model
{
    using System;
    using System.Collections.Generic;
    using System.Text;
    using System.Threading.Tasks;

    /// <summary>
    /// Initializes a new instance of the IUserRepository class.
    /// </summary>
    public interface IUserRepository
    {
        /// <summary>
        /// Save the <see cref="Identity"/>
        /// </summary>
        /// <param name="i">The <see cref="Identity"/></param>
        /// <returns>The <see cref="Task"/></returns>
        Task SaveAsync(Identity i);

        /// <summary>
        /// Save the user authentication action. If they authenticated with user credentials or did a refresh
        /// </summary>
        /// <param name="userId">The user identifier</param>
        /// <param name="action">The action the user performed</param>
        /// <param name="accessToken">The access token</param>
        /// <param name="refreshToken">The refresh token</param>
        /// <returns>The <see cref="Task"/></returns>
        Task SaveAsync(string userId, string action, string accessToken, string refreshToken);

        /// <summary>
        /// Get the <see cref="Identity"/>
        /// </summary>
        /// <param name="userId">The user identifier</param>
        /// <param name="accessToken">The access token</param>
        /// <param name="refreshToken">The refresh token</param>
        /// <returns>The <see cref="Task"/></returns>
        Task<Identity> IdentityAsync(string userId, string accessToken, string refreshToken = null);

        /// <summary>
        /// Get the last time the user authenticated with username and passwors
        /// </summary>
        /// <param name="userId">The user identifier</param>
        /// <param name="accessToken">The access token</param>
        /// <param name="refreshToken">The refresh token</param>
        /// <returns>The <see cref="Task"/></returns>
        Task<DateTimeOffset> LastLogonTimeAsync(string userId);

        /// <summary>
        /// Get the status
        /// </summary>
        /// <param name="userId">The user identifier</param>
        /// <returns>The <see cref="Task{TResult}"/></returns>
        Task<Status> StatusAsync(string userId);

        /// <summary>
        /// Save the status
        /// </summary>
        /// <param name="userId">The user identifier</param>
        /// <param name="status">The <see cref="Status"/></param>
        /// <returns>The <see cref="Task"/></returns>
        Task SaveAsync(string userId, Status status);
    }
}