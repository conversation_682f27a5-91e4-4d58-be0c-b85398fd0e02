﻿//-----------------------------------------------------------------------
// <copyright file="Program.cs" company="Mayo Foundation">
//     Copyright © Mayo Foundation. All rights reserved.
// </copyright>
//-----------------------------------------------------------------------

namespace Mayo.Mobile.Employee.Codes.Loader
{
    using System;
    using System.Collections.Generic;
    using System.DirectoryServices.AccountManagement;
    using System.IO;
    using System.Linq;
    using System.Threading;
    using System.Threading.Tasks;
    using DocumentFormat.OpenXml;
    using DocumentFormat.OpenXml.Packaging;
    using DocumentFormat.OpenXml.Spreadsheet;
    using Mayo.Mobile.Application.Azure.DataAccess.Interfaces;
    using Mayo.Mobile.Application.Azure.DataAccess.Models;
    using Mayo.Mobile.Application.Azure.DataAccess.Repositories;
    using Mayo.Mobile.Employee.RedemptionCodes.Model;
    using Microsoft.Extensions.Configuration;
    using Microsoft.Extensions.Configuration.FileExtensions;
    using Microsoft.Extensions.Configuration.Json;
    using Microsoft.Extensions.Options;

    /// <summary>
    /// Initializes a new instance of the Program class.
    /// </summary>
    public class Program
    {
        /// <summary>
        /// The main program
        /// </summary>
        /// <param name="args">The run time arguments</param>
        public static void Main(string[] args)
        {
            //Console.WriteLine($"Reading: {args[0]}");

            var builder = new ConfigurationBuilder()
                .SetBasePath(Directory.GetCurrentDirectory())
                .AddJsonFile("appsettings.json", optional: true, reloadOnChange: true);

            IConfigurationRoot configuration = builder.Build();
            ITableStorageRepository tableStorageRepo = new TableStorageRepository(Options.Create<StorageAccountRepoOptions>(new StorageAccountRepoOptions() {  ConnectionString = configuration.GetConnectionString("StorageAccount") }));
            CodeRepository repository = new CodeRepository(tableStorageRepo);

            ////var activeDirectoryRepository = new ActiveDirectoryRepository("TU02511", "Cabe1as01Cabe01");
            ////var email = activeDirectoryRepository.GetEmail("********");

            //// LoadData(args[0]);
            ////ReportUserData(repository);
            ////ReportCodesCounts(repository);
            //// ReportEmailList(repository, activeDirectoryRepository);
        }

        private static void ReportUserData(CodeRepository repository)
        {
            Console.WriteLine($"Querying...");

            var users = repository.GetUsersAsync().GetAwaiter().GetResult();
            var grouped = (from x in users
                           group x by x.date.Date.ToString("MM/dd") into g
                           orderby g.Key ascending
                           select new { Date = g.Key, Count = g.Count() });

            
            grouped.ToList().ForEach(x => Console.WriteLine($"{x.Date}     {x.Count}"));
            Console.WriteLine($"Total codes redeemed: {users.Count}");
            Console.WriteLine($"Finished.");
        }

        private static void ReportCodesCounts(CodeRepository repository)
        {
            Console.WriteLine($"Querying...");

            var counts = repository.GetCodeCountsAsync().GetAwaiter().GetResult();
            Console.WriteLine($"Total codes redeemed: {counts.Item1} out of {counts.Item2}");
            Console.WriteLine($"Finished.");
        }

        private static void ReportEmailList(CodeRepository repository, ActiveDirectoryRepository activeDirectoryRepository)
        {
            Console.WriteLine($"Querying...");

            var users = repository.GetUsersAsync().GetAwaiter().GetResult();

            List<string> lines = new List<string>();

            users.ForEach(x =>
                {

                    var email = activeDirectoryRepository.GetEmail(x.UserId);
                    if (email.Equals(x.UserId))
                    {
                        activeDirectoryRepository = new ActiveDirectoryRepository("TU02511", "Cabe1as01Cabe01");
                        email = activeDirectoryRepository.GetEmail(x.UserId);
                    }

                    //// Console.WriteLine($"{email}     {x.date.Date.ToString("MM/dd")}");
                    lines.Add($"{email}, {x.date.Date:MM/dd}");
                });

            File.WriteAllLines(@"C:\Data\EmailCodes3.txt", lines);

            Console.WriteLine($"Finished.");
        }

        private static void LoadData(string file, CodeRepository repository)
        {

            var list = GetRows(file);

            Console.WriteLine($"Number of codes found: {list.Count}.");
            Console.WriteLine($"Saving...");

            int count = 0;

            Parallel.ForEach(
                list,
                x =>
                {
                    if (repository.SaveAsync(x.Key, x.Value).GetAwaiter().GetResult())
                    {
                        Interlocked.Increment(ref count);
                    }
                });

            Console.WriteLine($"There were {count} new codes saved.");
        }


        /// <summary>
        /// Get the rows from the spreadsheet
        /// </summary>
        /// <remarks>https://docs.microsoft.com/en-us/office/open-xml/working-with-sheets</remarks>
        /// <param name="fileName">The file name and path</param>
        /// <returns>The <see cref="Dictionary{TKey, TValue}"/></returns>
        private static Dictionary<string, string> GetRows(string fileName)
        {
            bool isvalue = false;
            Dictionary<string, string> list = new Dictionary<string, string>();

            using (SpreadsheetDocument spreadsheetDocument = SpreadsheetDocument.Open(fileName, false))
            {
                WorkbookPart workbookPart = spreadsheetDocument.WorkbookPart;
                WorksheetPart worksheetPart = workbookPart.WorksheetParts.First();

                var rows = worksheetPart.Worksheet.Descendants<Row>().ToList();
                rows.ForEach(x =>
                {
                    string code = string.Empty;
                    string url = string.Empty;

                    var cells = x.Descendants<Cell>().ToList();
                    var cell = cells.First();

                    cells.ForEach(y =>
                    {
                        if (y.CellReference.HasValue && y.CellReference.Value.StartsWith("A"))
                        {
                            var value = GetCell(workbookPart, y);
                            if (value.Equals("Code", StringComparison.OrdinalIgnoreCase))
                            {
                                isvalue = true;
                                code = value;
                            }
                            else if (isvalue == true)
                            {
                                code = value;
                            }

                        }
                        else if (y.CellReference.HasValue && y.CellReference.Value.StartsWith("C") && isvalue == true)
                        {
                            var value = GetCell(workbookPart, y);
                            url = value;

                            if (code.Equals("Code", StringComparison.OrdinalIgnoreCase) == false && value.Equals("redeemed") == false)
                            {
                                list.TryAdd(code, url);
                                //// Console.WriteLine($"{code} - {url}");
                            }
                        }
                    });
                });
            }

            return list;
        }

        /// <summary>
        /// Get the cell's value
        /// </summary>
        /// <param name="workbookPart">The <see cref="WorkbookPart"/></param>
        /// <param name="x">The <see cref="Cell"/></param>
        /// <returns>The string contents of the cell</returns>
        private static string GetCell(WorkbookPart workbookPart, Cell x)
        {
            var value = x.InnerText;

            // If the cell represents an integer number, you are done. 
            // For dates, this code returns the serialized value that 
            // represents the date. The code handles strings and 
            // Booleans individually. For shared strings, the code 
            // looks up the corresponding value in the shared string 
            // table. For Booleans, the code converts the value into 
            // the words TRUE or FALSE.
            if (x.DataType != null)
            {
                switch (x.DataType.Value)
                {
                    case CellValues.SharedString:

                        // For shared strings, look up the value in the
                        // shared strings table.
                        var stringTable = workbookPart.GetPartsOfType<SharedStringTablePart>().FirstOrDefault();

                        // If the shared string table is missing, something 
                        // is wrong. Return the index that is in
                        // the cell. Otherwise, look up the correct text in 
                        // the table.
                        if (stringTable != null)
                        {
                            value = stringTable.SharedStringTable.ElementAt(int.Parse(value)).InnerText;
                        }
                        break;

                    case CellValues.Boolean:
                        value = value switch
                        {
                            "0" => "FALSE",
                            _ => "TRUE",
                        };
                        break;
                }
            }

            return value;
        }
    }
}
