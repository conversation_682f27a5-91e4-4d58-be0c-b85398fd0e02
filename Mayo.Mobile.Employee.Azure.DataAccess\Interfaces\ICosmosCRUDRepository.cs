﻿using System;
using System.Collections.Generic;
using System.Text;

namespace Mayo.Mobile.Employee.Azure.DataAccess.Interfaces
{
    /// <summary>
    /// CLASS FOR USES WITH ONLY TESTING!!
    /// </summary>
    public interface ICosmosCRUDRepository
    {
        /// <summary>
        /// For use with Testing ONLY!!
        /// </summary>
        /// <param name="containerName"></param>
        /// <returns></returns>
        void DeleteContainer(string containerName);
    }
}
