﻿using Mayo.Mobile.Employee.Azure.DataAccess.Models;
using Mayo.Mobile.Employee.Location.Model;
using System;
using System.Collections.Generic;
using System.Text;
using System.Threading.Tasks;

namespace Mayo.Mobile.Employee.Azure.DataAccess.Interfaces
{
    public interface IWorkOrderQueueRepo
    {
        /// <summary>
        /// Used to add the FacilityIssue to the WorkOrder Processing Queue
        /// </summary>
        /// <param name="issue"></param>
        /// <returns></returns>
        public Task EnqueueWOAsync(FacilityIssue issue);

        /// <summary>
        /// Used to enqueue Attachments to the attachment Queue to be processed
        /// </summary>
        /// <param name="attachment"></param>
        /// <returns></returns>
        public Task EnqueueWOAttachmentAsync(FacilityWorkOrderAttachment attachment);

        /// <summary>
        /// Used with Test Cases to test processing an Attachment
        /// </summary>
        /// <returns></returns>
        public Task<FacilityWorkOrderAttachment> DequeueAttachmentAsync();
    }
}
