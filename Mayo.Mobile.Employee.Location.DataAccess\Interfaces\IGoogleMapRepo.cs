﻿using System;
using System.Collections.Generic;
using System.Text;
using System.Threading.Tasks;

namespace Mayo.Mobile.Employee.Location.DataAccess.Interfaces
{
    public interface IGoogleMapRepo
    {
        /// <summary>
        /// Method to convert an address to latitude and longitude coordinates
        /// </summary>
        /// <param name="address">The address to be mapped</param>
        /// <returns>The <see cref="Task{TResult}"</returns>
        public Task<(string Latitude, string Longitude)> GetLatidueAndLongitudeAsync(string address);
    }
}
