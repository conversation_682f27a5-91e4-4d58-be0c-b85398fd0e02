﻿// -----------------------------------------------------------------------
// <copyright file="MockContentService.cs" company="Mayo Clinic">
//     Copyright © Mayo Foundation. All rights reserved.
// </copyright>
// -----------------------------------------------------------------------

namespace Mayo.Mobile.Employee.Content.Model
{
    using System;
    using System.Collections.Generic;
    using System.IO;
    using System.Linq;
    using System.Net;
    using System.Net.Http;
    using System.Text;
    using System.Threading.Tasks;
    using Mayo.Mobile.Employee.Model;
    using Newtonsoft.Json.Linq;

    /// <summary>
    /// Initializes a new instance of the MockContentService class.
    /// </summary>
    public class MockContentService : IContentService
    {

        private IContentStorageRepository Repository { get; set; }
        
        public MockContentService(IContentStorageRepository repository)
        {
            this.Repository = repository;
        }

        /// <summary>
        /// Get the list of alerts
        /// </summary>
        /// <returns>The <see cref="Task{TResult}"/></returns>
        public async Task<Package> GetAlertsAsync()
        {
            await Task.Delay(0);
            return new Package { Id = DateTime.UtcNow.ToString("yyyyMMdd"), Items = new List<PackageItem>(), Type = "ALERTS" };
        }

        /// <summary>
        /// Gets the content list
        /// </summary>
        /// <param name="id">The category identifier</param>
        /// <param name="count">The number of items</param>
        /// <returns>The <see cref="Task{TResult}"/></returns>
        public async Task<List<Package>> GetListAsync(string id, int? count)
        {
            return await this.Repository.GetBlobAsync<List<Package>>("content", "publiclist.json");
        }

        /// <summary>
        /// Get the content
        /// </summary>
        /// <param name="id">The content identifier</param>
        /// <param name="formatted">A value indicating whether the content should be formatted</param>
        /// <returns>The <see cref="Task{TResult}"/></returns>
        public async Task<Content> GetContentAsync(string id, bool formatted = true)
        {
            return await this.Repository.GetBlobAsync<Content>("content", id);
        }

        /// <summary>
        /// Get the image
        /// </summary>
        /// <param name="name">The name of the image</param>
        /// <returns>The <see cref="Task{TResult}"/></returns>
        public async Task<Stream> GetImageAsync(string name)
        {
            return await this.Repository.ImageAsync(name);
        }

        /// <summary>
        /// Gets the favorites list
        /// </summary>
        /// <param name="userId">The user identifier</param>
        /// <param name="count">The number of items</param>
        /// <returns>The <see cref="Task{TResult}"/></returns>
        public async Task<List<Package>> GetFavoritesAsync(string userId, int? count)
        {
            var items = await this.Repository.GetBlobAsync<List<PackageItem>>("user", $"{userId}/favorites/content.json");

            return new List<Package>
                {
                    new Package
                    {
                        Id = DateTime.UtcNow.ToString("yyyyMMdd"),
                            Items = items,
                            Type = "FAVORITES"
                    }
            };
        }

        /// <summary>
        /// Add to the favorites list
        /// </summary>
        /// <param name="userId">The user identifier</param>
        /// <param name="categoryId">The category identifier</param>
        /// <param name="id">The content identifier</param>
        /// <returns>The <see cref="Task"/></returns>
        public async Task AddFavoriteAsync(string userId, string categoryId, string id)
        {
            await Task.Delay(0);
        }

        /// <summary>
        /// Delete from the favorites list
        /// </summary>
        /// <param name="userId">The user identifier</param>
        /// <param name="categoryId">The category identifier</param>
        /// <param name="id">The content identifier</param>
        /// <returns>The <see cref="Task"/></returns>
        public async Task DeleteFavoriteAsync(string userId, string categoryId, string id)
        {
            await Task.Delay(0);
        }

        /// <summary>
        /// Gets the list of comments
        /// </summary>
        /// <param name="id">The content identifier</param>
        /// <param name="pageNumber">The page number for paging if provided</param>
        /// <returns>The <see cref="Task{TResult}"/></returns>
        public async Task<List<Comment>> CommentsAsync(string id, string pageNumber = null)
        {
            await Task.Delay(0);
            return new List<Comment>();
        }

        /// <summary>
        /// Add a comment to the thread
        /// </summary>
        /// <param name="contentId">The content identifier</param>
        /// <param name="threadId">The thread identifier</param>
        /// <param name="userId">The user identifier</param>
        /// <param name="comment">The plain text comment</param>
        /// <param name="replyId">The identifier of the comment to reply to</param>
        /// <returns>The <see cref="Task"/></returns>
        public async Task PostCommentAsync(string contentId, string threadId, string userId, string comment, string replyId = null)
        {
            await Task.Delay(0);
        }
    }
}