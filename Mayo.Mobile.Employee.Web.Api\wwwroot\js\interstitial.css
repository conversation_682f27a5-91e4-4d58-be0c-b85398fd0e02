
/*Button Treatments*/
.btn-primary {
    margin-top: 1.2em;
    background-color: #0057B8;
    width: 320px;
    color: white;
    font-weight: bold;
    font-size: 12px;
    height: 36px;
}

.btn-primary-mobile {
    margin-top: 1.2em;
    background-color: #0057B8;
    width: 320px;
    color: white;
    font-weight: bold;
    font-size: 12px;
    height: 36px;
}

/* Content treatment*/
@media only screen and (min-width: 650px) {
    #content {
        margin-top: 1.2em;
    }
}

@media only screen and (max-device-width: 768px) {
    #content {
        display: initial;
        margin: 0 !important;
    }

    .content-height {
        height: 320px;
    }

    .card-border {
        border-radius: 0 !important;
    }

    div.content-title {
        font-size: 12px;
    }
}

@media only screen and (max-width: 650px) {
    #content {
        display: initial;
        margin: 0 !important;
    }

    .content-height {
        height: 320px;
    }

    .card-border {
        border-radius: 0 !important;
    }

    div.content-title {
        font-size: 24px;
    }
}

@media only screen and (max-width: 650px) {
    #content {
        display: initial;
        margin: 0 !important;
    }

    .content-height {
        height: 320px;
    }

    .card-border {
        border-radius: 0 !important;
    }

    div.content-title {
        font-size: 22px;
    }
}

/*
@media only screen and (min-device-width: 414px) 
    and (max-device-width: 736px) 
    and (-webkit-min-device-pixel-ratio: 3) {

        div.content-title {
            font-size: 22px;
        }
}

@media only screen and (min-device-width: 375px) and (max-device-width: 812px) and (-webkit-min-device-pixel-ratio: 3) {

    div.content-title {
        font-size: 18px;
    }

    div.content-category {
        font-size: 10px;
    }
}
*/

@media (min-width: 650px) and (max-width: 991.98px) {
    #content {
        display: inline-flex;
        height: 100%;
    }

    .content-height {
        height: 440px;
    }

    .marketing-margin {
        margin-top: 3.2em;
    }
}

@media only screen and (min-width: 992px) {
    #content {
        height: 100%;
        width: 100%;
        margin-top: 1.2em;
        margin-right: 1.2em;
    }

    .content-height {
        height: 440px;
    }

    .marketing-margin {
        margin-left: 3.2em;
    }
}

img.rounded {
    object-fit: unset;
    border-radius: 50%;
    height: 100px;
    width: 100px;
}

.card-border {
}

#content {
    max-width: 650px;
}

.container-lg {
    align-self: center;
    align-items: center;
}


/*Provides the "Daily" style treatment of the content image, title and category for desktop*/

.content-height {
}

.card-border {
}

.content-gradient {
    content: '';
    position: absolute;
    left: 0;
    right: 0;
    bottom: 0;
    top: 0;
    width: 100%;
    height: 100%;
    display: inline-block;
    background: linear-gradient(to bottom, rgba(0,0,0,0) 0%,rgba(0,0,0,1) 100%);
    opacity: .8;
}

.content-title {
    color: white;
    text-align: start;
    font-size: 28px;
    font-weight: normal;
    font-family: Arial, Helvetica, sans-serif;
    margin-bottom: 10px;
    line-height: 26px;
}

.content-category {
    font-size: 16px;
    font-family: Arial, Helvetica, sans-serif;
    margin-bottom: 0px;
    margin-top: 0px;
    line-height: 12px;
}

/*Mayo Clinic app marketing and download*/
#marketing {
    padding: 3em 0 3em 0;
    font-family: Arial, Helvetica, sans-serif;
    font-size: 16px;
    font-weight: normal;
}

.marketing-margin {
    margin-bottom: 1.2em;
}

.marketing-title {
    color: #0057B8;
    font-size: 18px;
    font-weight: bold;
    margin-top: 1.0em;
    margin-bottom: 1.0em;
}

.marketing-icons {
    padding: 1.5em;
    margin-top: 1.2em;
    margin-bottom: 1.2em;
}

.name-title {
    margin-top: 0.5em;
    color: black;
    font-size: 14px;
    font-weight: bold;
}

.name-id {
    color: black;
    font-size: 12px;
}

.code-id {
    color: black;
    font-size: 24px;
    font-weight: bold;
}

.code-url {
    color: black;
    font-size: 12px;
    color: #0057B8;
    font-weight: bold;
}

/* Category-specific selectors */


.today {
    color: white;
    font-size: 16px;
    font-weight: bold;
    vertical-align: middle;
}

.today-image {
    border-bottom: 4px solid #0057B8;
}

.nopadding {
    padding: 0 !important;
    margin: 0 !important;
}


.image-cropper {
    max-width: 100%;
    height: auto;
    position: relative;
    overflow: hidden;
}

.image-rounded {
    display: block;
    margin: 0 auto;
    height: 100px;
    width: 100px;
    -webkit-border-radius: 50%;
    -moz-border-radius: 50%;
    -ms-border-radius: 50%;
    -o-border-radius: 50%;
    border-radius: 50%;
    background-size: cover;
}


.div-empty {
    line-height: 10px;
}