﻿//-----------------------------------------------------------------------
// <copyright file="Department.cs" company="Mayo Foundation">
//     Copyright © Mayo Foundation. All rights reserved.
// </copyright>
//-----------------------------------------------------------------------

namespace Mayo.Mobile.Employee.Model
{
    using System;
    using System.Collections.Generic;
    using System.Linq;
    using System.Runtime.Serialization;
    using System.Text;

    /// <summary>
    /// Initializes a new instance of the Department class.
    /// </summary>
    [DataContract(Namespace = "http://www.mayo.edu/MayoMobile")]
    public class Department : Mayo.Mobile.Staff.Model.Department
    {
        /// <summary>
        /// Gets or sets the <see cref="Location"/>
        /// </summary>
        [DataMember]
        public Location Location { get; set; }

        /// <summary>
        /// Gets or sets the support contact information
        /// </summary>
        [DataMember]
        public List<Support> Supports { get; set; }
    }
}
