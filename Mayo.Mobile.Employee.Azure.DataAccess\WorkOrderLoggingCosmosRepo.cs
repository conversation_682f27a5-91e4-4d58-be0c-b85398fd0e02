﻿using Mayo.Mobile.Application.Azure.DataAccess.Models;
using Mayo.Mobile.Employee.Azure.DataAccess.Interfaces;
using Mayo.Mobile.Employee.Azure.DataAccess.Models;
using Mayo.Mobile.Employee.Location.Model;
using Microsoft.Azure.Cosmos;
using Microsoft.Azure.Cosmos.Linq;
using Microsoft.Extensions.Options;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Mayo.Mobile.Employee.Azure.DataAccess
{
    public class WorkOrderLoggingCosmosRepo: IWorkOrderLoggingRepo
    {
        private CosmosClient cosmosClient;
        private Database database;
        private Container facilitiesContainer;

        public WorkOrderLoggingCosmosRepo(IOptions<CosmosRepoOptions> cosmosRepoOptions, IOptions<WorkOrderCosmosOptions> configurationOptions)
        {
            var clientOptions = new CosmosClientOptions()
            {
                SerializerOptions = new CosmosSerializationOptions()
                {
                    IgnoreNullValues = true
                },
                ConnectionMode = ConnectionMode.Gateway
            };
            cosmosClient = new CosmosClient(cosmosRepoOptions.Value.ConnectionString, clientOptions);
            database = cosmosClient.GetDatabase(configurationOptions.Value.CosmosDatabaseName);
            facilitiesContainer = database.GetContainer(configurationOptions.Value.CosmosContainerName);
        }

        /// <summary>
        /// Gets or Creates the WorkOrder Document 
        /// </summary>
        /// <param name="issue"><see cref="FacilityIssue"></param>
        /// <returns></returns>
        public async Task<FacilityWorkOrderDocument> GetCreateWorkOrderDocumentAsync(FacilityWorkOrderQueueMessage issue)
        {
            try
            {
                var partitionKey = new PartitionKey($"{issue.CampusId}{issue.BuildingId}{issue.FloorId}{issue.RoomId}");
                return await facilitiesContainer.ReadItemAsync<FacilityWorkOrderDocument>(issue.Id, partitionKey);
            }
            catch(CosmosException exception)
            {
                if(exception.StatusCode == System.Net.HttpStatusCode.NotFound)
                {
                    var workOrder = new FacilityWorkOrderDocument(issue.Id, issue.ReportedBy, issue.Comments, issue.CampusId, issue.BuildingId, issue.FloorId, issue.RoomId);
                    workOrder.PhotoIds = issue.PhotoIds;
                    return await facilitiesContainer.CreateItemAsync(workOrder);
                }

                // Something else errored throw the exception
                throw exception;
            }
        }

        /// <summary>
        /// Returns the WorkOrderDocument
        /// </summary>
        /// <param name="documentId">Id of the document in the documentDB</param>
        /// <param name="partitionKey">Key to find the document in the documentDB</param>
        /// <returns></returns>
        public async Task<FacilityWorkOrderDocument> GetWorkOrderDocumentAsync(string documentId, string partitionKey)
        {
            return await facilitiesContainer.ReadItemAsync<FacilityWorkOrderDocument>(documentId, new PartitionKey(partitionKey));
        }

        /// <summary>
        /// Used to update a FacilityWorkOrderDocument in our database
        /// </summary>
        /// <param name="workOrder"></param>
        /// <returns></returns>
        public async Task<FacilityWorkOrderDocument> UpdateWorkOrderDocumentAsync(FacilityWorkOrderDocument workOrder)
        {
            return await facilitiesContainer.UpsertItemAsync(workOrder);
        }
    }
}
