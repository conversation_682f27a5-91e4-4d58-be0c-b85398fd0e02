﻿//-----------------------------------------------------------------------
// <copyright file="DirectoryStorageRepository.cs" company="Mayo Foundation">
//     Copyright © Mayo Foundation. All rights reserved.
// </copyright>
//-----------------------------------------------------------------------

namespace Mayo.Mobile.Employee.Directory.Model
{
    using System;
    using System.Collections.Concurrent;
    using System.Collections.Generic;
    using System.IO;
    using System.Linq;
    using System.Runtime.Serialization.Json;
    using System.Threading;
    using System.Threading.Tasks;
    using Mayo.Mobile.Application.Azure.DataAccess.Interfaces;
    using Mayo.Mobile.Employee.Model;
    using Microsoft.Azure.Cosmos.Table;

    /// <summary>
    /// Initializes a new instance of the DirectoryStorageRepository class.
    /// </summary>
    public class DirectoryStorageRepository : IDirectoryStorageRepository
    {
        private ITableStorageRepository TableStorageRepo;
        private IBlobStorageRepository BlobStorageRepo;
        /// <summary>
        /// Initializes a new instance of the DirectoryStorageRepository class.
        /// </summary>
        /// <param name="connectionString">The connection string</param>
        public DirectoryStorageRepository(ITableStorageRepository tableStorageRepo, IBlobStorageRepository blobStorageRepo)
        {
            TableStorageRepo = tableStorageRepo;
            BlobStorageRepo = blobStorageRepo;
        }

        /// <summary>
        /// Get the favorited items for a person
        /// </summary>
        /// <param name="userId">The user identifier</param>
        /// <returns>The <see cref="Task{TResult}"/></returns>
        public async Task<List<Person>> FavoritesAsync(string userId)
        {

            var list = await BlobStorageRepo.GetBlobAsync<List<Person>>("user", $"{userId}/favorites/people.json");
            
            // create the emplty Favorites so we quit getting tons of not found errors
            if(list == null)
            {
                list = new List<Person>();
                await SaveFavoritesAsync(userId, list);
            }


            ConcurrentDictionary<string, Status> dictionary = new ConcurrentDictionary<string, Status>();

            SemaphoreSlim throttler = new SemaphoreSlim(System.Environment.ProcessorCount, System.Environment.ProcessorCount);

            await Task.WhenAll(list.Select(async x =>
            {
                await throttler.WaitAsync();
                try
                {
                    var s = await StatusAsync(x.Id);
                    dictionary.TryAdd(x.Id, s);
                }
                finally
                {
                    throttler.Release();
                }
            }));

            list.All(x => 
            {
                dictionary.TryGetValue(x.Id, out Status s);
                x.Status = s ?? new Status();
                return true;
            });

            return list;
        }

        /// <summary>
        /// Save the favorites for a person
        /// </summary>
        /// <param name="userId">The user identifier</param>
        /// <param name="list">The list of <see cref="Person"/></param>
        /// <returns>The <see cref="Task{TResult}"/></returns>
        public async Task SaveFavoritesAsync(string userId, List<Person> list)
        {
            await SaveAsync<List<Person>>("user", $"{userId}/favorites/people.json", list);
        }

        /// <summary>
        /// Get the status
        /// </summary>
        /// <param name="userId">The user identifier</param>
        /// <returns>The <see cref="Task{TResult}"/></returns>
        public async Task<Status> StatusAsync(string userId)
        {
            var d = await TableStorageRepo.GetEntityAsync("UserStorage", userId, "STATUS");
            if (d == null)
            {
                return new Status();
            }
            else
            {
                var s = new Status
                {
                    PersonId = userId,
                    Description = GetStringValue(d.Properties, "Description"),
                    Emoji = GetStringValue(d.Properties, "Emoji"),
                    Id = GetStringValue(d.Properties, "Id"),
                    EndDate = GetStringValue(d.Properties, "EndDate"),
                    IsActive = GetBoolValue(d.Properties, "IsActive"),
                    Name = GetStringValue(d.Properties, "Name"),
                    StartDate = GetStringValue(d.Properties, "StartDate")
                };

                if (s.IsActive == false)
                {
                    s = new Status();
                }

                if (string.IsNullOrEmpty(s.StartDate) == false)
                {
                    var date = DateTimeOffset.Parse(s.StartDate);
                    s = (date.UtcDateTime.CompareTo(DateTime.UtcNow) < 0) ? s : new Status();
                }

                if (string.IsNullOrEmpty(s.EndDate) == false)
                {
                    var date = DateTimeOffset.Parse(s.EndDate);
                    s = (date.UtcDateTime.CompareTo(DateTime.UtcNow) > 0) ? s : new Status();
                }

                return s;
            }
        }

        /// <summary>
        /// Save the blob block to the container 
        /// </summary>
        /// <typeparam name="T">The type of the object</typeparam>
        /// <param name="containerName">The blob container</param>
        /// <param name="name">The name of the blob block</param>
        /// <param name="content">The contents of the blob</param>
        public virtual async Task SaveAsync<T>(string containerName, string name, T content)
        {
            MemoryStream stream = null;
            try
            {
                stream = Serialize(content);
                await BlobStorageRepo.SaveAsync(containerName, name, stream);
                stream = null;
            }
            finally
            {
                if (stream != null)
                {
                    stream.Dispose();
                }
            }
        }

        /// <summary>
        /// Serialize the object to JSON
        /// </summary>
        /// <typeparam name="T">The type of the object</typeparam>
        /// <param name="model">The object</param>
        /// <returns>The JSON string</returns>
        protected virtual MemoryStream Serialize<T>(T model)
        {
            MemoryStream stream = new MemoryStream();
            DataContractJsonSerializer ser = new DataContractJsonSerializer(typeof(T));
            ser.WriteObject(stream, model);
            stream.Position = 0;
            return stream;
        }

        /// <summary>
        /// Gets the string value from the dictionary given a key
        /// </summary>
        /// <param name="dictionary">The <see cref="IDictionary{TKey, TValue}"/></param>
        /// <param name="key">The key</param>
        /// <returns>The value</returns>
        protected string GetStringValue(IDictionary<string, EntityProperty> dictionary, string key)
        {
            return dictionary.TryGetValue(key, out EntityProperty value) ? value.StringValue : string.Empty;
        }

        /// <summary>
        /// Gets the bool value from the dictionary given a key
        /// </summary>
        /// <param name="dictionary">The <see cref="IDictionary{TKey, TValue}"/></param>
        /// <param name="key">The key</param>
        /// <returns>The value</returns>
        protected bool GetBoolValue(IDictionary<string, EntityProperty> dictionary, string key)
        {
            return dictionary.TryGetValue(key, out EntityProperty value)
                ? value.BooleanValue ?? false
                : false;
        }
    }
}