﻿//-----------------------------------------------------------------------
// <copyright file="IdentityResponse.cs" company="Mayo Foundation">
//     Copyright © Mayo Foundation. All rights reserved.
// </copyright>
//-----------------------------------------------------------------------

namespace Mayo.Mobile.Employee.Web.Api.Model
{
    using Mayo.Mobile.Employee.Model;
    using System;
    using System.Collections.Generic;
    using System.Linq;
    using System.Runtime.Serialization;
    using System.Text;

    /// <summary>
    /// Initializes a new instance of the IdentityResponse class.
    /// </summary>
    [DataContract(Namespace = "http://www.mayo.edu/MayoMobile")]
    public class IdentityResponse
    {
        public IdentityResponse(Identity identity)
        {
            this.Identity = identity;
        }

        /// <summary>
        /// Gets or sets the <see cref="Identity"/>
        /// </summary>
        [DataMember]
        public Identity Identity { get; set; }
    }
}
