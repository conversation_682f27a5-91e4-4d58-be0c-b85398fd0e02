﻿//-----------------------------------------------------------------------
// <copyright file="DailyMayoRepository.cs" company="Mayo Foundation">
//     Copyright © Mayo Foundation. All rights reserved.
// </copyright>
//-----------------------------------------------------------------------

namespace Mayo.Mobile.Employee.Content.Model
{
    using System;
    using System.Collections.Generic;
    using System.Globalization;
    using System.Linq;
    using System.Runtime.Serialization;
    using System.Text;
    using Mayo.Mobile.Employee.Model;

    /// <summary>
    /// Initializes a new instance of the Content class.
    /// </summary>
    [DataContract(Namespace = "http://www.mayo.edu/MayoMobile")]
    public class Content
    {
        /// <summary>
        /// The identifier of the content
        /// </summary>
        private string id = string.Empty;

        /// <summary>
        /// Gets or sets the content identifier
        /// </summary>
        [DataMember(Name = "Id")]
        public string Id
        {
            get
            {
                this.id = string.IsNullOrEmpty(this.id) ? string.Format("package_{0}", DateTime.UtcNow.ToString("yyyyMMddHHmmss")) : this.id;
                return this.id;
            }

            set
            {
                this.id = value;
            }
        }

        /// <summary>
        /// Gets or sets the date the content was posted
        /// </summary>
        [IgnoreDataMember]
        public DateTime Date
        {
            get
            {
                return string.IsNullOrEmpty(this.FormattedDate) ? DateTime.UtcNow : DateTime.ParseExact(this.FormattedDate, "yyyy-MM-dd", CultureInfo.InvariantCulture);
            }

            set
            {
                this.FormattedDate = string.IsNullOrEmpty(this.FormattedDate) ? DateTime.UtcNow.ToString("yyyy-MM-dd") : value.ToString("yyyy-MM-dd");
            }
        }

        /// <summary>
        /// Gets or sets an image for the content
        /// </summary>
        //// [DataMember(Name = "Image")]
        [IgnoreDataMember]
        public string Image { get; set; }

        /// <summary>
        /// Gets or sets an image identifier for the content
        /// </summary>
        [DataMember]
        public string ImageId { get; set; }

        /// <summary>
        /// Gets or sets the content
        /// </summary>
        [DataMember(Name = "Page")]
        public Page Page { get; set; }

        /// <summary>
        /// Gets or sets the date time
        /// This can be private because it's only ever accessed by the serializer.
        /// </summary>
        [DataMember(Name = "Date")]
        private string FormattedDate { get; set; }

        /// <summary>
        /// Gets or sets the categories the content is available in
        /// </summary>
        [DataMember(Name = "Categories")]
        public List<Category> Categories { get; set; }
    }
}
