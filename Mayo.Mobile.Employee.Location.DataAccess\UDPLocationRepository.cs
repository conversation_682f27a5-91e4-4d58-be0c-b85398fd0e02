﻿//-----------------------------------------------------------------------
// <copyright file="LocationRepository.cs" company="Mayo Foundation">
//     Copyright © Mayo Foundation. All rights reserved.
// </copyright>
//-----------------------------------------------------------------------

namespace Mayo.Mobile.Employee.Location.DataAccess
{
    using System;
    using System.Collections.Generic;
    using System.Data;
    using System.Linq;
    using System.Threading.Tasks;
    using IBM.Data.DB2.Core;
    using Mayo.Mobile.Employee.Location.DataAccess.Interfaces;
    using Mayo.Mobile.Employee.Location.DataAccess.Models;
    using Microsoft.Extensions.Options;

    /// <summary>
    /// Initializes a new instance of the LocationRepository class.
    /// </summary>
    public class UDPLocationRepository : IUDPLocationRepo
    {
        private string connectionString;

        public UDPLocationRepository(IOptions<UDPOptions> configurationOptions)
        {
            connectionString = configurationOptions.Value.UPDConnectionString;
        }

        /// <summary>
        /// Constructor for Testing Purposes
        /// Initializes a new instance of the LocationRepository class.
        /// </summary>
        /// <param name="connectionString">The connection string</param>
        public UDPLocationRepository(UDPOptions configurationOptions)
        {
            connectionString = configurationOptions.UPDConnectionString;
        }

        /// <summary>
        /// Query the database
        /// </summary>
        /// <param name="id"></param>
        /// <returns>The <see cref="Task{TResult}"/></returns>
        public async Task<List<UDPLocationTableRow>> GetCampusDataAsync()
        {
            var table = new DataTable();
            var connection = new DB2Connection(connectionString);
            if (connection.State != ConnectionState.Open)
            {
                await connection.OpenAsync();
            }

            try
            {
                using (DB2Command cmd = connection.CreateCommand())
                {
                    cmd.CommandText = "SELECT * FROM MDMVW_MAPPS_ALL_LOCS";
                    //// assuming seconds
                    cmd.CommandTimeout = 240;

                    using (var reader = await cmd.ExecuteReaderAsync())
                    {
                        table.BeginLoadData();
                        table.Load(reader);
                        table.EndLoadData();
                    }
                }

                var list =  (from x in table.AsEnumerable()
                            select new UDPLocationTableRow()
                            {
                                LocationId = x.Field<string>("LOC_ID"),
                                SpaceTypeDescription = x.Field<string>("SPACE_TYPE_DESC"),
                                CampusCode = x.Field<string>("CAMPUS_CODE"),
                                CampusCodeDescription = x.Field<string>("CAMPUS_CODE_DESC"),
                                BuildingCode = x.Field<string>("BUILDING_CODE"),
                                BuildingCodeDescription = x.Field<string>("BUILDING_CODE_DESC"),
                                FloorCode = x.Field<string>("FLOOR_CODE"),
                                FloorCodeDescription = x.Field<string>("FLOOR_CODE_DESC"),
                                RoomCode = x.Field<string>("ROOM_CODE"),
                                LocationDescription = x.Field<string>("LOC_DESC"),
                                AddressLine1 = x.Field<string>("ADDR_LINE_1"),
                                AddressLine2 = x.Field<string>("ADDR_LINE_2"),
                                AddressLine3 = x.Field<string>("ADDR_LINE_3"),
                                CityCode = x.Field<string>("CITY"),
                                StateCode = x.Field<string>("ST_CD"),
                                PostalCode = x.Field<string>("PSTAL_CD"),
                                County = x.Field<string>("CNTY")
                            }).ToList();

                return list;

            }
            finally
            {
                if (table != null)
                {
                    table.Dispose();
                }

                if (connection != null)
                {
                    connection.Close();
                    connection.Dispose();
                }
            }
        }

        /// <summary>
        /// Query the database
        /// </summary>
        /// <param name="query"></param>
        /// <returns>The <see cref="Task{TResult}"/></returns>
        public async Task<DataTable> ExecuteDataTableQueryAsync(string query)
        {
            var table = new DataTable();
            var connection = new DB2Connection(connectionString);
            if (connection.State != ConnectionState.Open)
            {
                await connection.OpenAsync();
            }

            try
            {
                using (DB2Command cmd = connection.CreateCommand())
                {
                    cmd.CommandText = query;
                    //// assuming seconds
                    cmd.CommandTimeout = 240;

                    using (var reader = await cmd.ExecuteReaderAsync())
                    {
                        table.Load(reader);
                    }
                }

                return table;
            }
            finally
            {
                if (connection != null)
                {
                    connection.Close();
                    connection.Dispose();
                }
            }
        }
    }
}
