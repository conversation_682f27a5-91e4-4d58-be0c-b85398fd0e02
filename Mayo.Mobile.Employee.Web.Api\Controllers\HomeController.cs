﻿//-----------------------------------------------------------------------
// <copyright file="HomeController.cs" company="Mayo Foundation">
//     Copyright © Mayo Foundation. All rights reserved.
// </copyright>
//-----------------------------------------------------------------------

namespace Mayo.Mobile.Employee.Web.Api.Controllers
{
    using System;
    using System.Collections.Generic;
    using System.IO;
    using System.Linq;
    using System.Net.Http;
    using System.Net.Http.Headers;
    using System.Security.Claims;
    using System.Security.Principal;
    using System.Threading.Tasks;
    using Mayo.Mobile.Employee.Application.Model;
    using Mayo.Mobile.Employee.Person.Model;
    using Mayo.Mobile.Employee.RedemptionCodes.Model;
    using Mayo.Mobile.Employee.Web.Api.Model;
    using Mayo.Mobile.Logging.Model;
    using Microsoft.AspNetCore.Authentication.AzureAD.UI;
    using Microsoft.AspNetCore.Authorization;
    using Microsoft.AspNetCore.Http;
    using Microsoft.AspNetCore.Mvc;

    /// <summary>
    /// Initializes a new instance of the HomeController class.
    /// </summary>
    [Route("")]
    public class HomeController : Controller
    {
        private IPersonService PersonService;

        private ICodeRepository CodeRepo;

        public HomeController(IPersonService service, ICodeRepository repository)
        {
            PersonService = service;
            CodeRepo = repository;
        }

        /// <summary>
        /// The un-authenticated web view
        /// </summary>
        /// <returns>The <see cref="IActionResult"/></returns>
        [HttpGet]
        public IActionResult Index()
        {
            return View();
        }

        /// <summary>
        /// The authenticated web view to redeem a code
        /// </summary>
        /// <returns>The <see cref="Task{TResult}"/></returns>
        [HttpGet, Route("/redeem")]
        public async Task<IActionResult> RedeemAsync([FromServices] IUpdateService updateService)
        {
            var appStoreUrl = updateService.GetAppStoreUrl(string.Empty, this.Request.GetUserAgent());
            return Redirect(appStoreUrl.Equals(updateService.PlayStoreUrl) ? updateService.PlayStoreUrl : updateService.AppStoreUrl);
        }

        /// <summary>
        /// The picture of the person
        /// </summary>
        /// <param name="id">The image identifier</param>
        /// <returns>The <see cref="Task{TResult}"/></returns>
        [Authorize(Policy = "AzureAd")]
        [HttpGet, Route("/photo/{id}")]
        public async Task<IActionResult> PhotoAsync(string id)
        {
            var pic = await PersonService.GetPhotoAsync(id);
            return File(new MemoryStream(Convert.FromBase64String(pic)), "image/jpeg");
        }

        /// <summary>
        /// Redeem a code
        /// </summary>
        /// <param name="p">The <see cref="PersonViewModel"/></param>
        /// <returns></returns>
        [HttpPost, Route("/redeem")]
        public async Task<IActionResult> RedeemAsync(PersonViewModel p, [FromServices] IUpdateService updateService)
        {
            var appStoreUrl = updateService.GetAppStoreUrl(string.Empty, this.Request.GetUserAgent());
            return Redirect(appStoreUrl.Equals(updateService.PlayStoreUrl) ? updateService.PlayStoreUrl : updateService.AppStoreUrl);
        }

        /// <summary>
        /// Redirect to app store for a redemption code
        /// </summary>
        /// <param name="code">The redemption code</param>
        /// <param name="updateService">The <see cref="IUpdateService"/></param>
        /// <returns>The <see cref="Task{TResult}"/></returns>
        [HttpGet, Route("/code/{code}")]
        public async Task<IActionResult> CodeAsync(string code, [FromServices] IUpdateService updateService)
        {
            var url = await CodeRepo.GetUrlAsync(code);
            if (string.IsNullOrEmpty(url.UserId))
            {
                return View("CodeRedemptionError");
            }
            else
            {
                var appStoreUrl = updateService.GetAppStoreUrl(string.Empty, Request.GetUserAgent());
                return Redirect(appStoreUrl.Equals(updateService.PlayStoreUrl) ? updateService.PlayStoreUrl : url.Url);
            }
        }
    }
}