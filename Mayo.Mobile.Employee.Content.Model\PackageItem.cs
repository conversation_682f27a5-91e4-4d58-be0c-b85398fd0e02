﻿//-----------------------------------------------------------------------
// <copyright file="PackageItem.cs" company="Mayo Foundation">
//     Copyright © Mayo Foundation. All rights reserved.
// </copyright>
//-----------------------------------------------------------------------

namespace Mayo.Mobile.Employee.Content.Model
{
    using System;
    using System.Collections.Generic;
    using System.Globalization;
    using System.Linq;
    using System.Runtime.Serialization;
    using System.Text;
    using Mayo.Mobile.Employee.Model;

    /// <summary>
    /// Initializes a new instance of the PackageItem class.
    /// </summary>
    [DataContract(Namespace = "http://www.mayo.edu/MayoMobile")]
    public class PackageItem : Item
    {
        /// <summary>
        /// Gets or sets an image for the content
        /// </summary>
        [DataMember(Name = "Images")]
        public List<Element> Images { get; set; }

        /// <summary>
        /// Gets or sets a value indicating that the content was manually scheduled
        /// </summary>
        [DataMember]
        public bool IsScheduled { get; set; }

        /// <summary>
        /// Gets or sets the author
        /// </summary>
        [DataMember]
        public Person Author { get; set; }

        /// <summary>
        /// Gets or sets the time it takes to read/view the content
        /// </summary>
        [DataMember]
        public string ReadTime { get; set; }

        /// <summary>
        /// Gets or sets sortable date time
        /// </summary>
        [DataMember]
        public string SortDate { get; set; }

        /// <summary>
        /// Gets or sets the url
        /// </summary>
        [DataMember]
        public string Url { get; set; }
    }
}
