﻿//-----------------------------------------------------------------------
// <copyright file="Campus.cs" company="Mayo Foundation">
//     Copyright © Mayo Foundation. All rights reserved.
// </copyright>
//-----------------------------------------------------------------------

namespace Mayo.Mobile.Employee.Location.Model
{
    using Newtonsoft.Json;
    using System.Collections.Generic;

    /// <summary>
    /// Initializes a new instance of the Campus class.
    /// </summary>
    public class Campus : FacilityLocation
    {
        public Campus() : base() { }

        /// <summary>
        /// Gets or sets the list of buildings
        /// </summary>
        [JsonIgnore]
        public List<Building> Buildings { get; set; }
    }

    public class CampusComparer: IComparer<Campus>
    {
        public int Compare(Campus campus1, Campus campus2)
        {
            return campus1.Name.CompareTo(campus2.Name);
        }
    }
}
