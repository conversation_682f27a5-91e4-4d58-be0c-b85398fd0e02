using System.Collections.Generic;
using System.IO;
using System.Threading;
using System.Threading.Tasks;
using Mayo.Mobile.Employee.Content.News.Model.Interfaces;
using Mayo.Mobile.Employee.Search.Model;
using Microsoft.Azure.WebJobs;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;

namespace Mayo.Mobile.Employee.Functions
{
    public class ContentFunctions
    {
        private INewsContentService NewsContentservice;
        private ISearchAdminService SearchSvc;

        public ContentFunctions(INewsContentService newsContentService, ISearchAdminService searchService)
        {
            NewsContentservice = newsContentService;
            SearchSvc = searchService;

        }

        /// <summary>
        /// The timer triggered function to pull news content
        /// </summary>
        /// <param name="timerInfo">The <see cref="TimerInfo"/></param>
        /// <param name="token">The <see cref="CancellationToken"/></param>
        [Singleton]
        [FunctionName("NewsTimerFunctionAsync")]
        public async Task NewsTimerFunctionAsync([TimerTrigger("0 0 10-23 * * *", RunOnStartup = false)] TimerInfo timerInfo, CancellationToken token)
        {
            await NewsContentservice.SaveAsync();
        }

        /// <summary>
        /// The timer triggered function to pull the news content wrapper
        /// </summary>
        /// <param name="timerInfo">The <see cref="TimerInfo"/></param>
        /// <param name="token">The <see cref="CancellationToken"/></param>
        [Singleton]
        [FunctionName("NewsWrapperTimerFunctionAsync")]
        public async Task NewsWrapperTimerFunctionAsync([TimerTrigger("0 0 0 * * Mon", RunOnStartup = false)] TimerInfo timerInfo, CancellationToken token)
        {
            await NewsContentservice.SaveWrapperAsync();
        }

        /// <summary>
        /// Handle the content blob being saved
        /// </summary>
        /// <param name="blob">The blob <see cref="CloudBlockBlob"/></param>
        /// <param name="name">The name of the blob</param>
        /// <returns>The <see cref="Task"/></returns>
        [FunctionName("ProcessNewsContentSearchAsync")]
        public async Task ProcessNewsContentSearchAsync([BlobTrigger("content/{name}")] Stream blob, string name)
        {
            StreamReader reader = new StreamReader(blob);
            var json = await reader.ReadToEndAsync();
            var jsonLinq = JObject.Parse(json);
            var content = jsonLinq.ToObject<Content.Model.Content>();

            var indexes = new List<string> { "newscentercontent" };

            await SearchSvc.AddToIndexAsync(content, indexes);
        }
    }
}
