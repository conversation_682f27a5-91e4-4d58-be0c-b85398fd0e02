﻿// -----------------------------------------------------------------------
// <copyright file="NewsCenterResponse.cs" company="Mayo Clinic">
//     Copyright © Mayo Foundation. All rights reserved.
// </copyright>
// -----------------------------------------------------------------------

namespace Mayo.Mobile.Employee.Content.News.Model
{
    using Newtonsoft.Json;
    using System;
    using System.Collections.Generic;
    using System.Text;

    /// <summary>
    /// Initializes a new instance of the NewsCenterResponse class.
    /// </summary>
    /// <typeparam name="T">The <see cref="T"/></typeparam>
    public class NewsCenterResponse<T>
    {
        /// <summary>
        /// Gets or sets a success value
        /// </summary>
        [JsonProperty("success")]
        public int Success { get; set; }

        /// <summary>
        /// Gets or sets an error value
        /// </summary>
        [JsonProperty("error")]
        public int Error { get; set; }

        /// <summary>
        /// Gets or sets a list of error messages
        /// </summary>
        [JsonProperty("error_message")]
        public List<string> ErrorMessage { get; set; }

        /// <summary>
        /// Gets or sets the data content
        /// </summary>
        [JsonProperty("content")]
        public T Content { get; set; }
    }
}
