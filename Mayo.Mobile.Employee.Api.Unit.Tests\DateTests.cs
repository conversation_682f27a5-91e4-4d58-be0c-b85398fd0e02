using Microsoft.VisualStudio.TestTools.UnitTesting;
using System;

namespace Mayo.Mobile.Employee.Api.Unit.Tests
{
    [TestClass]
    public class DateTests
    {
        [TestMethod]
        public void CompareExpiredDate_Test()
        {
            string s = "2020-01-22T10:24:34-0600";
            var d = DateTimeOffset.Parse(s);

            Assert.IsTrue(d.UtcDateTime.CompareTo(DateTime.UtcNow) < 0);
        }

        [TestMethod]
        public void CompareActiveDate_Test()
        {
            string s = "2021-01-22T10:24:34-0600";
            var d = DateTimeOffset.Parse(s);

            Assert.IsTrue(d.UtcDateTime.CompareTo(DateTime.UtcNow) >= 0);
        }

        [TestMethod]
        public void CompareDate_Test()
        {
            string s = "2020-01-23T09:49:34-0600";
            var d = DateTimeOffset.Parse(s);

            Assert.IsTrue(d.UtcDateTime.CompareTo(DateTime.UtcNow) < 0);
        }
    }
}
