﻿// -----------------------------------------------------------------------
// <copyright file="Editorial.cs" company="Mayo Clinic">
//     Copyright © Mayo Foundation. All rights reserved.
// </copyright>
// -----------------------------------------------------------------------

namespace Mayo.Mobile.Employee.Content.News.Model
{
    using System;
    using System.Collections.Generic;
    using System.Runtime.Serialization;

    /// <summary>
    /// Initializes a new instance of the Editorial class.
    /// </summary>
    [DataContract(Namespace = "http://www.mayo.edu/MayoMobile")]
    public class Editorial
    {
        // "ID": 227
        [DataMember]
        public string Id { get; set; }

        // "post_author"
        [DataMember]
        public string Author { get; set; }

        // "post_date": "2018-05-25 14:15:19"
        [DataMember]
        public string PostDate { get; set; }

        // "post_date_gmt": "2018-05-25 18:15:19"
        [DataMember]
        public string PostDateUtc { get; set; }

        // "post_content": "Content for an API test."
        [DataMember]
        public string Content { get; set; }

        /// <summary>
        /// Gets or sets the markdown text
        /// </summary>
        [DataMember]
        public string MarkDown { get; set; }

        // "post_title": "My API Post"
        [DataMember]
        public string Title { get; set; }

        // "post_excerpt": ""
        [DataMember]
        public string Excerpt { get; set; }

        // "comment_status": "open" ??
        [DataMember]
        public bool CanComment { get; set; }

        // "post_name": "my-api-post"
        [DataMember]
        public string Name { get; set; }

        // "post_modified": "2018-05-25 14:15:19"
        [DataMember]
        public string ModifiedDate { get; set; }

        // "post_modified_gmt": "2018-05-25 18:15:19"
        [DataMember]
        public string ModifiedDateUtc { get; set; }

        // "guid": "CANONICAL_URL_TO_CONTENT"
        [DataMember]
        public string Guid { get; set; }

        /// <summary>
        /// Gets or sets the featured image
        /// </summary>
        [DataMember(Name = "featured_image")]
        public string Image { get; set; }

        /// <summary>
        /// Gets or sets the tags
        /// </summary>
        [DataMember(Name = "tags")]
        public List<string> Tags { get; set; }
    }
}
