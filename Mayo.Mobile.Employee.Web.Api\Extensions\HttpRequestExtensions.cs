﻿//-----------------------------------------------------------------------
// <copyright file="HttpRequestExtensions.cs" company="Mayo Foundation">
//     Copyright © Mayo Foundation. All rights reserved.
// </copyright>
//-----------------------------------------------------------------------

namespace Microsoft.AspNetCore.Http
{
    using System;
    using System.Collections.Generic;
    using System.Linq;
    using System.Net;
    using System.Net.Http;
    using System.Net.Http.Headers;
    using System.Security.Claims;
    using System.Text;
    using System.Threading.Tasks;

    /// <summary>
    /// Extends the HttpRequest collection
    /// </summary>
    public static class HttpRequestExtensions
    {
        /// <summary>
        /// Get the access token value
        /// </summary>
        /// <param name="request">The <see cref="HttpRequestMessage"/></param>
        /// <returns>The token value</returns>
        public static string GetAccessToken(this HttpRequest request)
        {
            request.Headers.TryGetValue("HeaderAuthorization", out var token);

            return string.Join(string.Empty, token.Skip(6)).Trim();
        }

        /// <summary>
        /// Get the headers
        /// </summary>
        /// <param name="request">The <see cref="HttpRequestMessage"/></param>
        /// <returns>The headers</returns>
        public static string GetHeaders(this HttpRequest request)
        {
            return string.Join(Environment.NewLine, request.Headers.Select(x => $"{x.Key} - {x.Value}"));
        }

        /// <summary>
        /// Get the user agent in lower case
        /// </summary>
        /// <param name="request">The <see cref="HttpRequest"/></param>
        /// <returns>The user agent string in lower case</returns>
        public static string GetUserAgent(this HttpRequest request)
        {
            request.Headers.TryGetValue("User-Agent", out var useragent);
            return ((string)useragent).ToLower();
        }
    }
}