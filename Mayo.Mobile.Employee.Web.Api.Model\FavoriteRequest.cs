﻿//-----------------------------------------------------------------------
// <copyright file="FavoriteRequest.cs" company="Mayo Foundation">
//     Copyright © Mayo Foundation. All rights reserved.
// </copyright>
//-----------------------------------------------------------------------

namespace Mayo.Mobile.Employee.Web.Api.Model
{
    using System;
    using System.Collections.Generic;
    using System.Runtime.Serialization;
    using System.Text;
    using Mayo.Mobile.Employee.Content.Model;

    /// <summary>
    /// Initializes a new instance of the FavoriteRequest class.
    /// </summary>
    [DataContract(Namespace = "http://www.mayo.edu/MayoMobile")]
    public class FavoriteRequest : Request
    {
        /// <summary>
        /// Gets or sets the category identifier
        /// </summary>
        [DataMember]
        public string CategoryId { get; set; }

        /// <summary>
        /// Gets or sets the identifier
        /// </summary>
        [DataMember]
        public string Id { get; set; }
    }
}
