﻿//-----------------------------------------------------------------------
// <copyright file="Person.cs" company="Mayo Foundation">
//     Copyright © Mayo Foundation. All rights reserved.
// </copyright>
//-----------------------------------------------------------------------

namespace Mayo.Mobile.Employee.Model
{
    using System;
    using System.Collections.Generic;
    using System.Linq;
    using System.Net.Mail;
    using System.Runtime.Serialization;
    using System.Text;
    using System.Threading.Tasks;

    /// <summary>
    /// Initializes a new instance of the Person class.
    /// </summary>
    [DataContract(Namespace = "http://www.mayo.edu/MayoMobile")]
    public class Person
    {
        /// <summary>
        /// The photo base64 string
        /// </summary>
        private string photo = null;

        /// <summary>
        /// The name of the person
        /// </summary>
        private string name = null;

        /// <summary>
        /// Initializes a new instance of the Person class.
        /// </summary>
        public Person()
        {
            this.name = "Mayo Clinic Staff";
        }

        /// <summary>
        /// Initializes a new instance of the Person class.
        /// </summary>
        /// <param name="email">The email address</param>
        /// <param name="name">The persons name</param>
        public Person(string email, string name)
        {
            this.Email = email;
            this.name = name;
            this.Abbreviation = this.InitialsEmailRegex(email).ToUpper();
            this.Description = string.Empty;
        }

        /// <summary>
        /// Initializes a new instance of the Person class.
        /// </summary>
        /// <param name="email">The email address</param>
        /// <param name="name">The persons name</param>
        /// <param name="description">The description text</param>
        public Person(string email, string name, string description)
        {
            this.Email = email;
            this.name = name;
            this.Abbreviation = this.InitialsEmailRegex(email).ToUpper();
            this.Description = description;
        }

        /// <summary>
        /// Gets or sets the person identifier
        /// </summary>
        [DataMember]
        public string Id { get; set; }

        /// <summary>
        /// Gets or sets the identifier type
        /// </summary>
        [IgnoreDataMember]
        public string IdType { get; set; }

        /// <summary>
        /// Gets or sets the name
        /// </summary>
        [DataMember]
        public string Name
        {
            get
            {
                return string.IsNullOrEmpty(this.name) ? "Mayo Clinic Staff" : this.name;
            }

            set
            {
                this.name = value;
            }
        }


        /// <summary>
        /// Gets or sets the description
        /// </summary>
        [DataMember]
        public virtual string Description { get; set; }

        /// <summary>
        /// Gets or sets the photo
        /// </summary>
        [DataMember]
        public string Photo
        {
            get
            {
                return this.photo ?? string.Empty;
            }

            set
            {
                this.photo = value;
            }
        }

        /// <summary>
        /// Gets or sets the abbreviated name or abbreviated value
        /// </summary>
        [DataMember]
        public virtual string Abbreviation { get; set; }

        /// <summary>
        /// Gets or sets the department name
        /// </summary>
        [DataMember]
        public virtual string Department { get; set; }

        /// <summary>
        /// Gets or sets the location name
        /// </summary>
        [DataMember]
        public virtual string Location { get; set; }

        /// <summary>
        /// Gets or sets the url for the image
        /// </summary>
        [DataMember]
        public virtual string ImageUrl { get; set; }

        /// <summary>
        /// Gets or sets the person email
        /// </summary>
        [DataMember]
        public string Email { get; set; }

        /// <summary>
        /// Gets or sets the <see cref="Status"/>
        /// </summary>
        [DataMember]
        public virtual Status Status { get; set; }

        /// <summary>
        /// Get the initials from an email address
        /// </summary>
        /// <param name="s">The email address</param>
        /// <returns>The formatted initials</returns>
        private string InitialsEmailRegex(string s)
        {
            MailAddress address = new MailAddress(s);
            var names = address.User.Split('.');
            return names.Count() > 1
                //// Lastname.Firstname format
                ? $"{names.Last().Substring(0, 1)}{names.First().Substring(0, 1)}"
                //// FirstinitialLastname format
                : $"{new string(names.First().Take(2).ToArray())}";
        }
    }
}
