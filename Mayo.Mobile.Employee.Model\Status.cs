﻿//-----------------------------------------------------------------------
// <copyright file="Status.cs" company="Mayo Foundation">
//     Copyright © Mayo Foundation. All rights reserved.
// </copyright>
//-----------------------------------------------------------------------
namespace Mayo.Mobile.Employee.Model
{
    using System;
    using System.Collections.Generic;
    using System.Runtime.Serialization;
    using System.Text;

    /// <summary>
    /// Initializes a new instance of the Status class.
    /// </summary>
    [DataContract(Namespace = "http://www.mayo.edu/MayoMobile")]
    public class Status
    {
        /// <summary>
        /// Initializes a new instance of the Status class.
        /// </summary>
        public Status()
        {
        }

        /// <summary>
        /// Initializes a new instance of the Status class.
        /// </summary>
        /// <param name="id">The status identifier</param>
        /// <param name="name">The name</param>
        /// <param name="description">The description</param>
        /// <param name="startDate">The start date</param>
        /// <param name="endDate">The end date</param>
        /// <param name="personId">The person identifier</param>
        public Status(string id, string name, string description, string startDate = null, string endDate = null, string personId = null)
        {
            this.Id = id;
            this.Name = name;
            this.Description = description;
            this.StartDate = startDate ?? string.Empty;
            this.EndDate = endDate ?? string.Empty;
            this.IsActive = true;
            this.PersonId = personId;
        }

        /// <summary>
        /// Gets or sets the identifier
        /// </summary>
        [DataMember]
        public string Id = string.Empty;

        /// <summary>
        /// Gets or sets the name
        /// </summary>
        [DataMember]
        public string Name = string.Empty;

        /// <summary>
        /// Gets or sets the description
        /// </summary>
        [DataMember]
        public string Description = string.Empty;

        /// <summary>
        /// Gets or sets emoji as utf-8 bytes in a string
        /// </summary>
        [DataMember]
        public string Emoji = string.Empty;

        /// <summary>
        /// Gets or sets when status is set
        /// </summary>
        [DataMember]
        public string StartDate = string.Empty;

        /// <summary>
        /// Gets or sets when the status expires
        /// </summary>
        [DataMember]
        public string EndDate = string.Empty;

        /// <summary>
        /// Gets or sets whether the status is active
        /// </summary>
        [DataMember]
        public bool IsActive { get; set; }

        /// <summary>
        /// Gets or sets the person identifier
        /// </summary>
        [DataMember]
        public string PersonId { get; set; }
    }
}