<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>netcoreapp3.1</TargetFramework>
    <IsPackable>false</IsPackable>
    <Platforms>AnyCPU;x64</Platforms>
    <PlatformTarget>x64</PlatformTarget>
    <RuntimeIdentifier>win-x64</RuntimeIdentifier>
    <SelfContained>true</SelfContained>
    <UseAppHost>true</UseAppHost>
  </PropertyGroup>

  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugType></DebugType>
  </PropertyGroup>

  <PropertyGroup Condition="'$s(Configuration)|$(Platform)'=='Release|x64'">
    <DebugType />
  </PropertyGroup>
  <ItemGroup>
    <PackageReference Include="Microsoft.NET.Test.Sdk" Version="16.7.1" />
    <PackageReference Include="MSTest.TestAdapter" Version="2.1.2" />
    <PackageReference Include="MSTest.TestFramework" Version="2.1.2" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\Mayo.Mobile.Employee.Azure.Search.Model\Mayo.Mobile.Employee.Azure.Search.Model.csproj" />
    <ProjectReference Include="..\Mayo.Mobile.Employee.Content.Model\Mayo.Mobile.Employee.Content.Model.csproj" />
    <ProjectReference Include="..\Mayo.Mobile.Employee.Content.News.Model\Mayo.Mobile.Employee.Content.News.Model.csproj" />
    <ProjectReference Include="..\Mayo.Mobile.Employee.Directory.Model\Mayo.Mobile.Employee.Directory.Model.csproj" />
    <ProjectReference Include="..\Mayo.Mobile.Employee.Functions\Mayo.Mobile.Employee.Functions.csproj" />
    <ProjectReference Include="..\Mayo.Mobile.Employee.Identity.Model\Mayo.Mobile.Employee.Identity.Model.csproj" />
    <ProjectReference Include="..\Mayo.Mobile.Employee.Location.DataAccess\Mayo.Mobile.Employee.Location.DataAccess.csproj" />
    <ProjectReference Include="..\Mayo.Mobile.Employee.Model\Mayo.Mobile.Employee.Model.csproj" />
    <ProjectReference Include="..\Mayo.Mobile.Employee.Search.Model\Mayo.Mobile.Employee.Search.Model.csproj" />
    <ProjectReference Include="..\Mayo.Mobile.Employee.Web.Api\Mayo.Mobile.Employee.Web.Api.csproj" />
    <ProjectReference Include="..\Mayo.Mobile.Employee.Web.Api.Model\Mayo.Mobile.Employee.Web.Api.Model.csproj" />
  </ItemGroup>

  <ItemGroup>
    <None Remove="appsettings.json" />
  </ItemGroup>

  <ItemGroup>
    <Content Include="appsettings.json">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
  </ItemGroup>
</Project>
