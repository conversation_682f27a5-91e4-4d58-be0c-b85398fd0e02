
/*Button Treatments*/
.btn-primary {
    margin-top: 1.2em;
    background-color: #0057B8;
    width: 320px;
    color: white;
    font-weight: bold;
    font-size: 12px;
    height: 36px;
}

.btn-primary-mobile {
    margin-top: 1.2em;
    background-color: #0057B8;
    width: 320px;
    color: white;
    font-weight: bold;
    font-size: 12px;
    height: 36px;
}

/* Content treatment*/
#content {
    max-width: 640px;
}

.image-cropper {
    max-width: 100%;
    height: auto;
    position: relative;
    overflow: hidden;
}

.image-rounded {
    display: block;
    margin: 0 auto;
    height: 100px;
    width: 100px;
    -webkit-border-radius: 50%;
    -moz-border-radius: 50%;
    -ms-border-radius: 50%;
    -o-border-radius: 50%;
    border-radius: 50%;
    background-size: cover;
}

/*evaulate keeping these*/

h1 {
    font-size: 1.8em;
}

h2 {
    font-size: 1.5em;
}


/*Provides the "Daily" style treatment of the content image, title and category for desktop*/

.daily-wrap {
    position: relative;
}

.daily-wrap .overlay {
    position: absolute;
    width: 70px;
    height: 70px;
    top: 50%;
    left: 50%;
    margin: -35px 0 0 -35px;
    z-index: 3;
}

.daily-wrap .gradient {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(to bottom, rgba(0,0,0,0) 0%,rgba(0,0,0,1) 100%);
    opacity: .8;
    z-index: 1;
}

.daily-wrap .daily-information {
    position: absolute;
    left: 0;
    right: 0;
    bottom: 0px;
    padding-left: 20px;
    padding-right: 20px;
    z-index: 2;
}

.daily-wrap .daily-information .title {
    color: white;
    font-size: 26px;
    margin-bottom: 0;
    line-height: 30px;
}


.daily-wrap .daily-information .category-container {
    margin-bottom: 1rem;
}

.daily-wrap .daily-information .category-image {
    vertical-align: middle;
    width: 22px;
    height: 22px;
}

/*Provides the "Daily" style treatment of the content image, title and category for mobile*/

.mobile-wrap {
    position: relative;
    margin-left: -15px;
    margin-right: -15px;
}

.mobile-wrap .overlay {
    position: absolute;
    width: 56px;
    height: 56px;
    top: 50%;
    left: 50%;
    margin: -35px 0 0 -35px;
    z-index: 3;
}

.mobile-wrap .gradient {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(to bottom, rgba(0,0,0,0) 0%,rgba(0,0,0,1) 100%);
    opacity: .8;
    z-index: 1;
}

.mobile-wrap .mobile-information {
    position: absolute;
    left: 0;
    right: 0;
    bottom: 0px;
    padding-left: 20px;
    padding-right: 20px;
    z-index: 2;
}

.mobile-wrap .mobile-information .title {
    color: white;
    font-size: 21px;
    margin-bottom: 0;
    line-height: 25px;
}


.mobile-wrap .mobile-information .category-container {
    margin-bottom: 1rem;
}

.mobile-wrap .mobile-information .category-image {
    vertical-align: middle;
    width: 18px;
    height: 18px;
}

/*Mayo Clinic app marketing and download*/
#marketing {
    padding: 3em 0 3em 0;
    font-family: Verdana;
    font-size: 12px;
    font-weight: lighter;
}

.marketing-title {
    margin: 1em 0;
    color: #0057B8;
    font-size: 16px;
    font-weight: bold;
}

.marketing-title-mobile {
    margin-top: 1em;
    color: #0057B8;
    font-size: 16px;
    font-weight: bold;
}

.name-title {
    color: black;
    font-size: 14px;
    font-weight: bold;
}

.name-id {
    color: black;
    font-size: 12px;
}

.code-id {
    color: black;
    font-size: 24px;
    font-weight: bold;
}

.code-url {
    color: black;
    font-size: 12px;
    color: #0057B8;
    font-weight: bold;
}

/* Category-specific selectors */


.today {
    color: white;
    font-size: 14px;
    font-weight: bold;
    vertical-align: middle;
}

.today-image {
    border-bottom: 4px solid #0057B8;
}

.div-empty {
    line-height: 16px;
}