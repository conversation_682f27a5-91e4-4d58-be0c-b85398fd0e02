﻿// -----------------------------------------------------------------------
// <copyright file="INewsCenterRepository.cs" company="Mayo Clinic">
//     Copyright © Mayo Foundation. All rights reserved.
// </copyright>
// -----------------------------------------------------------------------

namespace Mayo.Mobile.Employee.Content.News.Model
{
    using System;
    using System.Collections.Generic;
    using System.Net.Http;
    using System.Text;
    using System.Threading.Tasks;
    using Mayo.Mobile.Employee.Content.Model;

    /// <summary>
    /// Initializes a new instance of the INewsCenterRepository class.
    /// </summary>
    public interface INewsCenterRepository
    {
        /// <summary>
        /// Post a comment
        /// </summary>
        /// <param name="contentId">The content identifier</param>
        /// <param name="comment">The plain text comment</param>
        /// <param name="threadId">The thread identifier</param>
        /// <param name="email">The user email</param>
        /// <param name="replyToId">The optional comment to reply to</param>
        /// <returns>The <see cref="Task"/></returns>
        Task PostCommentAsync(string contentId, string comment, string threadId, string email, string replyToId = null);

        /// <summary>
        /// 
        /// </summary>
        /// <param name="id"></param>
        /// <param name="pageNumber"></param>
        /// <returns></returns>
        Task<List<Comment>> GetCommentsAsync(string id, int? pageNumber = null);

        /// <summary>
        /// Get the user name
        /// </summary>
        /// <param name="userEmail">The enail address</param>
        /// <returns>The <see cref="Task{TResult}"/></returns>
        Task<string> GetUserAsync(string userEmail);

        /// <summary>
        /// Create a user email address
        /// </summary>
        /// <param name="userEmail">The user email address</param>
        /// <param name="displayName">The display name</param>
        /// <returns>The <see cref="Task{TResult}"/></returns>
        Task<string> CreateUserAsync(string userEmail, string displayName = null);
    }
}
