﻿// -----------------------------------------------------------------------
// <copyright file="MockIdentityService.cs" company="Mayo Clinic">
//     Copyright © Mayo Foundation. All rights reserved.
// </copyright>
// -----------------------------------------------------------------------

namespace Mayo.Mobile.Employee.Identity.Model
{
    using System;
    using System.IO;
    using System.Net;
    using System.Net.Http;
    using System.Net.Http.Headers;
    using System.Text;
    using System.Threading.Tasks;
    using Newtonsoft.Json.Linq;

    /// <summary>
    /// Initializes a new instance of the IdentityService class.
    /// </summary>
    public class MockIdentityService : IIdentityService
    {

        public MockIdentityService() { }

        /// <summary>
        /// Authenticate user credentials
        /// </summary>
        /// <param name="userName">The user name</param>
        /// <param name="password">The password</param>
        /// <returns>The <see cref="Task{TResult}"/></returns>
        public async Task<Employee.Model.Identity> LogonAsync (string userName, string password)
        {
            await Task.Delay(0);

            if (userName.Equals("mothermarymoes", StringComparison.OrdinalIgnoreCase) && password.Equals("Fr@ncis"))
            {
                return new Employee.Model.Identity
                {
                    UserId = "00000001",
                    FullName = "Moes, Mary Alfred",
                    FirstName = "Mary",
                    LastName = "Moes",
                    IsActive = true,
                    IsValidated = true,
                    Email = "<EMAIL>",
                    Id = "00000001",
                    // PasswordUpdateDate = DateTime.UtcNow.AddDays(-7).ToString("o")
                    PasswordUpdateDate = DateTime.UtcNow.AddDays(-7).ToString("MM/dd/yyyy HH:mm:ss")
                };
            }
            else
            {
                throw new IdentityException("Invalid username or password.", HttpStatusCode.BadRequest, userName: userName);
            }
        }

        /// <summary>
        /// Authenticate the user
        /// </summary>
        /// <param name="token">The access token</param>
        /// <returns>The <see cref="Task{TResult}"/></returns>
        public async Task<Employee.Model.Identity> TokenAsync(string token)
        {
            await Task.Delay(0);

            return new Employee.Model.Identity
            {
                UserId = "00000001",
                FullName = "Moes, Mary Alfred",
                FirstName = "Mary",
                LastName = "Moes",
                IsActive = true,
                IsValidated = true,
                Email = "<EMAIL>",
                Id = "00000001",
                // PasswordUpdateDate = DateTime.UtcNow.AddDays(-7).ToString("o")
                PasswordUpdateDate = DateTime.UtcNow.AddDays(-7).ToString("MM/dd/yyyy HH:mm:ss")
            };
        }

        /// <summary>
        /// Gets person photo
        /// </summary>
        /// <param name="personId">The user id</param>
        /// <returns>The <see cref="Task{TResult}"/></returns>
        public async Task<Stream> GetPhotoAsync(string personId)
        {
            await Task.Delay(0);
            return null;
        }
    }
}