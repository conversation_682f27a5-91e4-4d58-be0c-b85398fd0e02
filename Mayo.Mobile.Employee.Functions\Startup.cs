﻿using Mayo.Mobile.Application.Azure.DataAccess.Interfaces;
using Mayo.Mobile.Application.Azure.DataAccess.Models;
using Mayo.Mobile.Application.Azure.DataAccess.Repositories;
using Mayo.Mobile.Employee.Azure.DataAccess;
using Mayo.Mobile.Employee.Azure.DataAccess.Interfaces;
using Mayo.Mobile.Employee.Azure.DataAccess.Models;
using Mayo.Mobile.Employee.Azure.Search.Model;
using Mayo.Mobile.Employee.Content.Model;
using Mayo.Mobile.Employee.Content.News.Model;
using Mayo.Mobile.Employee.Content.News.Model.Interfaces;
using Mayo.Mobile.Employee.Content.News.Model.Models;
using Mayo.Mobile.Employee.Facilities.DataAccess;
using Mayo.Mobile.Employee.Facilities.DataAccess.Interfaces;
using Mayo.Mobile.Employee.Functions.Interfaces;
using Mayo.Mobile.Employee.Functions.Models;
using Mayo.Mobile.Employee.Functions.Services;
using Mayo.Mobile.Employee.Location.DataAccess;
using Mayo.Mobile.Employee.Location.DataAccess.Interfaces;
using Mayo.Mobile.Employee.Search.Model;
using Mayo.Mobile.Logging.Interfaces;
using Mayo.Mobile.Logging.Model;
using Mayo.Mobile.Logging.Model.Models;
using Mayo.Mobile.Logging.Models;
using Microsoft.Azure.Functions.Extensions.DependencyInjection;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;

[assembly: FunctionsStartup(typeof(Mayo.Mobile.Employee.Functions.Startup))]
namespace Mayo.Mobile.Employee.Functions
{
    public class Startup : FunctionsStartup
    {
        /// <summary>
        /// Configure the application
        /// </summary>
        /// <param name="builder">The <see cref="IFunctionsHostBuilder"/></param>
        public override void Configure(IFunctionsHostBuilder builder)
        {

            builder.Services.AddOptions<SplunkLoggerOptions>()
                .Configure<IConfiguration>((settings, configuration) => configuration
                .GetSection("SplunkLoggerOptions").Bind(settings));
            builder.Services.AddHttpClient<IAuditService, SplunkLogger>();

            builder.Services.AddOptions<CosmosRepoOptions>()
                .Configure<IConfiguration>((settings, configuration) => configuration
                .GetSection("CosmosRepoOptions").Bind(settings));
            builder.Services.AddSingleton<ICosmosRepository, CosmosRepository>();
            builder.Services.AddOptions<CosmosLoggerOptions>()
                .Configure<IConfiguration>((settings, configuration) => configuration
                .GetSection("CosmosLoggerOptions").Bind(settings));
            builder.Services.AddTransient<ICosmosLogger<EventWrapper>, CosmosLogger>();

            builder.Services.AddOptions<StorageAccountRepoOptions>()
                .Configure<IConfiguration>((settings, configuration) => configuration
                .GetSection("StorageAccountRepoOptions").Bind(settings));
            builder.Services.AddSingleton<ITableStorageRepository, TableStorageRepository>();
            
            builder.Services.AddSingleton<IQueueStorageRepository, QueueStorageRepository>();
           
            builder.Services.AddSingleton<IBlobStorageRepository, BlobStorageRepository>();


            builder.Services.AddSingleton<IMayoMobileLogger, MayoMobileLogger>();
            builder.Services.AddSingleton<IContentStorageRepository, ContentStorageRepository>();

            #region Facilities DI
            builder.Services.AddOptions<FacilityCosmosOptions>()
                .Configure<IConfiguration>((settings, configuration) => configuration
                .GetSection("FacilityCosmosOptions").Bind(settings));
            builder.Services.AddOptions<WorkOrderCosmosOptions>()
                .Configure<IConfiguration>((settings, configuration) => configuration
                .GetSection("WorkOrderCosmosOptions").Bind(settings));
            builder.Services.AddOptions<FacilityBlobOptions>()
                .Configure<IConfiguration>((settings, configuration) => configuration
                .GetSection("FacilityBlobOptions").Bind(settings));
            builder.Services.AddOptions<FacilityQueueOptions>()
                .Configure<IConfiguration>((settings, configuration) => configuration
                .GetSection("FacilityQueueOptions").Bind(settings));
            builder.Services.AddOptions<GoogleRepoOptions>()
                .Configure<IConfiguration>((settings, configuration) => configuration
                .GetSection("GoogleRepoOptions").Bind(settings));
            builder.Services.AddOptions<UDPOptions>()
                .Configure<IConfiguration>((settings, configuration) => configuration
                .GetSection("UDPLocationOptions").Bind(settings));
            builder.Services.AddOptions<ServiceNowRepoOptions>()
                .Configure<IConfiguration>((settings, configuration) => configuration
                .GetSection("ServiceNowOptions").Bind(settings));
            builder.Services.AddOptions<FacilityFunctionsServiceOptions>()
                .Configure<IConfiguration>((settings, configuration) => configuration
                .GetSection("FacilityFunctionsServiceOptions").Bind(settings));

            builder.Services.AddSingleton<IFacilitiesLocationRepo, FacilitiesCosmosRepo>();
            builder.Services.AddSingleton<IWorkOrderLoggingRepo, WorkOrderLoggingCosmosRepo>();
            builder.Services.AddSingleton<IWorkOrderQueueRepo, FacilitiesQueueRepo>();
            builder.Services.AddSingleton<IWorkOrderAttachmentsRepo, FacilitiesBlobRepo>();
            builder.Services.AddSingleton<IUDPLocationRepo, UDPLocationRepository>();
            builder.Services.AddHttpClient<IGoogleMapRepo, GoogleRepository>();
            builder.Services.AddHttpClient<IServiceNowRepo, ServiceNowRepository>();
            builder.Services.AddTransient<IFacilityFunctionsService, FacilityFunctionsService>();
            #endregion

            builder.Services.AddOptions<NewsContentServiceOptions>()
                .Configure<IConfiguration>((settings, configuration) => configuration
                .GetSection("NewsContentServiceOptions").Bind(settings));
            builder.Services.AddHttpClient<INewsContentService, NewsContentService>();

            builder.Services.AddOptions<AzureAdminRepoOptions>()
                .Configure<IConfiguration>((settings, configuration) => configuration
                .GetSection("AzureAdminRepoOptions").Bind(settings));
            builder.Services.AddSingleton<ISearchAdminRepository, AzureAdminRepository>();
            builder.Services.AddSingleton<ISearchAdminService, SearchAdminService>();

        }
    }
}