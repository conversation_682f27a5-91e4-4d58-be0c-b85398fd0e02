﻿//-----------------------------------------------------------------------
// <copyright file="Content.cs" company="Mayo Foundation">
//     Copyright © Mayo Foundation. All rights reserved.
// </copyright>
//-----------------------------------------------------------------------

namespace Mayo.Mobile.Employee.Azure.Search.Model
{
    using System;
    using System.Collections.Generic;
    using System.ComponentModel;
    using System.Linq;
    using System.Text;
    using System.Text.RegularExpressions;
    using System.Threading.Tasks;
    using Microsoft.Azure.Search;
    using Microsoft.Azure.Search.Models;

    /// <summary>
    /// Initializes a new instance of the Content class.
    /// </summary>
    [SerializePropertyNamesAsCamelCase]
    public class Content
    {
        /// <summary>
        /// The description
        /// </summary>
        private string description = string.Empty;

        /// <summary>
        /// The definition
        /// </summary>
        private string definition = string.Empty;

        /// <summary>
        /// Gets or sets the identifier
        /// </summary>
        [System.ComponentModel.DataAnnotations.Key]
        [IsSearchable]
        public string Id { get; set; }

        /// <summary>
        /// Gets or sets the title
        /// </summary>
        [IsSearchable, IsFilterable, IsSortable]
        public string Name { get; set; }

        /// <summary>
        /// Gets or sets the description
        /// </summary>
        [IsSearchable]
        [Analyzer(AnalyzerName.AsString.EnLucene)]
        public string Description
        {
            get
            {
                return this.description;
            }

            set
            {
                this.description = value;
                this.description = this.StripHTML(this.description);
            }
        }

        /// <summary>
        /// Gets or sets the definition
        /// </summary>
        [IsSearchable, IsFilterable, IsFacetable]
        public string Definition
        {
            get
            {
                return this.definition;
            }

            set
            {
                this.definition = value;
                this.definition = this.StripHTML(this.definition);
            }
        }

        /// <summary>
        /// Gets or sets the Category name
        /// </summary>
        [IsSearchable, IsFilterable, IsSortable, IsFacetable]
        public string CategoryName { get; set; }

        /// <summary>
        /// Gets or sets the Category identifier
        /// </summary>
        public string CategoryId { get; set; }

        /// <summary>
        /// Gets or sets the type
        /// </summary>
        [IsSearchable, IsFilterable, IsSortable, IsFacetable]
        public string Type { get; set; }

        /// <summary>
        /// Gets or sets the list of tags
        /// </summary>
        [IsSearchable, IsFilterable, IsFacetable]
        public string[] Tags { get; set; }

        /// <summary>
        /// Gets or sets the time the content was created
        /// </summary>
        public DateTimeOffset CreateDatetime { get; set; }

        /// <summary>
        /// Gets or sets the image identifier
        /// </summary>
        public string ImageId { get; set; }

        /// <summary>
        /// Strips HTML tags
        /// </summary>
        /// <param name="htmlText">HTML string</param>
        /// <returns>string without HTML tags</returns>
        private string StripHTML(string htmlText)
        {
            if (string.IsNullOrEmpty(htmlText))
            {
                return htmlText;
            }
            else
            {
                string plainText = Regex.Replace(htmlText, "<.*?>", string.Empty);
                plainText = Regex.Replace(plainText, @"[\s\r\n]+", " ").Trim();
                plainText = Regex.Replace(plainText, @"&(ndash|mdash|#8211|#8212|#x2013|#x2014);", "-");
                return plainText;
            }
        }
    }
}
