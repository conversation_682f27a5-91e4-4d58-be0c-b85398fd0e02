﻿//-----------------------------------------------------------------------
// <copyright file="SearchAdminService.cs" company="Mayo Foundation">
//     Copyright © Mayo Foundation. All rights reserved.
// </copyright>
//-----------------------------------------------------------------------

namespace Mayo.Mobile.Employee.Search.Model
{
    using System;
    using System.Collections.Concurrent;
    using System.Collections.Generic;
    using System.IO;
    using System.Linq;
    using System.Text;
    using System.Threading;
    using System.Threading.Tasks;
    using System.Threading.Tasks.Dataflow;
    using Mayo.Mobile.Employee.Azure.Search.Model;
    using Mayo.Mobile.Employee.Content.Model;

    /// <summary>
    /// Initializes a new instance of the SearchAdminService class.
    /// </summary>
    public class SearchAdminService : ISearchAdminService
    {
        /// <summary>
        /// The <see cref="ISearchAdminRepository"/>
        /// </summary>
        private readonly ISearchAdminRepository repository;

        /// <summary>
        /// The <see cref="IContentStorageRepository"/>
        /// </summary>
        private readonly IContentStorageRepository storageRepository;

        /// <summary>
        /// Initializes a new instance of the SearchAdminService class.
        /// </summary>
        /// <param name="repository">The <see cref="ISearchAdminRepository"/></param>
        /// <param name="storageRepository">The <see cref="IContentStorageRepository"/></param>
        public SearchAdminService(ISearchAdminRepository repository, IContentStorageRepository storageRepository)
        {
            this.repository = repository;
            this.storageRepository = storageRepository;
        }

        /// <summary>
        /// Load the news center content to the search index
        /// </summary>
        /// <returns>The <see cref="Task"/></returns>
        public async Task LoadNewsCenterIndexAsync()
        {
            var index = await repository.CreateIndexAsync<Mayo.Mobile.Employee.Azure.Search.Model.Content>(
                "newscentercontent",
                false,
                "contentsuggester",
                new string[] { "name", "description" },
                new string[] { "scoring1" },
                new Dictionary<string, double>[]
                {
                    new Dictionary<string, double>
                    {
                        { "name", 10.0 },
                        { "description", 9.0 },
                        { "definition", 8.0 }
                    }
                });

            var list = await this.storageRepository.GetBlobsAsync("content");

            ConcurrentBag<Mayo.Mobile.Employee.Azure.Search.Model.Content> indexList = new ConcurrentBag<Mayo.Mobile.Employee.Azure.Search.Model.Content>();
            ConcurrentBag<(string id, string Message)> errors = new ConcurrentBag<(string Id, string Message)>();

            var block = new ActionBlock<string>(
                async y =>
                {
                    try
                    {
                        var c = await storageRepository.GetBlobAsync<Mayo.Mobile.Employee.Content.Model.Content>("content", y);

                        if (c.Page != null)
                        {
                            indexList.Add(new Mayo.Mobile.Employee.Azure.Search.Model.Content
                            {
                                Id = c.Id,
                                CategoryName = c.Page.Categories.First().Name,
                                CategoryId = c.Page.Categories.First().Id,
                                Description = c.Page.Elements.Where(z => z.Type.Equals("DESCRIPTION")).Select(z => z.Value).FirstOrDefault(),
                                ImageId = c.Page.Elements.Where(z => z.Type.Equals("IMAGELISTURL")).Select(z => z.Value).FirstOrDefault(),
                                Name = c.Page.Elements.Where(z => z.Type.Equals("TITLE")).Select(z => z.Value).FirstOrDefault(),
                                Tags = null,
                                Type = c.Page.Type,
                                CreateDatetime = c.Page.Date
                            });
                        }
                    }
                    catch (Exception ex)
                    {
                        errors.Add((y, ex.Message));
                    }
                },
                new ExecutionDataflowBlockOptions { MaxDegreeOfParallelism = 10 });

            list.ForEach(async y =>
            {
                await block.SendAsync(y);
            });

            block.Complete();
            await block.Completion;

            //// Can only index 1000 at a time
            if (indexList.Count <= 1000)
            {
                await repository.UploadAsync<Mayo.Mobile.Employee.Azure.Search.Model.Content>(index.Name, indexList);
            }
            else
            {
                var sublists = indexList
                                .Select((y, i) => new { Index = i, Value = y })
                                .GroupBy(y => y.Index / 1000)
                                .Select(y => y.Select(v => v.Value).ToList())
                                .ToList();

                var b = new ActionBlock<List<Mayo.Mobile.Employee.Azure.Search.Model.Content>>(
                async y =>
                {
                    await repository.UploadAsync<Mayo.Mobile.Employee.Azure.Search.Model.Content>(index.Name, y);
                },
                new ExecutionDataflowBlockOptions { MaxDegreeOfParallelism = 10 });

                sublists.ForEach(async y =>
                {
                    await b.SendAsync(y);
                });

                b.Complete();
                await b.Completion;
            }
        }

        /// <summary>
        /// Add the content from the blob to the the search index
        /// </summary>
        /// <param name="c">The <see cref="Mayo.Mobile.Employee.Content.Model.Content"/></param>
        /// <param name="indexes">The list of indexes to add the content to</param>
        /// <returns>The <see cref="Task"/></returns>
        public async Task AddToIndexAsync(Mayo.Mobile.Employee.Content.Model.Content c, List<string> indexes)
        {
            await Task.WhenAll(indexes.Select(async x =>
            {
                await repository.UploadAsync<Mayo.Mobile.Employee.Azure.Search.Model.Content>(
                    x,
                    new List<Mayo.Mobile.Employee.Azure.Search.Model.Content>
                    {
                        new Mayo.Mobile.Employee.Azure.Search.Model.Content
                        {
                            Id = c.Id,
                            CategoryName = c.Page.Categories.First().Name,
                            CategoryId = c.Page.Categories.First().Id,
                            Description = c.Page.Elements.Where(z => z.Type.Equals("DESCRIPTION")).Select(z => z.Value).FirstOrDefault(),
                            ImageId = c.Page.Elements.Where(z => z.Type.Equals("IMAGELISTURL")).Select(z => z.Value).FirstOrDefault(),
                            Name = c.Page.Elements.Where(z => z.Type.Equals("TITLE")).Select(z => z.Value).FirstOrDefault(),
                            Tags = null,
                            Type = c.Page.Type,
                            CreateDatetime = c.Page.Date
                        }
                    });
            }));
        }
    }
}
