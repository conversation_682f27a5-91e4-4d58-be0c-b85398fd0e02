﻿using System;
using System.Collections.Generic;
using System.Text;

namespace Mayo.Mobile.Employee.Location.DataAccess.Models
{
    public class UDPLocationTableRow
    {
        public string LocationId { get; set; }
        public string LocationDescription { get; set; }
        public string SpaceTypeDescription { get; set; }
        public string CampusCode { get; set; }
        public string CampusCodeDescription { get; set; }
        public string BuildingCode { get; set; }
        public string BuildingCodeDescription { get; set; }
        public string FloorCode { get; set; }
        public string FloorCodeDescription { get; set; }
        public string RoomCode { get; set; }
        public string AddressLine1 { get; set; }
        public string AddressLine2 { get; set; }
        public string AddressLine3 { get; set; }
        public string CityCode { get; set; }
        public string StateCode { get; set; }
        public string PostalCode { get; set; }
        public string County { get; set; }

        public string GetFormattedAddress()
        {
            return $"{AddressLine1}{Environment.NewLine}{AddressLine2}{Environment.NewLine}{AddressLine3}";
        }
    }
}
