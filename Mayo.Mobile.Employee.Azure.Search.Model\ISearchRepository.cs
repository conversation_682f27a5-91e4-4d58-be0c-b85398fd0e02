﻿//-----------------------------------------------------------------------
// <copyright file="ISearchRepository.cs" company="Mayo Foundation">
//     Copyright © Mayo Foundation. All rights reserved.
// </copyright>
//-----------------------------------------------------------------------

namespace Mayo.Mobile.Employee.Azure.Search.Model
{
    using System;
    using System.Collections.Generic;
    using System.Linq;
    using System.Text;
    using System.Threading.Tasks;

    /// <summary>
    /// Initializes a new instance of the ISearchRepository class.
    /// </summary>
    public interface ISearchRepository
    {
        /// <summary>
        /// Search the content
        /// </summary>
        /// <param name="name">The name of the index to search</param>
        /// <param name="query">The text querying for</param>
        /// <param name="filter">The search filter</param>
        /// <returns>The <see cref="Task{TResult}"/></returns>
        Task<List<Mayo.Mobile.Employee.Model.SearchResult>> SearchAsync(string name, string query, string filter = null);

        /// <summary>
        /// Search the content
        /// </summary>
        /// <param name="name">The name of the index to search</param>
        /// <param name="query">The text querying for</param>
        /// <returns>The <see cref="Task{TResult}"/></returns>
        Task<List<Mayo.Mobile.Employee.Model.SearchResult>> SuggestAsync(string name, string query);

        /// <summary>
        /// This code added to correctly implement the disposable pattern.
        /// </summary>
        void Dispose();
    }
}
